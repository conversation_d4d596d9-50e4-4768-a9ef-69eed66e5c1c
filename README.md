## 工程目的
提供统一的面向PaaS的应用服务框架

## 工程结构
Maven工程结构

```bash
fs-paas-appframework
  └─fs-paas-app-api
  └─fs-paas-app-core
  └─fs-paas-app-license
  └─fs-paas-app-log
  └─fs-paas-app-metadata
  └─fs-paas-app-privilege
  └─fs-paas-app-flow
  └─fs-paas-app-web
  └─fs-paas-app-runtime
  └─fs-paas-app-fcp
  └─fs-paas-app-udobj-rest
```
模块说明
- fs-paas-app-api 核心模块API
- fs-paas-app-core 核心模块领域模型实现 
- fs-paas-app-license 对PaaS license服务的封装模块
- fs-paas-app-log 对PaaS审计日志服务封装模块
- fs-paas-app-metadata 对PaaS元数据服务的封装模块
- fs-paas-app-privilege 对PaaS权限服务的封装模块
- fs-paas-app-flow 对PaaS流程相关服务的封装模块（审批，工作流，BPM）
- fs-paas-app-web 应用框架REST服务扩展模块
- fs-paas-app-runtime 应用框架二次开发运行时模块
- fa-paas-app-fcp 应用框架fcp协议接入点 (主要是为了兼容老接口)
- s-paas-app-udobj-rest 兼容老的udobj项目中的REST服务

## 常见问题解决

### Java模块系统访问控制问题

**问题描述**：
在Java 9+版本中，可能会遇到以下错误：
```
java.lang.reflect.InaccessibleObjectException: Unable to make field protected java.lang.reflect.InvocationHandler java.lang.reflect.Proxy.h accessible: module java.base does not "opens java.lang.reflect" to unnamed module
```

**解决方案**：
在JVM启动参数中添加以下配置：
```bash
--add-opens java.base/java.lang.reflect=ALL-UNNAMED
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
```

**IDEA运行配置**：
1. 打开Run/Debug Configurations
2. 在VM options中添加上述参数
3. 重新启动应用

**Maven配置**：
在`pom.xml`的`maven-surefire-plugin`中添加：
```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-surefire-plugin</artifactId>
    <configuration>
        <argLine>
            --add-opens java.base/java.lang.reflect=ALL-UNNAMED
            --add-opens java.base/java.lang=ALL-UNNAMED
            --add-opens java.base/java.util=ALL-UNNAMED
        </argLine>
    </configuration>
</plugin>
```

**原因分析**：
此问题是由于MyBatis的`PaginationAutoMapInterceptor`拦截器在处理代理对象时，需要通过反射访问`java.lang.reflect.Proxy`的内部字段，但Java 9+的模块系统默认不允许访问`java.base`模块的内部API。






