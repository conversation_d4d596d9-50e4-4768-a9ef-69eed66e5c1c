package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.model.enums.ObjectActionInfo;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.log.BatchQueryObjectActions;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetLogModuleGroup;
import com.facishare.paas.appframework.core.predef.service.dto.log.GetTenantLogInterval;
import com.facishare.paas.appframework.log.dto.LogAnalysis;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.impl.I18nServiceImpl;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class ObjectLogServiceTest {

    private ObjectLogService objectLogService;
    private final String tenantId = "74255";
    
    @Mock
    private ServiceContext context;
    
    @Mock
    private ServiceFacade serviceFacade;

    @BeforeAll
    static void setupSpec() throws Exception {
        // 创建 mock 实例
        I18nClient i18nClient = mock(I18nClient.class);
        I18nServiceImpl i18nServiceImpl = mock(I18nServiceImpl.class);

        // 给 mock 设置返回值
        when(i18nClient.getAllLanguage()).thenReturn(Collections.emptyList());

        // 设置内部字段 impl
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl);

        // 设置 SINGLETON
        Whitebox.setInternalState(I18nClient.class, "SINGLETON", i18nClient);
    }

    @BeforeEach
    void setup() throws Exception {
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(User.systemUser(tenantId))
                .build();
        RequestContextManager.setContext(requestContext);
        objectLogService = new ObjectLogService();
        context = mock(ServiceContext.class);
        
        // 注入 serviceFacade
        Whitebox.setInternalState(objectLogService, "serviceFacade", serviceFacade);
        
        // Mock serviceFacade 方法 - 使用 lenient 避免不必要的 stubbing 错误
        ObjectDescribe mockObjectDescribe = new ObjectDescribe();
        mockObjectDescribe.setApiName("EmployeeObjectUsage");
        mockObjectDescribe.setDisplayName("员工对象使用情况");
        lenient().when(serviceFacade.findObject(anyString(), anyString())).thenReturn(mockObjectDescribe);
        lenient().when(serviceFacade.findDescribeListWithoutFields(anyString(), any())).thenReturn(Collections.emptyList());
        lenient().when(serviceFacade.findObjectsByTenantId(anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), anyString())).thenReturn(Collections.emptyList());
        lenient().when(serviceFacade.findTenantConfig(any(User.class), anyString())).thenReturn(null);
        lenient().when(serviceFacade.findDescribeList(anyString(), anyBoolean(), anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyString())).thenReturn(Collections.emptyList());
    }

    @Test
    void testGetLogModuleGroup() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "getLogModuleGroup");
        GetLogModuleGroup.Arg arg = new GetLogModuleGroup.Arg();
        
        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> objectLogService.getLogModuleGroup(arg, context));
    }

    @Test
    void testUpdateLogAnalysis() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "updateLogAnalysis");
        List<LogAnalysis.OperationLog> operationLogList = new ArrayList<>();
        LogAnalysis.OperationLog operationLog = new LogAnalysis.OperationLog();
        operationLog.setDescribeApiName("AccountObj");
        operationLog.setOperation(new ArrayList<String>() {{ add("1"); add("2"); }});
        operationLogList.add(operationLog);
        LogAnalysis.Arg arg = new LogAnalysis.Arg();
        arg.setLoginLog(true);
        arg.setOperationLog(true);
        arg.setOperationLogArgs(operationLogList);
        
        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> objectLogService.updateLogAnalysis(arg, context));
    }

    @Test
    void testFindLogAnalysis() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "findLogAnalysis");
        LogAnalysis.Arg arg = new LogAnalysis.Arg();
        
        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> objectLogService.findLogAnalysis(arg, context));
    }

    @Test
    void testFindOperationLogRelationship() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "findOperationLogRelationship");
        LogAnalysis.Arg arg = new LogAnalysis.Arg();
        
        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> objectLogService.findOperationLogRelationship(arg, context));
    }
}
