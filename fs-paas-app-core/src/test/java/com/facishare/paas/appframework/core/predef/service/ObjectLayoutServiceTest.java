package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.service.dto.layout.*;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.ApplicationLayeredLogicService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ReferenceLogicService;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.fieldalign.GlobalFieldAlignService;
import com.facishare.paas.appframework.metadata.fieldextra.FieldBackgroundExtraLogicService;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.layout.factory.IComponentFactoryManager;
import com.facishare.paas.appframework.metadata.layout.factory.ListComponentFactory;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeExtService;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ObjectLayoutServiceTest {

    @Mock
    private LayoutLogicService layoutLogicService;
    
    @Mock
    private ServiceFacade serviceFacade;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private ConfigService configService;
    
    @Mock
    private RedissonService redissonService;
    
    @Mock
    private GlobalFieldAlignService fieldAlignService;
    
    @Mock
    private ListComponentFactory listComponentFactory;
    
    @Mock
    private ReferenceLogicService referenceLogicService;
    
    @Mock
    private LogService logService;
    
    @Mock
    private FieldBackgroundExtraLogicService fieldBackgroundExtraLogicService;
    
    @Mock
    private ChangeOrderLogicService changeOrderLogicService;
    
    @Mock
    private IComponentFactoryManager componentFactoryManager;
    
    @Mock
    private ApplicationLayeredLogicService applicationLayeredLogicService;
    
    @Mock
    private FunctionPrivilegeService functionPrivilegeService;
    
    @Mock
    private IObjectDescribeExtService describeExtService;
    
    @InjectMocks
    private ObjectLayoutService objectLayoutService;
    
    private ServiceContext serviceContext;
    private IObjectDescribe realDescribe;
    private ILayout realLayout;

    @BeforeEach
    void setUp() {
        User mockUser = new User("test-tenant", "test-user");
        RequestContext requestContext = RequestContext.builder()
                .tenantId("test-tenant")
                .user(mockUser)
                .build();
        serviceContext = new ServiceContext(requestContext, "testService", "testMethod");

        // 使用真实的对象而不是 Mock 对象来避免类型转换问题
        realDescribe = new ObjectDescribe();
        realDescribe.setApiName("test_object");

        realLayout = new Layout();
        realLayout.setName("test_layout");
        realLayout.setDisplayName("Test Layout");
        realLayout.set("is_default", true);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建布局功能，验证正常创建布局的流程
     */
    @Test
    @DisplayName("测试创建布局 - 正常情况")
    void testCreateLayout_Success() {
        // Arrange
        CreateLayout.Arg arg = new CreateLayout.Arg();
        arg.setLayoutData(new LayoutDocument());
        arg.setAppId("test-app");
        arg.setSkipValidate(false);

        when(layoutLogicService.createLayout(any(LayoutLogicService.LayoutContext.class), any(ILayout.class), eq(true)))
                .thenReturn(realLayout);

        // Act
        CreateLayout.Result result = objectLayoutService.createLayout(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getLayout());
        verify(layoutLogicService).createLayout(any(LayoutLogicService.LayoutContext.class), any(ILayout.class), eq(true));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建布局功能，验证跳过验证的情况
     */
    @Test
    @DisplayName("测试创建布局 - 跳过验证")
    void testCreateLayout_SkipValidate() {
        // Arrange
        CreateLayout.Arg arg = new CreateLayout.Arg();
        arg.setLayoutData(new LayoutDocument());
        arg.setAppId("test-app");
        arg.setSkipValidate(true);

        when(layoutLogicService.createLayout(any(LayoutLogicService.LayoutContext.class), any(ILayout.class), eq(false)))
                .thenReturn(realLayout);

        // Act
        CreateLayout.Result result = objectLayoutService.createLayout(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(layoutLogicService).createLayout(any(LayoutLogicService.LayoutContext.class), any(ILayout.class), eq(false));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新布局功能，验证正常更新布局的流程
     */
    @Test
    @DisplayName("测试更新布局 - 正常情况")
    void testUpdateLayout_Success() {
        // Arrange
        UpdateLayout.Arg arg = new UpdateLayout.Arg();
        arg.setLayoutData("{\"name\":\"updated_layout\"}");
        arg.setSkipValidate(false);

        when(layoutLogicService.updateLayout(any(User.class), any(ILayout.class), eq(true)))
                .thenReturn(realLayout);

        // Act
        UpdateLayout.Result result = objectLayoutService.updateLayout(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getLayout());
        verify(layoutLogicService).updateLayout(any(User.class), any(ILayout.class), eq(true));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新布局功能，验证跳过验证的情况
     */
    @Test
    @DisplayName("测试更新布局 - 跳过验证")
    void testUpdateLayout_SkipValidate() {
        // Arrange
        UpdateLayout.Arg arg = new UpdateLayout.Arg();
        arg.setLayoutData("{\"name\":\"updated_layout\"}");
        arg.setSkipValidate(true);

        when(layoutLogicService.updateLayout(any(User.class), any(ILayout.class), eq(false)))
                .thenReturn(realLayout);

        // Act
        UpdateLayout.Result result = objectLayoutService.updateLayout(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(layoutLogicService).updateLayout(any(User.class), any(ILayout.class), eq(false));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除布局功能，验证正常删除布局的流程
     */
    @Test
    @DisplayName("测试删除布局 - 正常情况")
    void testDeleteLayout_Success() {
        // Arrange
        DeleteLayout.Arg arg = new DeleteLayout.Arg();
        arg.setLayoutId("layout-id-123");

        when(layoutLogicService.deleteLayout(any(User.class), eq("layout-id-123")))
                .thenReturn(realLayout);

        // Act
        DeleteLayout.Result result = objectLayoutService.deleteLayout(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getLayout());
        verify(layoutLogicService).deleteLayout(any(User.class), eq("layout-id-123"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找布局功能，验证正常查找布局的流程
     */
    @Test
    @DisplayName("测试查找布局 - 正常情况")
    void testFindLayout_Success() {
        // Arrange
        FindLayout.Arg arg = new FindLayout.Arg();
        arg.setObjectDescribeApiName("test_object");
        arg.setApiName("test_layout");

        when(describeLogicService.findObject(eq("test-tenant"), eq("test_object")))
                .thenReturn(realDescribe);
        when(layoutLogicService.findLayoutByApiName(any(User.class), eq("test_layout"), eq("test_object")))
                .thenReturn(realLayout);

        // Act
        FindLayout.Result result = objectLayoutService.findLayout(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getLayout());
        assertNotNull(result.getObjectDescribe());
        assertNotNull(result.getObjectDescribeDraft());
        verify(describeLogicService).findObject(eq("test-tenant"), eq("test_object"));
        verify(layoutLogicService).findLayoutByApiName(any(User.class), eq("test_layout"), eq("test_object"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找移动端列表布局功能，验证正常情况
     */
    @Test
    @DisplayName("测试查找移动端列表布局 - 正常情况")
    void testFindMobileListLayout_Success() {
        // Arrange
        FindMobileListLayout.Arg arg = new FindMobileListLayout.Arg();
        arg.setObjectDescribeApiName("test_object");
        arg.setAppId("test-app");
        arg.setIncludeDescribe(false);

        // 创建一个带有 TableComponent 的布局
        Layout layoutWithTable = new Layout();
        layoutWithTable.setName("test_layout");
        layoutWithTable.setDisplayName("Test Layout");
        layoutWithTable.set("is_default", true);
        // 添加一个简单的 table component
        Map<String, Object> tableComponent = new HashMap<>();
        tableComponent.put("type", "table");
        tableComponent.put("name", "table");
        layoutWithTable.set("components", Arrays.asList(tableComponent));

        List<ILayout> realLayouts = Arrays.asList(layoutWithTable);

        when(describeLogicService.findObject(eq("test-tenant"), eq("test_object")))
                .thenReturn(realDescribe);
        when(layoutLogicService.findMobileListLayout(any(LayoutLogicService.LayoutContext.class), eq(realDescribe), eq(true)))
                .thenReturn(realLayouts);

        // Act
        FindMobileListLayout.Result result = objectLayoutService.findMobileListLayout(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getListLayout());
        assertNull(result.getObjectDescribe());
        verify(describeLogicService).findObject(eq("test-tenant"), eq("test_object"));
        verify(layoutLogicService).findMobileListLayout(any(LayoutLogicService.LayoutContext.class), eq(realDescribe), eq(true));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找移动端列表布局功能，验证包含描述的情况
     */
    @Test
    @DisplayName("测试查找移动端列表布局 - 包含描述")
    void testFindMobileListLayout_IncludeDescribe() {
        // Arrange
        FindMobileListLayout.Arg arg = new FindMobileListLayout.Arg();
        arg.setObjectDescribeApiName("test_object");
        arg.setAppId("test-app");
        arg.setIncludeDescribe(true);

        // 创建一个带有 TableComponent 的布局
        Layout layoutWithTable = new Layout();
        layoutWithTable.setName("test_layout");
        layoutWithTable.setDisplayName("Test Layout");
        layoutWithTable.set("is_default", true);
        // 添加一个简单的 table component
        Map<String, Object> tableComponent = new HashMap<>();
        tableComponent.put("type", "table");
        tableComponent.put("name", "table");
        layoutWithTable.set("components", Arrays.asList(tableComponent));

        List<ILayout> realLayouts = Arrays.asList(layoutWithTable);

        when(describeLogicService.findObject(eq("test-tenant"), eq("test_object")))
                .thenReturn(realDescribe);
        when(layoutLogicService.findMobileListLayout(any(LayoutLogicService.LayoutContext.class), eq(realDescribe), eq(true)))
                .thenReturn(realLayouts);

        // Act
        FindMobileListLayout.Result result = objectLayoutService.findMobileListLayout(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getListLayout());
        assertNotNull(result.getObjectDescribe());
        assertNotNull(result.getObjectDescribeDraft());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找详情布局列表功能，验证正常情况
     */
    @Test
    @DisplayName("测试查找详情布局列表 - 正常情况")
    void testFindDetailLayoutList_Success() {
        // Arrange
        FindDetailLayoutList.Arg arg = new FindDetailLayoutList.Arg();
        arg.setObjectDescribeApiName("test_object");
        arg.setAppId("test-app");

        List<ILayout> realLayouts = Arrays.asList(realLayout);

        when(describeLogicService.findObject(eq("test-tenant"), eq("test_object")))
                .thenReturn(realDescribe);
        when(layoutLogicService.getDetailLayouts(any(LayoutLogicService.LayoutContext.class), eq(realDescribe)))
                .thenReturn(realLayouts);

        // Act
        FindDetailLayoutList.Result result = objectLayoutService.findDetailLayoutList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getListLayout());
        assertEquals(1, result.getListLayout().size());
        verify(describeLogicService).findObject(eq("test-tenant"), eq("test_object"));
        verify(layoutLogicService).getDetailLayouts(any(LayoutLogicService.LayoutContext.class), eq(realDescribe));
    }
}