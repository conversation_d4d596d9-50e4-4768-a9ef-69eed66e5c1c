package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.predef.service.dto.data.CanAddLookUpData;
import com.facishare.paas.appframework.core.predef.service.dto.data.HasLookupFields;
import com.facishare.paas.appframework.core.predef.service.dto.data.ValidateLookupData;
import com.facishare.paas.appframework.metadata.ProductCategoryService;
import com.facishare.paas.appframework.metadata.metadatahandle.LookupDataValidator;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectLookupService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectLookupService单元测试")
class ObjectLookupServiceTest {

    @Mock
    private ServiceFacade serviceFacade;
    
    @Mock
    private ProductCategoryService productCategoryService;
    
    @Mock
    private LookupDataValidator lookupDataValidator;
    
    @InjectMocks
    private ObjectLookupService objectLookupService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "TestObj__c";
    private final String TARGET_DESCRIBE_API_NAME = "TargetObj__c";
    private final String LOOKUP_FIELD_NAME = "lookupField";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "lookup", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否可以添加查找数据成功的场景
     */
    @Test
    @DisplayName("测试canAddLookUpData成功")
    void testCanAddLookUpDataSuccess() {
        // Arrange
        CanAddLookUpData.Arg arg = new CanAddLookUpData.Arg();
        arg.setTargetDescribeApiName(TARGET_DESCRIBE_API_NAME);
        arg.setLookupFieldName(LOOKUP_FIELD_NAME);
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        when(mockDescribe.getDisplayName()).thenReturn("Target Object");
        
        when(serviceFacade.findObject(TENANT_ID, TARGET_DESCRIBE_API_NAME)).thenReturn(mockDescribe);

        // Act
        CanAddLookUpData.Result result = objectLookupService.canAddLookUpData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(serviceFacade).findObject(TENANT_ID, TARGET_DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查对象是否有查找字段成功的场景
     */
    @Test
    @DisplayName("测试hasLookupFields成功")
    void testHasLookupFieldsSuccess() {
        // Arrange
        HasLookupFields.Arg arg = new HasLookupFields.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        
        IFieldDescribe mockField1 = mock(IFieldDescribe.class);
        when(mockField1.getType()).thenReturn(IFieldType.OBJECT_REFERENCE);
        
        IFieldDescribe mockField2 = mock(IFieldDescribe.class);
        when(mockField2.getType()).thenReturn(IFieldType.TEXT);
        
        List<IFieldDescribe> mockFieldDescribes = Arrays.asList(mockField1, mockField2);
        when(mockDescribe.getFieldDescribes()).thenReturn(mockFieldDescribes);
        
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);

        // Act
        HasLookupFields.Result result = objectLookupService.hasLookupFields(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isHasLookupFields()); // 应该有查找字段
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查对象是否有查找字段时没有查找字段的场景
     */
    @Test
    @DisplayName("测试hasLookupFields没有查找字段")
    void testHasLookupFieldsWithNoLookupFields() {
        // Arrange
        HasLookupFields.Arg arg = new HasLookupFields.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        
        IFieldDescribe mockField1 = mock(IFieldDescribe.class);
        when(mockField1.getType()).thenReturn(IFieldType.TEXT);
        
        IFieldDescribe mockField2 = mock(IFieldDescribe.class);
        when(mockField2.getType()).thenReturn(IFieldType.NUMBER);
        
        List<IFieldDescribe> mockFieldDescribes = Arrays.asList(mockField1, mockField2);
        when(mockDescribe.getFieldDescribes()).thenReturn(mockFieldDescribes);
        
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);

        // Act
        HasLookupFields.Result result = objectLookupService.hasLookupFields(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isHasLookupFields()); // 应该没有查找字段
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证查找数据成功的场景
     */
    @Test
    @DisplayName("测试validateLookupData成功")
    void testValidateLookupDataSuccess() {
        // Arrange
        ValidateLookupData.Arg arg = new ValidateLookupData.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);

        // Act
        ValidateLookupData.Result result = objectLookupService.validateLookupData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查对象是否有查找字段时字段列表为空的场景
     */
    @Test
    @DisplayName("测试hasLookupFields字段列表为空")
    void testHasLookupFieldsWithEmptyFieldList() {
        // Arrange
        HasLookupFields.Arg arg = new HasLookupFields.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        when(mockDescribe.getFieldDescribes()).thenReturn(Arrays.asList());
        
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);

        // Act
        HasLookupFields.Result result = objectLookupService.hasLookupFields(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isHasLookupFields());
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查对象是否有查找字段时包含主从关系字段的场景
     */
    @Test
    @DisplayName("测试hasLookupFields包含主从关系字段")
    void testHasLookupFieldsWithMasterDetailField() {
        // Arrange
        HasLookupFields.Arg arg = new HasLookupFields.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        
        IFieldDescribe masterDetailField = mock(IFieldDescribe.class);
        when(masterDetailField.getType()).thenReturn(IFieldType.MASTER_DETAIL);
        
        IFieldDescribe textField = mock(IFieldDescribe.class);
        when(textField.getType()).thenReturn(IFieldType.TEXT);
        
        List<IFieldDescribe> mockFieldDescribes = Arrays.asList(masterDetailField, textField);
        when(mockDescribe.getFieldDescribes()).thenReturn(mockFieldDescribes);
        
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);

        // Act
        HasLookupFields.Result result = objectLookupService.hasLookupFields(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isHasLookupFields()); // 主从关系字段也算查找字段
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
    }



    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectLookupService);
        assertNotNull(serviceFacade);
        assertNotNull(productCategoryService);
        assertNotNull(lookupDataValidator);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(user, serviceContext.getRequestContext().getUser());
        assertEquals("lookup", serviceContext.getServiceName());
        assertEquals("test", serviceContext.getServiceMethod());
    }
}
