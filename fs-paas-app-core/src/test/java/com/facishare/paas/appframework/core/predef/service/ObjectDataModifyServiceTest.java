package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.data.*;
import com.facishare.paas.appframework.metadata.DataSnapshotLogicService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataMiscService;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectDataModifyService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectDataModifyService单元测试")
class ObjectDataModifyServiceTest {

    @Mock
    private MetaDataService metaDataService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private DataSnapshotLogicService dataSnapshotLogicService;
    
    @Mock
    private MetaDataMiscService metaDataMiscService;
    
    @InjectMocks
    private ObjectDataModifyService objectDataModifyService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String OBJECT_API_NAME = "test_object__c";
    private final String DATA_ID = "test_data_id";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "data", "test_method");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量更新字段成功的场景
     */
    @Test
    @DisplayName("测试批量更新字段成功")
    void testBatchUpdateByFieldsSuccess() {
        // Arrange
        BatchUpdateByFields.Arg arg = new BatchUpdateByFields.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        
        ObjectDataDocument dataDoc = new ObjectDataDocument();
        dataDoc.put("name", "test_name");
        dataDoc.put("_id", DATA_ID);
        arg.setDataList(Arrays.asList(dataDoc));
        arg.setUpdateFieldList(Arrays.asList("name"));

        // Act
        BatchUpdateByFields.Result result = objectDataModifyService.batchUpdateByFields(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(metaDataService).batchUpdateByFields(eq(user), any(List.class), eq(arg.getUpdateFieldList()));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量更新字段时数据列表为空的场景
     */
    @Test
    @DisplayName("测试批量更新字段 - 数据列表为空")
    void testBatchUpdateByFieldsWithEmptyDataList() {
        // Arrange
        BatchUpdateByFields.Arg arg = new BatchUpdateByFields.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setDataList(Collections.emptyList());
        arg.setUpdateFieldList(Arrays.asList("name"));

        // Act
        BatchUpdateByFields.Result result = objectDataModifyService.batchUpdateByFields(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(metaDataService, never()).batchUpdateByFields(any(User.class), any(List.class), any(List.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量更新字段时更新字段列表为空的场景
     */
    @Test
    @DisplayName("测试批量更新字段 - 更新字段列表为空")
    void testBatchUpdateByFieldsWithEmptyUpdateFieldList() {
        // Arrange
        BatchUpdateByFields.Arg arg = new BatchUpdateByFields.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        
        ObjectDataDocument dataDoc = new ObjectDataDocument();
        dataDoc.put("name", "test_name");
        arg.setDataList(Arrays.asList(dataDoc));
        arg.setUpdateFieldList(Collections.emptyList());

        // Act
        BatchUpdateByFields.Result result = objectDataModifyService.batchUpdateByFields(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(metaDataService, never()).batchUpdateByFields(any(User.class), any(List.class), any(List.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找数据成功的场景
     */
    @Test
    @DisplayName("测试根据ID查找数据成功")
    void testFindDataByIdSuccess() {
        // Arrange
        FindDataById.Arg arg = new FindDataById.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);

        IObjectDescribe mockDescribe = new ObjectDescribe();
        mockDescribe.setApiName(OBJECT_API_NAME);
        
        ObjectData objectData = new ObjectData();
        objectData.setId(DATA_ID);
        objectData.setTenantId(TENANT_ID);
        objectData.setDescribeApiName(OBJECT_API_NAME);

        when(describeLogicService.findObject(TENANT_ID, OBJECT_API_NAME)).thenReturn(mockDescribe);
        when(metaDataService.findObjectData(user, DATA_ID, mockDescribe)).thenReturn(objectData);

        // Act
        FindDataById.Result result = objectDataModifyService.findDataById(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getObjectData());
        verify(describeLogicService).findObject(TENANT_ID, OBJECT_API_NAME);
        verify(metaDataService).findObjectData(user, DATA_ID, mockDescribe);
        verify(metaDataService).doDataPrivilegeCheck(eq(user), any(List.class), eq(mockDescribe), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找数据时无数据权限的场景
     */
    @Test
    @DisplayName("测试根据ID查找数据 - 无数据权限")
    void testFindDataByIdWithNoPrivilege() {
        // Arrange
        FindDataById.Arg arg = new FindDataById.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);

        IObjectDescribe mockDescribe = new ObjectDescribe();
        mockDescribe.setApiName(OBJECT_API_NAME);
        
        IObjectData mockObjectData = mock(IObjectData.class);

        when(describeLogicService.findObject(TENANT_ID, OBJECT_API_NAME)).thenReturn(mockDescribe);
        when(metaDataService.findObjectData(user, DATA_ID, mockDescribe)).thenReturn(mockObjectData);
        doThrow(new ValidateException("No privilege")).when(metaDataService)
                .doDataPrivilegeCheck(eq(user), any(List.class), eq(mockDescribe), anyString());

        // Act
        FindDataById.Result result = objectDataModifyService.findDataById(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getObjectData());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据名称查找ID成功的场景
     */
    @Test
    @DisplayName("测试根据名称查找ID成功")
    void testFindIdByNameSuccess() {
        // Arrange
        FindIdByName.Arg arg = new FindIdByName.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setNameList(Arrays.asList("name1", "name2"));

        Map<String, String> expectedMap = Maps.newHashMap();
        expectedMap.put("name1", "id1");
        expectedMap.put("name2", "id2");

        when(metaDataService.findObjectIdByName(user, OBJECT_API_NAME, arg.getNameList()))
                .thenReturn(expectedMap);

        // Act
        FindIdByName.Result result = objectDataModifyService.findIdByName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(expectedMap, result.getNameIdMap());
        verify(metaDataService).findObjectIdByName(user, OBJECT_API_NAME, arg.getNameList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据名称查找ID时名称列表为空的场景
     */
    @Test
    @DisplayName("测试根据名称查找ID - 名称列表为空")
    void testFindIdByNameWithEmptyNameList() {
        // Arrange
        FindIdByName.Arg arg = new FindIdByName.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setNameList(Collections.emptyList());

        // Act
        FindIdByName.Result result = objectDataModifyService.findIdByName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getNameIdMap().isEmpty());
        verify(metaDataService, never()).findObjectIdByName(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据名称查找ID时对象API名称为空的场景
     */
    @Test
    @DisplayName("测试根据名称查找ID - 对象API名称为空")
    void testFindIdByNameWithEmptyObjectApiName() {
        // Arrange
        FindIdByName.Arg arg = new FindIdByName.Arg();
        arg.setObjectApiName("");
        arg.setNameList(Arrays.asList("name1"));

        // Act
        FindIdByName.Result result = objectDataModifyService.findIdByName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getNameIdMap().isEmpty());
        verify(metaDataService, never()).findObjectIdByName(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试审批触发成功回调成功的场景
     */
    @Test
    @DisplayName("测试审批触发成功回调成功")
    void testApprovalTriggerSuccessCallbackSuccess() {
        // Arrange
        ApprovalTriggerSuccessCallback.Arg arg = new ApprovalTriggerSuccessCallback.Arg();
        arg.setDataId(DATA_ID);
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setOtherBizId("biz_id_123");

        Map<String, Object> callbackData = Maps.newHashMap();
        callbackData.put("field1", "value1");
        arg.setCallbackData(callbackData);

        when(dataSnapshotLogicService.findAndMergeSnapshot(eq(TENANT_ID), eq(OBJECT_API_NAME), eq(DATA_ID),
                any(), eq("biz_id_123"))).thenReturn(null);

        // Act
        ApprovalTriggerSuccessCallback.Result result = objectDataModifyService.approvalTriggerSuccessCallback(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(dataSnapshotLogicService).findAndMergeSnapshot(eq(TENANT_ID), eq(OBJECT_API_NAME), eq(DATA_ID), any(), eq("biz_id_123"));
        verify(dataSnapshotLogicService).createSnapshot(eq(user), eq(OBJECT_API_NAME), eq(DATA_ID), any(ObjectDataSnapshot.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试审批触发成功回调时参数为空的异常场景
     */
    @Test
    @DisplayName("测试审批触发成功回调 - 参数为空异常")
    void testApprovalTriggerSuccessCallbackWithNullParams() {
        // Arrange
        ApprovalTriggerSuccessCallback.Arg arg = new ApprovalTriggerSuccessCallback.Arg();
        arg.setDataId("");
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setOtherBizId("biz_id_123");

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            objectDataModifyService.approvalTriggerSuccessCallback(arg, serviceContext);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试审批触发成功回调时快照已存在的场景
     */
    @Test
    @DisplayName("测试审批触发成功回调 - 快照已存在")
    void testApprovalTriggerSuccessCallbackWithExistingSnapshot() {
        // Arrange
        ApprovalTriggerSuccessCallback.Arg arg = new ApprovalTriggerSuccessCallback.Arg();
        arg.setDataId(DATA_ID);
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setOtherBizId("biz_id_123");

        ObjectDataSnapshot existingSnapshot = ObjectDataSnapshot.builder()
                .masterSnapshot(Maps.newHashMap())
                .build();
        existingSnapshot.getMasterSnapshot().put("field1", "value1");

        when(dataSnapshotLogicService.findAndMergeSnapshot(eq(TENANT_ID), eq(OBJECT_API_NAME), eq(DATA_ID),
                any(), eq("biz_id_123"))).thenReturn(existingSnapshot);

        // Act
        ApprovalTriggerSuccessCallback.Result result = objectDataModifyService.approvalTriggerSuccessCallback(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(dataSnapshotLogicService, never()).createSnapshot(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找并合并快照成功的场景
     */
    @Test
    @DisplayName("测试查找并合并快照成功")
    void testFindAndMergeSnapshotSuccess() {
        // Arrange
        FindAndMergeSnapshot.Arg arg = new FindAndMergeSnapshot.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setBiz("approval_flow");
        arg.setBizId("biz_id_123");
        arg.setFillExtendField(false);

        ObjectDataSnapshot mockSnapshot = ObjectDataSnapshot.builder()
                .masterSnapshot(Maps.newHashMap())
                .build();

        when(dataSnapshotLogicService.findAndMergeSnapshot(TENANT_ID, OBJECT_API_NAME, DATA_ID,
                "approval_flow", "biz_id_123")).thenReturn(mockSnapshot);

        // Act
        FindAndMergeSnapshot.Result result = objectDataModifyService.findAndMergeSnapshot(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(mockSnapshot, result.getSnapshot());
        verify(dataSnapshotLogicService).findAndMergeSnapshot(TENANT_ID, OBJECT_API_NAME, DATA_ID, "approval_flow", "biz_id_123");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找并合并快照时需要填充扩展字段的场景
     */
    @Test
    @DisplayName("测试查找并合并快照 - 填充扩展字段")
    void testFindAndMergeSnapshotWithFillExtendField() {
        // Arrange
        FindAndMergeSnapshot.Arg arg = new FindAndMergeSnapshot.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setBiz("approval_flow");
        arg.setBizId("biz_id_123");
        arg.setFillExtendField(true);

        Map<String, Object> masterSnapshot = Maps.newHashMap();
        masterSnapshot.put("field1", "value1");

        ObjectDataSnapshot mockSnapshot = ObjectDataSnapshot.builder()
                .masterSnapshot(masterSnapshot)
                .detailSnapshot(Maps.newHashMap())
                .build();

        IObjectDescribe mockDescribe = new ObjectDescribe();
        mockDescribe.setApiName(OBJECT_API_NAME);

        when(dataSnapshotLogicService.findAndMergeSnapshot(TENANT_ID, OBJECT_API_NAME, DATA_ID,
                "approval_flow", "biz_id_123")).thenReturn(mockSnapshot);
        when(describeLogicService.findObject(TENANT_ID, OBJECT_API_NAME)).thenReturn(mockDescribe);

        // Act
        FindAndMergeSnapshot.Result result = objectDataModifyService.findAndMergeSnapshot(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(mockSnapshot, result.getSnapshot());
        verify(describeLogicService).findObject(TENANT_ID, OBJECT_API_NAME);
        verify(metaDataMiscService).fillExtendFieldInfo(eq(mockDescribe), any(List.class), eq(user));
    }
}
