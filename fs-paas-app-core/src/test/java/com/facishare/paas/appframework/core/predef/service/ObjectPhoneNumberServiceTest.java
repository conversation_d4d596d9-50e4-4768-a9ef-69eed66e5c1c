package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.PhoneNumberService;
import com.facishare.paas.appframework.common.service.dto.QueryPhoneNumberInformation;
import com.facishare.paas.appframework.common.service.dto.QueryVerificationCode;
import com.facishare.paas.appframework.common.service.dto.CheckVerificationCode;
import com.facishare.paas.appframework.common.service.dto.SmsStatus;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectPhoneNumberService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectPhoneNumberService单元测试")
class ObjectPhoneNumberServiceTest {

    @Mock
    private PhoneNumberService phoneNumberService;
    
    @InjectMocks
    private ObjectPhoneNumberService service;
    
    private User testUser;
    private ServiceContext serviceContext;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        testUser = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(testUser)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "phone_number", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试queryPhoneNumberInfo方法，正常查询手机归属地
     */
    @Test
    @DisplayName("正常场景 - 测试queryPhoneNumberInfo方法")
    void testQueryPhoneNumberInfo_NormalCase() {
        // 准备测试数据
        QueryPhoneNumberInformation.Arg arg = new QueryPhoneNumberInformation.Arg();
        arg.setMobile("13800138000");
        
        QueryPhoneNumberInformation.Result mockResult = QueryPhoneNumberInformation.Result.builder()
                .mobile("13800138000")
                .province("北京")
                .city("北京")
                .operator("移动")
                .build();
        
        // 配置Mock行为
        when(phoneNumberService.queryPhoneNumberInfo(anyString())).thenReturn(mockResult);
        
        // 执行被测试方法
        QueryPhoneNumberInformation.Result result = service.queryPhoneNumberInfo(arg);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("13800138000", result.getMobile());
        assertEquals("北京", result.getProvince());
        assertEquals("北京", result.getCity());
        assertEquals("移动", result.getOperator());
        
        // 验证Mock交互
        verify(phoneNumberService).queryPhoneNumberInfo(eq("13800138000"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试batchQueryPhoneNumberInfo方法，正常批量查询手机归属地
     */
    @Test
    @DisplayName("正常场景 - 测试batchQueryPhoneNumberInfo方法")
    void testBatchQueryPhoneNumberInfo_NormalCase() {
        // 准备测试数据
        QueryPhoneNumberInformation.Arg arg = new QueryPhoneNumberInformation.Arg();
        arg.setMobile("13800138000,13900139000");
        
        List<QueryPhoneNumberInformation.Result> mockResults = Lists.newArrayList();
        QueryPhoneNumberInformation.Result result1 = QueryPhoneNumberInformation.Result.builder()
                .mobile("13800138000")
                .province("北京")
                .city("北京")
                .operator("移动")
                .build();
        QueryPhoneNumberInformation.Result result2 = QueryPhoneNumberInformation.Result.builder()
                .mobile("13900139000")
                .province("上海")
                .city("上海")
                .operator("联通")
                .build();
        mockResults.add(result1);
        mockResults.add(result2);
        
        // 配置Mock行为
        when(phoneNumberService.batchQueryPhoneNumberInfo(anySet())).thenReturn(mockResults);
        
        // 执行被测试方法
        List<QueryPhoneNumberInformation.Result> results = service.batchQueryPhoneNumberInfo(arg);
        
        // 验证结果
        assertNotNull(results);
        assertEquals(2, results.size());
        assertEquals("13800138000", results.get(0).getMobile());
        assertEquals("13900139000", results.get(1).getMobile());
        
        // 验证Mock交互
        verify(phoneNumberService).batchQueryPhoneNumberInfo(anySet());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkVerificationCode方法，正常校验短信验证码
     */
    @Test
    @DisplayName("正常场景 - 测试checkVerificationCode方法成功")
    void testCheckVerificationCode_Success() {
        // 准备测试数据
        CheckVerificationCode.Arg arg = new CheckVerificationCode.Arg();
        arg.setAreaCode("86");
        arg.setPhone("13800138000");
        arg.setSmsCode("123456");
        
        // 配置Mock行为
        when(phoneNumberService.verifySmsCode(any(User.class), anyString(), anyString(), anyString()))
                .thenReturn("SUCCESS");
        
        // 执行被测试方法
        CheckVerificationCode.Result result = service.checkVerificationCode(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getErrorMessage());
        
        // 验证Mock交互
        verify(phoneNumberService).verifySmsCode(eq(testUser), eq("86"), eq("13800138000"), eq("123456"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkVerificationCode方法，验证码错误场景
     */
    @Test
    @DisplayName("异常场景 - 测试checkVerificationCode方法验证码错误")
    void testCheckVerificationCode_CodeError() {
        // 准备测试数据
        CheckVerificationCode.Arg arg = new CheckVerificationCode.Arg();
        arg.setAreaCode("86");
        arg.setPhone("13800138000");
        arg.setSmsCode("000000");
        
        // 配置Mock行为
        when(phoneNumberService.verifySmsCode(any(User.class), anyString(), anyString(), anyString()))
                .thenReturn("SMS_CODE_ERROR");
        
        // 执行被测试方法
        CheckVerificationCode.Result result = service.checkVerificationCode(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNotNull(result.getErrorMessage());
        
        // 验证Mock交互
        verify(phoneNumberService).verifySmsCode(eq(testUser), eq("86"), eq("13800138000"), eq("000000"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkSmsStatus方法，正常检测短信业务状态
     */
    @Test
    @DisplayName("正常场景 - 测试checkSmsStatus方法")
    void testCheckSmsStatus_NormalCase() {
        // 配置Mock行为
        when(phoneNumberService.checkSmsStatus(anyString())).thenReturn(true);
        
        // 执行被测试方法
        SmsStatus.Result result = service.checkSmsStatus(serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        // 验证Mock交互
        verify(phoneNumberService).checkSmsStatus(eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试checkSmsStatus方法，短信业务未开通场景
     */
    @Test
    @DisplayName("正常场景 - 测试checkSmsStatus方法未开通")
    void testCheckSmsStatus_NotEnabled() {
        // 配置Mock行为
        when(phoneNumberService.checkSmsStatus(anyString())).thenReturn(false);
        
        // 执行被测试方法
        SmsStatus.Result result = service.checkSmsStatus(serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        
        // 验证Mock交互
        verify(phoneNumberService).checkSmsStatus(eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(service);
        assertNotNull(phoneNumberService);
    }
}
