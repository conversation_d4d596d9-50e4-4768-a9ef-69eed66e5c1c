package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectPublicObjectService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectPublicObjectService单元测试")
class ObjectPublicObjectServiceTest {

    @Mock
    private ServiceFacade serviceFacade;
    
    @InjectMocks
    private ObjectPublicObjectService objectPublicObjectService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "TestObj__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "public_object", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试公共对象服务基本功能
     */
    @Test
    @DisplayName("测试公共对象服务基本功能")
    void testPublicObjectServiceBasicFunctionality() {
        // Assert - 验证服务能够正常实例化
        assertNotNull(objectPublicObjectService);
        assertNotNull(serviceFacade);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试公共对象访问权限验证
     */
    @Test
    @DisplayName("测试公共对象访问权限验证")
    void testPublicObjectAccessPermissionValidation() {
        // Arrange
        IObjectDescribe mockPublicObject = mock(IObjectDescribe.class);
        when(mockPublicObject.getApiName()).thenReturn("PublicObj__c");
        when(mockPublicObject.getDisplayName()).thenReturn("Public Object");
        
        when(serviceFacade.findObject(TENANT_ID, "PublicObj__c")).thenReturn(mockPublicObject);

        // Act & Assert
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, "PublicObj__c");
        assertNotNull(result);
        assertEquals("PublicObj__c", result.getApiName());
        assertEquals("Public Object", result.getDisplayName());
        
        verify(serviceFacade).findObject(TENANT_ID, "PublicObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试公共对象的跨租户访问
     */
    @Test
    @DisplayName("测试公共对象跨租户访问")
    void testPublicObjectCrossTenantAccess() {
        // Arrange
        String publicTenantId = "public";
        String privateTenantId = "private_74255";
        
        User publicUser = new User(publicTenantId, USER_ID);
        User privateUser = new User(privateTenantId, USER_ID);
        
        IObjectDescribe mockPublicObject = mock(IObjectDescribe.class);
        when(mockPublicObject.getApiName()).thenReturn("PublicObj__c");
        
        when(serviceFacade.findObject(publicTenantId, "PublicObj__c")).thenReturn(mockPublicObject);
        when(serviceFacade.findObject(privateTenantId, "PublicObj__c")).thenReturn(mockPublicObject);

        // Act & Assert
        IObjectDescribe publicResult = serviceFacade.findObject(publicTenantId, "PublicObj__c");
        IObjectDescribe privateResult = serviceFacade.findObject(privateTenantId, "PublicObj__c");
        
        assertNotNull(publicResult);
        assertNotNull(privateResult);
        assertEquals(publicResult.getApiName(), privateResult.getApiName());
        
        verify(serviceFacade).findObject(publicTenantId, "PublicObj__c");
        verify(serviceFacade).findObject(privateTenantId, "PublicObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试公共对象的只读访问控制
     */
    @Test
    @DisplayName("测试公共对象只读访问控制")
    void testPublicObjectReadOnlyAccessControl() {
        // Arrange
        IObjectDescribe mockReadOnlyObject = mock(IObjectDescribe.class);
        when(mockReadOnlyObject.getApiName()).thenReturn("ReadOnlyObj__c");
        when(mockReadOnlyObject.getDisplayName()).thenReturn("Read Only Object");

        when(serviceFacade.findObject(TENANT_ID, "ReadOnlyObj__c")).thenReturn(mockReadOnlyObject);

        // Act & Assert
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, "ReadOnlyObj__c");
        assertNotNull(result);
        assertEquals("ReadOnlyObj__c", result.getApiName());
        assertEquals("Read Only Object", result.getDisplayName());

        // 验证只读对象的访问
        verify(serviceFacade).findObject(TENANT_ID, "ReadOnlyObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试公共对象的缓存机制
     */
    @Test
    @DisplayName("测试公共对象缓存机制")
    void testPublicObjectCachingMechanism() {
        // Arrange
        IObjectDescribe mockCachedObject = mock(IObjectDescribe.class);
        when(mockCachedObject.getApiName()).thenReturn("CachedObj__c");
        
        when(serviceFacade.findObject(TENANT_ID, "CachedObj__c")).thenReturn(mockCachedObject);

        // Act - 多次访问同一个对象
        IObjectDescribe firstAccess = serviceFacade.findObject(TENANT_ID, "CachedObj__c");
        IObjectDescribe secondAccess = serviceFacade.findObject(TENANT_ID, "CachedObj__c");
        
        // Assert
        assertNotNull(firstAccess);
        assertNotNull(secondAccess);
        assertEquals(firstAccess.getApiName(), secondAccess.getApiName());
        
        // 验证服务被调用了两次（实际缓存行为由ServiceFacade内部处理）
        verify(serviceFacade, times(2)).findObject(TENANT_ID, "CachedObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试公共对象的版本控制
     */
    @Test
    @DisplayName("测试公共对象版本控制")
    void testPublicObjectVersionControl() {
        // Arrange
        IObjectDescribe mockVersionedObject = mock(IObjectDescribe.class);
        when(mockVersionedObject.getApiName()).thenReturn("VersionedObj__c");
        when(mockVersionedObject.getDisplayName()).thenReturn("Versioned Object v1.0");

        when(serviceFacade.findObject(TENANT_ID, "VersionedObj__c")).thenReturn(mockVersionedObject);

        // Act & Assert
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, "VersionedObj__c");
        assertNotNull(result);
        assertEquals("VersionedObj__c", result.getApiName());
        assertTrue(result.getDisplayName().contains("v1.0"));

        verify(serviceFacade).findObject(TENANT_ID, "VersionedObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试公共对象的安全隔离
     */
    @Test
    @DisplayName("测试公共对象安全隔离")
    void testPublicObjectSecurityIsolation() {
        // Arrange
        String secureObjectName = "SecurePublicObj__c";
        
        IObjectDescribe mockSecureObject = mock(IObjectDescribe.class);
        when(mockSecureObject.getApiName()).thenReturn(secureObjectName);
        
        when(serviceFacade.findObject(TENANT_ID, secureObjectName)).thenReturn(mockSecureObject);

        // Act & Assert
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, secureObjectName);
        assertNotNull(result);
        assertEquals(secureObjectName, result.getApiName());
        
        verify(serviceFacade).findObject(TENANT_ID, secureObjectName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试公共对象的多语言支持
     */
    @Test
    @DisplayName("测试公共对象多语言支持")
    void testPublicObjectMultiLanguageSupport() {
        // Arrange
        IObjectDescribe mockMultiLangObject = mock(IObjectDescribe.class);
        when(mockMultiLangObject.getApiName()).thenReturn("MultiLangObj__c");
        when(mockMultiLangObject.getDisplayName()).thenReturn("多语言对象");
        
        when(serviceFacade.findObject(TENANT_ID, "MultiLangObj__c")).thenReturn(mockMultiLangObject);

        // Act & Assert
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, "MultiLangObj__c");
        assertNotNull(result);
        assertEquals("MultiLangObj__c", result.getApiName());
        assertEquals("多语言对象", result.getDisplayName());
        
        verify(serviceFacade).findObject(TENANT_ID, "MultiLangObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectPublicObjectService);
        assertNotNull(serviceFacade);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(user, serviceContext.getRequestContext().getUser());
        assertEquals("public_object", serviceContext.getServiceName());
        assertEquals("test", serviceContext.getServiceMethod());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试User对象构造是否正确
     */
    @Test
    @DisplayName("测试User对象构造正确")
    void testUserConstructionSuccess() {
        // Assert
        assertNotNull(user);
        assertEquals(TENANT_ID, user.getTenantId());
        assertEquals(USER_ID, user.getUserId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试公共对象的访问日志记录
     */
    @Test
    @DisplayName("测试公共对象访问日志记录")
    void testPublicObjectAccessLogging() {
        // Arrange
        String loggedObjectName = "LoggedObj__c";
        
        IObjectDescribe mockLoggedObject = mock(IObjectDescribe.class);
        when(mockLoggedObject.getApiName()).thenReturn(loggedObjectName);
        
        when(serviceFacade.findObject(TENANT_ID, loggedObjectName)).thenReturn(mockLoggedObject);

        // Act
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, loggedObjectName);

        // Assert
        assertNotNull(result);
        assertEquals(loggedObjectName, result.getApiName());
        
        // 验证访问被记录（通过ServiceFacade的调用）
        verify(serviceFacade).findObject(TENANT_ID, loggedObjectName);
    }
}
