package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.predef.service.dto.option.*;
import com.facishare.paas.appframework.metadata.options.OptionReference;
import com.facishare.paas.appframework.metadata.options.OptionSetLogicService;
import com.facishare.paas.appframework.metadata.repository.model.MtOptionSet;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectOptionSetService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectOptionSetService单元测试")
class ObjectOptionSetServiceTest {

    @Mock
    private OptionSetLogicService optionSetLogicService;
    
    @InjectMocks
    private ObjectOptionSetService objectOptionSetService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String OPTION_API_NAME = "testOption";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "options", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找所有选项集成功的场景
     */
    @Test
    @DisplayName("测试findAllOptionSet成功")
    void testFindAllOptionSetSuccess() {
        // Arrange
        FindAllOptionSet.Arg arg = new FindAllOptionSet.Arg();
        
        MtOptionSet mockOptionSet = mock(MtOptionSet.class);
        List<MtOptionSet> mockOptionSets = Arrays.asList(mockOptionSet);
        
        when(optionSetLogicService.findAll(user)).thenReturn(mockOptionSets);

        // Act
        FindAllOptionSet.Result result = objectOptionSetService.findAllOptionSet(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(optionSetLogicService).findAll(user);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找单个选项集成功的场景
     */
    @Test
    @DisplayName("测试findOptionSet成功")
    void testFindOptionSetSuccess() {
        // Arrange
        FindOptionSet.Arg arg = new FindOptionSet.Arg();
        arg.setOptionApiName(OPTION_API_NAME);

        MtOptionSet mockOptionSet = mock(MtOptionSet.class);
        when(optionSetLogicService.find(user, OPTION_API_NAME)).thenReturn(Optional.of(mockOptionSet));

        // Act
        FindOptionSet.Result result = objectOptionSetService.findOptionSet(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(optionSetLogicService).find(user, OPTION_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找单个选项集返回空的场景
     */
    @Test
    @DisplayName("测试findOptionSet返回空")
    void testFindOptionSetReturnsEmpty() {
        // Arrange
        FindOptionSet.Arg arg = new FindOptionSet.Arg();
        arg.setOptionApiName(OPTION_API_NAME);

        when(optionSetLogicService.find(user, OPTION_API_NAME)).thenReturn(Optional.empty());

        // Act
        FindOptionSet.Result result = objectOptionSetService.findOptionSet(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(optionSetLogicService).find(user, OPTION_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建选项集成功的场景
     */
    @Test
    @DisplayName("测试createOptionSet成功")
    void testCreateOptionSetSuccess() {
        // Arrange
        SaveOptionSet.Arg arg = new SaveOptionSet.Arg();
        MtOptionSet mockOption = mock(MtOptionSet.class);
        arg.setOption(mockOption);

        MtOptionSet mockOptionSet = mock(MtOptionSet.class);
        when(optionSetLogicService.create(user, mockOption)).thenReturn(mockOptionSet);

        // Act
        SaveOptionSet.Result result = objectOptionSetService.createOptionSet(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(optionSetLogicService).create(user, mockOption);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新选项集成功的场景
     */
    @Test
    @DisplayName("测试updateOptionSet成功")
    void testUpdateOptionSetSuccess() {
        // Arrange
        SaveOptionSet.Arg arg = new SaveOptionSet.Arg();
        MtOptionSet mockOption = mock(MtOptionSet.class);
        arg.setOption(mockOption);

        MtOptionSet mockOptionSet = mock(MtOptionSet.class);
        when(optionSetLogicService.update(user, mockOption, arg.onlyUpdateOptions())).thenReturn(mockOptionSet);

        // Act
        SaveOptionSet.Result result = objectOptionSetService.updateOptionSet(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(optionSetLogicService).update(user, mockOption, arg.onlyUpdateOptions());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启用选项集成功的场景
     */
    @Test
    @DisplayName("测试enableOptionSet成功")
    void testEnableOptionSetSuccess() {
        // Arrange
        FindOptionSet.Arg arg = new FindOptionSet.Arg();
        arg.setOptionApiName(OPTION_API_NAME);

        MtOptionSet mockOptionSet = mock(MtOptionSet.class);
        when(optionSetLogicService.enable(user, OPTION_API_NAME)).thenReturn(mockOptionSet);

        // Act
        FindOptionSet.Result result = objectOptionSetService.enableOptionSet(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(optionSetLogicService).enable(user, OPTION_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用选项集成功的场景
     */
    @Test
    @DisplayName("测试disableOptionSet成功")
    void testDisableOptionSetSuccess() {
        // Arrange
        FindOptionSet.Arg arg = new FindOptionSet.Arg();
        arg.setOptionApiName(OPTION_API_NAME);

        MtOptionSet mockOptionSet = mock(MtOptionSet.class);
        when(optionSetLogicService.disable(user, OPTION_API_NAME)).thenReturn(mockOptionSet);

        // Act
        FindOptionSet.Result result = objectOptionSetService.disableOptionSet(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(optionSetLogicService).disable(user, OPTION_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除选项集成功的场景
     */
    @Test
    @DisplayName("测试deletedOptionSet成功")
    void testDeletedOptionSetSuccess() {
        // Arrange
        FindOptionSet.Arg arg = new FindOptionSet.Arg();
        arg.setOptionApiName(OPTION_API_NAME);

        // Act
        FindOptionSet.Result result = objectOptionSetService.deletedOptionSet(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(optionSetLogicService).deleted(user, OPTION_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查计数成功的场景
     */
    @Test
    @DisplayName("测试checkCount成功")
    void testCheckCountSuccess() {
        // Act
        FindOptionSet.Result result = objectOptionSetService.checkCount(serviceContext);

        // Assert
        assertNotNull(result);
        verify(optionSetLogicService).checkCount(user);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试触发计算成功的场景
     */
    @Test
    @DisplayName("测试triggerCalculate成功")
    void testTriggerCalculateSuccess() {
        // Arrange
        TriggerCalculate.Arg arg = new TriggerCalculate.Arg();
        List<String> optionApiNames = Arrays.asList("option1", "option2");
        arg.setOptionApiNames(new HashSet<>(optionApiNames));
        arg.setOnlyTouch(true);

        // Act
        TriggerCalculate.Result result = objectOptionSetService.triggerCalculate(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(optionSetLogicService).asyncTriggerCalculate(user, new HashSet<>(optionApiNames), true);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找引用成功的场景
     */
    @Test
    @DisplayName("测试findReference成功")
    void testFindReferenceSuccess() {
        // Arrange
        FindReference.Arg arg = new FindReference.Arg();
        arg.setOptionApiName(OPTION_API_NAME);

        OptionReference mockOptionReference = mock(OptionReference.class);
        List<OptionReference.SimpleReference> mockReferences = Arrays.asList(mock(OptionReference.SimpleReference.class));
        when(mockOptionReference.getOptionReferences()).thenReturn(mockReferences);
        when(optionSetLogicService.findReference(user, OPTION_API_NAME)).thenReturn(mockOptionReference);

        // Act
        FindReference.Result result = objectOptionSetService.findReference(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(optionSetLogicService).findReference(user, OPTION_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectOptionSetService);
        assertNotNull(optionSetLogicService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(user, serviceContext.getRequestContext().getUser());
        assertEquals("options", serviceContext.getServiceName());
        assertEquals("test", serviceContext.getServiceMethod());
    }
}
