package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.MessagePollingService;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.brushData.*;
import com.facishare.paas.appframework.flow.ApprovalFlowService;
import com.facishare.paas.appframework.flow.WorkFlowService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.gdpr.GdprService;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.options.OptionSetLogicService;
import com.facishare.paas.appframework.metadata.relation.*;
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService;
import com.facishare.paas.appframework.metadata.treeview.TreeViewService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IUdefButtonService;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.uc.api.service.EnterpriseRemoteService;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * BrushDataService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("BrushDataService单元测试")
class BrushDataServiceTest {

    @Mock
    private MetaDataService metaDataService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private ReferenceLogicService referenceLogicService;
    
    @Mock
    private FieldRelationCalculateService fieldRelationCalculateService;
    
    @Mock
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    
    @Mock
    private JobScheduleService jobScheduleService;
    
    @Mock
    private LayoutLogicService layoutLogicService;
    
    @Mock
    private SwitchCacheService switchCacheService;
    
    @Mock
    private FunctionPrivilegeService functionPrivilegeService;
    
    @Mock
    private GDSHandler gdsHandler;
    
    @Mock
    private WorkFlowService workFlowService;
    
    @Mock
    private TreeViewService treeViewService;
    
    @Mock
    private ApprovalFlowService approvalFlowService;
    
    @Mock
    private OptionSetLogicService optionSetLogicService;
    
    @Mock
    private MaskFieldLogicService maskFieldLogicService;
    
    @Mock
    private AutoNumberLogicService autoNumberLogicService;
    
    @Mock
    private DataSnapshotLogicService dataSnapshotLogicService;
    
    @Mock
    private QuoteValueService quoteValueService;
    
    @Mock
    private IUdefButtonService buttonService;
    
    @Mock
    private MetaDataComputeService metaDataComputeService;
    
    @Mock
    private FieldRelationGraphService fieldRelationGraphService;
    
    @Mock
    private EnterpriseRemoteService enterpriseRemoteService;
    
    @Mock
    private ApplicationLayeredLogicService applicationLayeredLogicService;
    
    @Mock
    private CustomButtonServiceImpl customButtonService;
    
    @Mock
    private MessagePollingService messagePollingService;
    
    @Mock
    private ObjectMappingService objectMappingService;
    
    @Mock
    private GdprService gdprService;
    
    @Mock
    private MergeJedisCmd expressionRedisSupport;

    @InjectMocks
    private BrushDataService brushDataService;

    private ServiceContext serviceContext;
    private User user;

    private static final String TENANT_ID = "78057";
    private static final String USER_ID = "1000";
    private static final String OBJECT_API_NAME = "TestObj";
    private static final String FIELD_API_NAME = "test_field__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .user(user)
                .tenantId(TENANT_ID)
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        serviceContext = new ServiceContext(requestContext, "brush_data", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除表达式Redis值成功的场景
     */
    @Test
    @DisplayName("测试删除表达式Redis值成功")
    void testRemoveExpressionRedisValueSuccess() {
        // Arrange
        Map<String, Object> arg = Maps.newHashMap();
        List<String> keys = Lists.newArrayList("key1", "key2", "key3");
        arg.put("keys", keys);

        // Act
        Map<String, Object> result = brushDataService.removeExpressionRedisValue(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertEquals(keys, result.get("keys"));
        verify(expressionRedisSupport).del(any(String[].class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除表达式Redis值时参数为空的场景
     */
    @Test
    @DisplayName("测试删除表达式Redis值时参数为空")
    void testRemoveExpressionRedisValueEmptyArg() {
        // Arrange
        Map<String, Object> emptyArg = Maps.newHashMap();

        // Act
        Map<String, Object> result = brushDataService.removeExpressionRedisValue(serviceContext, emptyArg);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(expressionRedisSupport, never()).del(any(String[].class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除表达式Redis值时keys为空的场景
     */
    @Test
    @DisplayName("测试删除表达式Redis值时keys为空")
    void testRemoveExpressionRedisValueEmptyKeys() {
        // Arrange
        Map<String, Object> arg = Maps.newHashMap();
        arg.put("keys", Lists.newArrayList());

        // Act
        Map<String, Object> result = brushDataService.removeExpressionRedisValue(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(expressionRedisSupport, never()).del(any(String[].class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除表达式Redis值时发生异常的场景
     */
    @Test
    @DisplayName("测试删除表达式Redis值时发生异常")
    void testRemoveExpressionRedisValueThrowsException() {
        // Arrange
        Map<String, Object> arg = Maps.newHashMap();
        List<String> keys = Lists.newArrayList("key1", "key2");
        arg.put("keys", keys);
        
        doThrow(new RuntimeException("Redis连接失败")).when(expressionRedisSupport).del(any(String[].class));

        // Act & Assert
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            brushDataService.removeExpressionRedisValue(serviceContext, arg);
        });
        
        assertEquals("Redis连接失败", exception.getMessage());
        verify(expressionRedisSupport).del(any(String[].class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提交公式引用关系成功的场景（指定对象）
     */
    @Test
    @DisplayName("测试提交公式引用关系成功（指定对象）")
    void testSubmitFormulaReferenceRelationWithObjectSuccess() {
        // Arrange
        SubmitFormulaReferenceRelation.Arg arg = new SubmitFormulaReferenceRelation.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setFieldApiName(FIELD_API_NAME);

        IObjectDescribe mockDescribe = createMockObjectDescribe();
        IFieldDescribe mockField = createMockFieldDescribe();
        List<ReferenceData> mockReferenceData = createMockReferenceData();

        when(describeLogicService.findObject(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                .thenReturn(mockDescribe);
        when(mockDescribe.getFieldDescribe(eq(FIELD_API_NAME)))
                .thenReturn(mockField);
        when(fieldRelationCalculateService.checkReferenceOfFormulaField(eq(mockDescribe), anyList(), eq(false)))
                .thenReturn(mockReferenceData);

        // Act
        SubmitFormulaReferenceRelation.Result result = brushDataService.submitFormulaReferenceRelation(serviceContext, arg);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(describeLogicService).findObject(eq(TENANT_ID), eq(OBJECT_API_NAME));
        verify(fieldRelationCalculateService).checkReferenceOfFormulaField(eq(mockDescribe), anyList(), eq(false));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试提交公式引用关系时租户ID为空的场景
     */
    @Test
    @DisplayName("测试提交公式引用关系时租户ID为空")
    void testSubmitFormulaReferenceRelationEmptyTenantId() {
        // Arrange
        ServiceContext emptyTenantContext = new ServiceContext(
                RequestContext.builder().tenantId("").build(), "brush_data", "test");
        SubmitFormulaReferenceRelation.Arg arg = new SubmitFormulaReferenceRelation.Arg();

        // Act
        SubmitFormulaReferenceRelation.Result result = brushDataService.submitFormulaReferenceRelation(emptyTenantContext, arg);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(describeLogicService, never()).findObject(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查存储字段成功的场景
     */
    @Test
    @DisplayName("测试检查存储字段成功")
    void testCheckStoredFormulaFieldsSuccess() {
        // Arrange
        CheckStoredFormulaFields.Arg arg = new CheckStoredFormulaFields.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);

        IObjectDescribe mockDescribe = createMockObjectDescribe();
        when(describeLogicService.findObject(eq(TENANT_ID), eq(OBJECT_API_NAME)))
                .thenReturn(mockDescribe);

        // Act
        CheckStoredFormulaFields.Result result = brushDataService.checkStoredFormulaFields(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(describeLogicService).findObject(eq(TENANT_ID), eq(OBJECT_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查存储字段时租户ID为空抛出异常的场景
     */
    @Test
    @DisplayName("测试检查存储字段时租户ID为空抛出异常")
    void testCheckStoredFormulaFieldsThrowsValidateExceptionWhenTenantIdEmpty() {
        // Arrange
        ServiceContext emptyTenantContext = new ServiceContext(
                RequestContext.builder().tenantId("").build(), "brush_data", "test");
        CheckStoredFormulaFields.Arg arg = new CheckStoredFormulaFields.Arg();

        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            brushDataService.checkStoredFormulaFields(emptyTenantContext, arg);
        });
        
        verify(describeLogicService, never()).findObject(any(), any());
    }

    // Helper methods for creating test data
    private IObjectDescribe createMockObjectDescribe() {
        IObjectDescribe describe = mock(IObjectDescribe.class);
        lenient().when(describe.getApiName()).thenReturn(OBJECT_API_NAME);
        lenient().when(describe.getTenantId()).thenReturn(TENANT_ID);
        lenient().when(describe.getFieldDescribes()).thenReturn(Lists.newArrayList());
        return describe;
    }

    private IFieldDescribe createMockFieldDescribe() {
        IFieldDescribe field = mock(IFieldDescribe.class);
        lenient().when(field.getApiName()).thenReturn(FIELD_API_NAME);
        lenient().when(field.isIndex()).thenReturn(false);
        return field;
    }

    private List<ReferenceData> createMockReferenceData() {
        ReferenceData referenceData = ReferenceData.builder()
                .targetType("related_describe_field")
                .sourceValue("TestObj.test_field")
                .build();
        return Lists.newArrayList(referenceData);
    }
}