package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.privilege.service.RoleService;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.recordType.CreateRecordType;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.I18nSettingService;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypeRoleViewPojo;
import com.facishare.paas.appframework.metadata.fieldextra.FieldBackgroundExtraLogicService;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectRecordTypeService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectRecordTypeService单元测试")
class ObjectRecordTypeServiceTest {

    @Mock
    private RecordTypeLogicServiceImpl recordTypeService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private RoleService roleService;
    
    @Mock
    private FieldBackgroundExtraLogicService fieldBackgroundExtraLogicService;
    
    @Mock
    private I18nSettingService i18nSettingService;
    
    @InjectMocks
    private ObjectRecordTypeService objectRecordTypeService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String OBJECT_API_NAME = "test_object__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "record_type", "test_method");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型成功的场景
     */
    @Test
    @DisplayName("测试创建记录类型成功")
    void testCreateRecordTypeSuccess() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setRemark("Test remark");
        
        RecordTypeRoleViewPojo pojo = new RecordTypeRoleViewPojo();
        pojo.setApi_name("test_record_type");
        pojo.setLabel("Test Record Type");
        arg.setRecord_type(JSON.toJSONString(pojo));
        
        List<I18nInfo> i18nInfoList = Lists.newArrayList();
        I18nInfo i18nInfo = new I18nInfo();
        i18nInfo.setApiName("test_record_type");
        i18nInfo.setValue("Test Record Type EN");
        i18nInfoList.add(i18nInfo);
        arg.setI18nInfoList(i18nInfoList);

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(true);

        when(recordTypeService.createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any()))
                .thenReturn(recordTypeResult);

        // Act
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(recordTypeService).createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any());
        verify(i18nSettingService).synTranslateToMeta(TENANT_ID, OBJECT_API_NAME, i18nInfoList);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型时记录类型为空的场景
     */
    @Test
    @DisplayName("测试创建记录类型 - 记录类型为空")
    void testCreateRecordTypeWithBlankRecordType() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setRecord_type("");

        // Act
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(recordTypeService, never()).createRecordType(any(), any(), any(), any());
        verify(i18nSettingService, never()).synTranslateToMeta(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型时对象API名称为空的场景
     */
    @Test
    @DisplayName("测试创建记录类型 - 对象API名称为空")
    void testCreateRecordTypeWithBlankDescribeApiName() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName("");
        
        RecordTypeRoleViewPojo pojo = new RecordTypeRoleViewPojo();
        pojo.setApi_name("test_record_type");
        arg.setRecord_type(JSON.toJSONString(pojo));

        // Act
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(recordTypeService, never()).createRecordType(any(), any(), any(), any());
        verify(i18nSettingService, never()).synTranslateToMeta(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型失败的场景
     */
    @Test
    @DisplayName("测试创建记录类型失败")
    void testCreateRecordTypeFailure() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        
        RecordTypeRoleViewPojo pojo = new RecordTypeRoleViewPojo();
        pojo.setApi_name("test_record_type");
        pojo.setLabel("Test Record Type");
        arg.setRecord_type(JSON.toJSONString(pojo));
        arg.setI18nInfoList(Collections.emptyList());

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(false);

        when(recordTypeService.createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any()))
                .thenReturn(recordTypeResult);

        // Act
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(recordTypeService).createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any());
        verify(i18nSettingService).synTranslateToMeta(TENANT_ID, OBJECT_API_NAME, Collections.emptyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型时JSON解析异常的场景
     */
    @Test
    @DisplayName("测试创建记录类型 - JSON解析异常")
    void testCreateRecordTypeWithInvalidJson() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setRecord_type("invalid json");

        // Act & Assert
        assertThrows(Exception.class, () -> {
            objectRecordTypeService.createRecordType(arg, serviceContext);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型时包含国际化信息的场景
     */
    @Test
    @DisplayName("测试创建记录类型 - 包含国际化信息")
    void testCreateRecordTypeWithI18nInfo() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        
        RecordTypeRoleViewPojo pojo = new RecordTypeRoleViewPojo();
        pojo.setApi_name("test_record_type");
        pojo.setLabel("Test Record Type");
        arg.setRecord_type(JSON.toJSONString(pojo));
        
        List<I18nInfo> i18nInfoList = Lists.newArrayList();
        I18nInfo i18nInfo1 = new I18nInfo();
        i18nInfo1.setApiName("test_record_type_en");
        i18nInfo1.setValue("Test Record Type EN");

        I18nInfo i18nInfo2 = new I18nInfo();
        i18nInfo2.setApiName("test_record_type_zh");
        i18nInfo2.setValue("测试记录类型");
        
        i18nInfoList.add(i18nInfo1);
        i18nInfoList.add(i18nInfo2);
        arg.setI18nInfoList(i18nInfoList);

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(true);

        when(recordTypeService.createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any()))
                .thenReturn(recordTypeResult);

        // Act
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(recordTypeService).createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any());
        verify(i18nSettingService).synTranslateToMeta(TENANT_ID, OBJECT_API_NAME, i18nInfoList);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建记录类型时国际化信息为null的场景
     */
    @Test
    @DisplayName("测试创建记录类型 - 国际化信息为null")
    void testCreateRecordTypeWithNullI18nInfo() {
        // Arrange
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        
        RecordTypeRoleViewPojo pojo = new RecordTypeRoleViewPojo();
        pojo.setApi_name("test_record_type");
        pojo.setLabel("Test Record Type");
        arg.setRecord_type(JSON.toJSONString(pojo));
        arg.setI18nInfoList(null);

        RecordTypeResult recordTypeResult = new RecordTypeResult();
        recordTypeResult.setSuccess(true);

        when(recordTypeService.createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any()))
                .thenReturn(recordTypeResult);

        // Act
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result);
        verify(recordTypeService).createRecordType(eq(TENANT_ID), eq(OBJECT_API_NAME), any(RecordTypeRoleViewPojo.class), any());
        verify(i18nSettingService).synTranslateToMeta(TENANT_ID, OBJECT_API_NAME, null);
    }
}
