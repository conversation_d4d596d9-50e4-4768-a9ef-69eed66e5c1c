package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.globalvariable.*;
import com.facishare.paas.appframework.metadata.GlobalVarServiceImpl;
import com.facishare.paas.appframework.metadata.dto.GlobalVariableResult;
import com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe;
import com.facishare.paas.metadata.impl.describe.GlobalVariableDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GlobalVariableServiceTest {

    private static final String TENANT_ID = "74255";
    private static final String USER_ID = "1000";
    private static final String API_NAME = "testVariable";
    private static final String JSON_DATA = "{\"apiName\":\"testVariable\",\"label\":\"测试变量\"}";

    @Mock
    private GlobalVarServiceImpl globalVarService;
    
    @InjectMocks
    private GlobalVariableService globalVariableService;
    
    private ServiceContext serviceContext;
    private User user;

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        RequestContextManager.setContext(requestContext);
        serviceContext = new ServiceContext(requestContext, "global_variable", "test");
    }

    @AfterEach
    void tearDown() {
        RequestContextManager.removeContext();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建全局变量成功的场景
     */
    @Test
    @DisplayName("测试创建全局变量成功")
    void testCreateGlobalVariableSuccess() {
        // Arrange
        CreateGlobalVariable.Arg arg = new CreateGlobalVariable.Arg();
        arg.setJson_data(JSON_DATA);
        
        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(true);
        
        when(globalVarService.create(any(IGlobalVariableDescribe.class)))
                .thenReturn(globalVariableResult);

        // Act
        CreateGlobalVariable.Result result = globalVariableService.createGlobalVarialbe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVarService).create(any(IGlobalVariableDescribe.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建全局变量失败的场景
     */
    @Test
    @DisplayName("测试创建全局变量失败")
    void testCreateGlobalVariableFailed() {
        // Arrange
        CreateGlobalVariable.Arg arg = new CreateGlobalVariable.Arg();
        arg.setJson_data(JSON_DATA);
        
        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(false);
        
        when(globalVarService.create(any(IGlobalVariableDescribe.class)))
                .thenReturn(globalVariableResult);

        // Act
        CreateGlobalVariable.Result result = globalVariableService.createGlobalVarialbe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(globalVarService).create(any(IGlobalVariableDescribe.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新全局变量成功的场景
     */
    @Test
    @DisplayName("测试更新全局变量成功")
    void testUpdateGlobalVariableSuccess() {
        // Arrange
        UpdateGlobalVariable.Arg arg = new UpdateGlobalVariable.Arg();
        arg.setJson_data(JSON_DATA);
        
        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(true);
        
        when(globalVarService.update(any(IGlobalVariableDescribe.class)))
                .thenReturn(globalVariableResult);

        // Act
        UpdateGlobalVariable.Result result = globalVariableService.updateGlobalVariable(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVarService).update(any(IGlobalVariableDescribe.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除全局变量成功的场景
     */
    @Test
    @DisplayName("测试删除全局变量成功")
    void testDeleteGlobalVariableSuccess() {
        // Arrange
        DeleteGlobalVariable.Arg arg = new DeleteGlobalVariable.Arg();
        arg.setApiName(API_NAME);
        
        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(true);
        
        when(globalVarService.delete(eq(API_NAME), eq(TENANT_ID)))
                .thenReturn(globalVariableResult);

        // Act
        DeleteGlobalVariable.Result result = globalVariableService.deleteGloableVarialbe(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVarService).delete(eq(API_NAME), eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取全局变量列表成功的场景
     */
    @Test
    @DisplayName("测试获取全局变量列表成功")
    void testGetGlobalVariableListSuccess() {
        // Arrange
        GetGlobalVariableList.Arg arg = new GetGlobalVariableList.Arg();
        arg.setLabel("测试");
        arg.setRealTimeTrans(true);
        
        List<IGlobalVariableDescribe> variableList = Lists.newArrayList();
        IGlobalVariableDescribe variable = mock(IGlobalVariableDescribe.class);
        variableList.add(variable);
        
        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(true);
        globalVariableResult.setGlobalVariableList(variableList);
        
        when(globalVarService.findGlobalVariableList(eq("测试"), eq(true), eq(TENANT_ID)))
                .thenReturn(globalVariableResult);

        // Act
        GetGlobalVariableList.Result result = globalVariableService.getGlobalVariableList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getGlobalVariableList());
        assertEquals(1, result.getGlobalVariableList().size());
        verify(globalVarService).findGlobalVariableList(eq("测试"), eq(true), eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取全局变量详情成功的场景
     */
    @Test
    @DisplayName("测试获取全局变量详情成功")
    void testGetGlobalVariableDetailSuccess() {
        // Arrange
        GetGloableVariableDetail.Arg arg = new GetGloableVariableDetail.Arg();
        arg.setApiName(API_NAME);
        arg.setRealTimeTrans(true);

        GlobalVariableDescribe variable = new GlobalVariableDescribe();
        variable.setApiName(API_NAME);
        variable.setLabel("测试变量");

        when(globalVarService.findGlobalVariableInfo(eq(API_NAME), eq(true), isNull(), eq(TENANT_ID)))
                .thenReturn(variable);

        // Act
        GetGloableVariableDetail.Result result = globalVariableService.getGlobalVariableDetail(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVarService).findGlobalVariableInfo(eq(API_NAME), eq(true), isNull(), eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取全局变量详情指定语言成功的场景
     */
    @Test
    @DisplayName("测试获取全局变量详情指定语言成功")
    void testGetGlobalVariableDetailAssignLangSuccess() {
        // Arrange
        GetGlobalVariableDetailAssignLang.Arg arg = new GetGlobalVariableDetailAssignLang.Arg();
        arg.setApiName(API_NAME);
        arg.setRealTimeTrans(true);
        arg.setLang("zh_CN");

        GlobalVariableDescribe variable = new GlobalVariableDescribe();
        variable.setApiName(API_NAME);
        variable.setLabel("测试变量");

        when(globalVarService.findGlobalVariableInfo(eq(API_NAME), eq(true), eq("zh_CN"), eq(TENANT_ID)))
                .thenReturn(variable);

        // Act
        GetGloableVariableDetail.Result result = globalVariableService.getGlobalVariableDetailAssignLang(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVarService).findGlobalVariableInfo(eq(API_NAME), eq(true), eq("zh_CN"), eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取全局变量详情返回null的场景
     */
    @Test
    @DisplayName("测试获取全局变量详情返回null")
    void testGetGlobalVariableDetailReturnsNull() {
        // Arrange
        GetGloableVariableDetail.Arg arg = new GetGloableVariableDetail.Arg();
        arg.setApiName(API_NAME);
        arg.setRealTimeTrans(true);
        
        when(globalVarService.findGlobalVariableInfo(eq(API_NAME), eq(true), isNull(), eq(TENANT_ID)))
                .thenReturn(null);

        // Act
        GetGloableVariableDetail.Result result = globalVariableService.getGlobalVariableDetail(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVarService).findGlobalVariableInfo(eq(API_NAME), eq(true), isNull(), eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取全局变量列表返回空列表的场景
     */
    @Test
    @DisplayName("测试获取全局变量列表返回空列表")
    void testGetGlobalVariableListReturnsEmptyList() {
        // Arrange
        GetGlobalVariableList.Arg arg = new GetGlobalVariableList.Arg();
        arg.setLabel("");
        arg.setRealTimeTrans(false);
        
        GlobalVariableResult globalVariableResult = new GlobalVariableResult();
        globalVariableResult.setSuccess(true);
        globalVariableResult.setGlobalVariableList(Lists.newArrayList());
        
        when(globalVarService.findGlobalVariableList(eq(""), eq(false), eq(TENANT_ID)))
                .thenReturn(globalVariableResult);

        // Act
        GetGlobalVariableList.Result result = globalVariableService.getGlobalVariableList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getGlobalVariableList());
        assertTrue(result.getGlobalVariableList().isEmpty());
        verify(globalVarService).findGlobalVariableList(eq(""), eq(false), eq(TENANT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建全局变量参数为空的场景
     */
    @Test
    @DisplayName("测试创建全局变量参数为空")
    void testCreateGlobalVariableWithNullData() {
        // Arrange
        CreateGlobalVariable.Arg arg = new CreateGlobalVariable.Arg();
        arg.setJson_data(null);

        // Act & Assert - 期望抛出异常，因为json_data为null
        assertThrows(NullPointerException.class, () -> {
            globalVariableService.createGlobalVarialbe(arg, serviceContext);
        });
    }
}
