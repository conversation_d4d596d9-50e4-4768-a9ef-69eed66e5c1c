package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.button.service.BrushButtonDescribeService;
import com.facishare.paas.appframework.common.service.MessagePollingService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.button.*;
import com.facishare.paas.appframework.flow.FlowCommonService;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataManager;
import com.facishare.paas.appframework.metadata.config.IUdefButtonConfig;
import com.facishare.paas.appframework.metadata.dto.ButtonJobExecuteResult;
import com.facishare.paas.appframework.metadata.handler.HandlerLogicService;
import com.facishare.paas.appframework.privilege.UserDefinedButtonService;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.UdefButton;
import com.facishare.paas.metadata.service.impl.UdefFunctionService;
import com.facishare.paas.I18N;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;

import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ButtonService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ButtonService单元测试")
class ButtonServiceTest {

    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private CustomButtonServiceImpl buttonService;
    
    @Mock
    private PostActionService actionService;
    
    @Mock
    private UserDefinedButtonService userDefinedButtonService;
    
    @Mock
    private ObjectMappingService objectMappingService;
    
    @Mock
    private UdefFunctionService udefFunctionService;
    
    @Mock
    private LogService logService;
    
    @Mock
    private FunctionLogicService functionLogicService;
    
    @Mock
    private JobScheduleService jobScheduleService;
    
    @Mock
    private MessagePollingService messagePollingService;
    
    @Mock
    private BrushButtonDescribeService brushButtonDescribeService;
    
    @Mock
    private UIEventLogicService eventLogicService;
    
    @Mock
    private ButtonLogicService buttonLogicService;
    
    @Mock
    private MetaDataFindService metaDataFindService;
    
    @Mock
    private FindAndFilterButtonsByDataManager findAndFilterButtonsByDataManager;
    
    @Mock
    private HandlerLogicService handlerLogicService;
    
    @Mock
    private InfraServiceFacade infraServiceFacade;
    
    @Mock
    private ReferenceLogicService referenceLogicService;
    
    @Mock
    private FlowCommonService flowCommonService;

    @InjectMocks
    private ButtonService buttonServiceUnderTest;

    private ServiceContext serviceContext;
    private User user;

    private static final String TENANT_ID = "78057";
    private static final String USER_ID = "1000";
    private static final String OBJECT_API_NAME = "TestObj";
    private static final String BUTTON_API_NAME = "test_button__c";
    private static final String BUTTON_ID = "button123";
    private static final String DATA_ID = "data123";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .user(user)
                .tenantId(TENANT_ID)
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        serviceContext = new ServiceContext(requestContext, "button", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建按钮成功的场景
     */
    @Test
    @DisplayName("测试创建按钮成功")
    void testCreateButtonSuccess() {
        // Arrange
        CreateButton.Arg arg = new CreateButton.Arg();
        arg.setButton(createButtonJson());
        arg.setPost_actions(Lists.newArrayList());
        arg.setRoles(Lists.newArrayList("role1", "role2"));

        IUdefButton mockButton = createMockButton();
        List<IUdefButton> existingButtons = Lists.newArrayList();

        when(buttonService.findButtonList(eq(user), isNull()))
                .thenReturn(existingButtons);
        when(buttonService.createCustomButton(eq(user), any(IUdefButton.class)))
                .thenReturn(mockButton);

        // Act
        CreateButton.Result result = buttonServiceUnderTest.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(buttonService).checkCustomButtonCountLimit(eq(user), isNull(), eq(0));
        verify(buttonService).createCustomButton(eq(user), any(IUdefButton.class));
        verify(userDefinedButtonService).createUserDefinedButton(eq(user), isNull(),
                isNull(), anyString(), eq(Lists.newArrayList("role1", "role2")));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建按钮失败的场景
     */
    @Test
    @DisplayName("测试创建按钮失败")
    void testCreateButtonFailure() {
        // Arrange
        CreateButton.Arg arg = new CreateButton.Arg();
        arg.setButton(createButtonJson());
        arg.setPost_actions(Lists.newArrayList());

        List<IUdefButton> existingButtons = Lists.newArrayList();

        when(buttonService.findButtonList(eq(user), isNull()))
                .thenReturn(existingButtons);
        when(buttonService.createCustomButton(eq(user), any(IUdefButton.class)))
                .thenReturn(null); // 创建失败

        // Act
        CreateButton.Result result = buttonServiceUnderTest.create(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(buttonService).createCustomButton(eq(user), any(IUdefButton.class));
        verify(userDefinedButtonService, never()).createUserDefinedButton(any(), any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找按钮列表成功的场景
     */
    @Test
    @DisplayName("测试查找按钮列表成功")
    void testFindButtonListSuccess() {
        // Arrange
        FindButtonList.Arg arg = new FindButtonList.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);

        List<IUdefButton> mockButtons = Lists.newArrayList(createMockButton());

        when(buttonService.findButtonList(eq(user), eq(OBJECT_API_NAME), eq(true)))
                .thenReturn(mockButtons);
        when(buttonService.findButtonConfigListByApiName(eq(user), eq(OBJECT_API_NAME), anyList()))
                .thenReturn(Lists.newArrayList());

        // Act
        FindButtonList.Result result = buttonServiceUnderTest.findButtonList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getButtonList());
        assertEquals(1, result.getButtonList().size());
        verify(buttonService).findButtonList(eq(user), eq(OBJECT_API_NAME), eq(true));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找按钮信息成功的场景
     */
    @Test
    @DisplayName("测试查找按钮信息成功")
    void testFindButtonInfoSuccess() {
        // 使用MockedStatic来模拟I18N
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // Arrange
            FindButtonInfo.Arg arg = new FindButtonInfo.Arg();
            arg.setDescribeApiName(OBJECT_API_NAME);
            arg.setButtonApiName(BUTTON_API_NAME);

            IUdefButton mockButton = createMockButton();
            List<IUdefAction> mockActions = Lists.newArrayList();

            when(buttonService.findButtonByApiNameForDesigner(eq(user), eq(BUTTON_API_NAME), eq(OBJECT_API_NAME)))
                    .thenReturn(mockButton);
            when(actionService.findActionListForDesigner(eq(user), eq(mockButton), eq(OBJECT_API_NAME)))
                    .thenReturn(mockActions);

            // Mock I18N
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn("测试");

            // Act
            FindButtonInfo.Result result = buttonServiceUnderTest.findButtonInfo(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getButton());
            assertEquals(BUTTON_API_NAME, result.getButton().get("apiName"));
            verify(buttonService).findButtonByApiNameForDesigner(eq(user), eq(BUTTON_API_NAME), eq(OBJECT_API_NAME));
            verify(actionService).findActionListForDesigner(eq(user), eq(mockButton), eq(OBJECT_API_NAME));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找按钮信息时按钮不存在的场景
     */
    @Test
    @DisplayName("测试查找按钮信息时按钮不存在")
    void testFindButtonInfoNotFound() {
        // 使用MockedStatic来模拟I18N
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // Arrange
            FindButtonInfo.Arg arg = new FindButtonInfo.Arg();
            arg.setDescribeApiName(OBJECT_API_NAME);
            arg.setButtonApiName("nonexistent_button");

            when(buttonService.findButtonByApiNameForDesigner(eq(user), eq("nonexistent_button"), eq(OBJECT_API_NAME)))
                    .thenReturn(null);

            // Mock I18N - 返回null而不是字符串，避免抛出ValidateException
            mockedI18N.when(() -> I18N.text(anyString())).thenReturn(null);

            // Act & Assert - 期望抛出ValidateException
            assertThrows(ValidateException.class, () -> {
                buttonServiceUnderTest.findButtonInfo(arg, serviceContext);
            });

            verify(buttonService).findButtonByApiNameForDesigner(eq(user), eq("nonexistent_button"), eq(OBJECT_API_NAME));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试改变按钮状态成功的场景
     */
    @Test
    @DisplayName("测试改变按钮状态成功")
    void testChangeButtonStatusSuccess() {
        // Arrange
        ChangeButtonStatus.Arg arg = new ChangeButtonStatus.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setButtonApiName(BUTTON_API_NAME);
        arg.setActive(true);

        when(buttonService.updateStatus(eq(user), eq(BUTTON_API_NAME), eq(OBJECT_API_NAME), eq(true)))
                .thenReturn(true);

        // Act
        ChangeButtonStatus.Result result = buttonServiceUnderTest.changeButtonStatus(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(buttonService).updateStatus(eq(user), eq(BUTTON_API_NAME), eq(OBJECT_API_NAME), eq(true));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试启动按钮成功的场景
     */
    @Test
    @DisplayName("测试启动按钮成功")
    void testStartButtonSuccess() {
        // Arrange
        StartButton.Arg arg = new StartButton.Arg();
        arg.setObjectDataId(DATA_ID);
        arg.setArgs(Lists.newArrayList());

        // Act
        StartButton.Result result = buttonServiceUnderTest.startButton(arg, serviceContext);

        // Assert
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试校验按钮数量成功的场景
     */
    @Test
    @DisplayName("测试校验按钮数量成功")
    void testValidateButtonCountSuccess() {
        // Arrange
        ValidateButtonCount.Arg arg = new ValidateButtonCount.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);

        List<IUdefButton> mockButtons = Lists.newArrayList(createMockButton());
        
        when(buttonService.findButtonList(eq(user), eq(OBJECT_API_NAME)))
                .thenReturn(mockButtons);

        // Act
        ValidateButtonCount.Result result = buttonServiceUnderTest.validateButtonCount(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(buttonService).findButtonList(eq(user), eq(OBJECT_API_NAME));
        verify(buttonService).checkCustomButtonCountLimit(eq(user), eq(OBJECT_API_NAME), anyInt());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取按钮信息成功的场景
     */
    @Test
    @DisplayName("测试获取按钮信息成功")
    void testFetchButtonInfoSuccess() {
        // Arrange
        FetchButtonInfo.Arg arg = new FetchButtonInfo.Arg();
        arg.setLastFetchTime(0L);

        List<IUdefButton> mockButtons = Lists.newArrayList(createMockButton());

        when(buttonService.findButtonsByLastModifiedTime(eq(user), eq(0L)))
                .thenReturn(mockButtons);

        // Act
        FetchButtonInfo.Result result = buttonServiceUnderTest.fetchButtonInfo(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getButtonList());
        verify(buttonService).findButtonsByLastModifiedTime(eq(user), eq(0L));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找按钮配置成功的场景
     */
    @Test
    @DisplayName("测试查找按钮配置成功")
    void testFindButtonConfigSuccess() {
        // Arrange
        FindUdefButtonConfig.Arg arg = new FindUdefButtonConfig.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);

        IUdefButtonConfig mockConfig = mock(IUdefButtonConfig.class);
        
        when(buttonService.findButtonConfigByApiName(eq(OBJECT_API_NAME), any(), eq(user)))
                .thenReturn(mockConfig);

        // Act
        FindUdefButtonConfig.Result result = buttonServiceUnderTest.findButtonConfig(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getButtonConfig());
        verify(buttonService).findButtonConfigByApiName(eq(OBJECT_API_NAME), any(), eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找按钮作业结果成功的场景
     */
    @Test
    @DisplayName("测试查找按钮作业结果成功")
    void testFindButtonJobResultSuccess() {
        // Arrange
        FindButtonJobResult.Arg arg = new FindButtonJobResult.Arg();
        arg.setJobId("job123");

        ButtonJobExecuteResult mockJobResult = mock(ButtonJobExecuteResult.class);
        
        when(jobScheduleService.queryButtonJobExecuteResult(eq(user), any(), eq("job123")))
                .thenReturn(mockJobResult);

        // Act
        FindButtonJobResult.Result result = buttonServiceUnderTest.findButtonJobResult(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(mockJobResult, result.getJobInfo());
        verify(jobScheduleService).queryButtonJobExecuteResult(eq(user), any(), eq("job123"));
    }

    // Helper methods for creating test data
    private String createButtonJson() {
        return "{\"apiName\":\"" + BUTTON_API_NAME + "\",\"describeApiName\":\"" + OBJECT_API_NAME + "\",\"label\":\"测试按钮\",\"isActive\":true}";
    }

    private IUdefButton createMockButton() {
        // 创建一个真实的 UdefButton 对象而不是 mock
        Map<String, Object> buttonMap = new HashMap<>();
        buttonMap.put("id", BUTTON_ID);
        buttonMap.put("apiName", BUTTON_API_NAME);
        buttonMap.put("describeApiName", OBJECT_API_NAME);
        buttonMap.put("label", "测试按钮");
        buttonMap.put("isActive", true);
        buttonMap.put("actions", Lists.newArrayList());
        return new com.facishare.paas.metadata.impl.UdefButton(buttonMap);
    }
}