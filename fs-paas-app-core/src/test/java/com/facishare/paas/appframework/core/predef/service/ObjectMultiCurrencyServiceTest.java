package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.multiCurrency.*;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.metadata.MultiCurrencyLogicService;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectMultiCurrencyService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectMultiCurrencyService单元测试")
class ObjectMultiCurrencyServiceTest {

    @Mock
    private MultiCurrencyLogicService multiCurrencyLogicService;
    
    @InjectMocks
    private ObjectMultiCurrencyService service;
    
    private User testUser;
    private ServiceContext serviceContext;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        testUser = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(testUser)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "multi_currency", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试openMultiCurrency方法，正常开启多货币
     */
    @Test
    @DisplayName("正常场景 - 测试openMultiCurrency方法")
    void testOpenMultiCurrency_NormalCase() {
        // 准备测试数据
        OpenMultiCurrency.Arg arg = new OpenMultiCurrency.Arg();
        arg.setFunctionalCurrency("CNY");
        
        // 配置Mock行为
        doNothing().when(multiCurrencyLogicService).openMultiCurrency(anyString(), any(User.class));
        
        // 执行被测试方法
        OpenMultiCurrency.Result result = service.openMultiCurrency(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证Mock交互
        verify(multiCurrencyLogicService).openMultiCurrency(eq("CNY"), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试multiCurrencyStatus方法，正常查询多货币状态
     */
    @Test
    @DisplayName("正常场景 - 测试multiCurrencyStatus方法")
    void testMultiCurrencyStatus_NormalCase() {
        // 配置Mock行为
        when(multiCurrencyLogicService.findMultiCurrencyStatus(any(User.class))).thenReturn(1);
        
        // 执行被测试方法
        FindMultiCurrencyStatus.Result result = service.multiCurrencyStatus(serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getStatus());
        
        // 验证Mock交互
        verify(multiCurrencyLogicService).findMultiCurrencyStatus(eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试multiCurrencyStatus方法，用户为null的场景
     */
    @Test
    @DisplayName("异常场景 - 测试multiCurrencyStatus方法用户为null")
    void testMultiCurrencyStatus_NullUser() {
        // 准备测试数据
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(null)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        ServiceContext nullUserContext = new ServiceContext(requestContext, "multi_currency", "test");

        // 执行被测试方法并验证异常
        // 修复：用户为null时应该抛出PermissionError异常
        assertThrows(PermissionError.class, () -> {
            service.multiCurrencyStatus(nullUserContext);
        });

        // 验证Mock交互
        verify(multiCurrencyLogicService, never()).findMultiCurrencyStatus(any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试refreshCurrencyOptions方法，正常刷新货币选项
     */
    @Test
    @DisplayName("正常场景 - 测试refreshCurrencyOptions方法")
    void testRefreshCurrencyOptions_NormalCase() {
        // 准备测试数据
        RefreshCurrencyOptions.Arg arg = new RefreshCurrencyOptions.Arg();
        arg.setTenantIds(Lists.newArrayList("74255", "74256"));
        
        // 配置Mock行为
        doNothing().when(multiCurrencyLogicService).bulkRefreshCurrencyOptions(anyList());
        
        // 执行被测试方法
        RefreshCurrencyOptions.Result result = service.refreshCurrencyOptions(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证Mock交互
        verify(multiCurrencyLogicService).bulkRefreshCurrencyOptions(eq(Lists.newArrayList("74255", "74256")));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试batchModifyRate方法，正常批量编辑汇率
     */
    @Test
    @DisplayName("正常场景 - 测试batchModifyRate方法")
    void testBatchModifyRate_NormalCase() {
        // 准备测试数据
        BatchModifyRate.Arg arg = new BatchModifyRate.Arg();
        arg.setExchangeRateList(Lists.newArrayList());
        
        // 配置Mock行为
        doNothing().when(multiCurrencyLogicService).batchModifyRate(anyList(), any(User.class));
        
        // 执行被测试方法
        BatchModifyRate.Result result = service.batchModifyRate(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        
        // 验证Mock交互
        verify(multiCurrencyLogicService).batchModifyRate(anyList(), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试queryRate方法，正常分页查询汇率
     */
    @Test
    @DisplayName("正常场景 - 测试queryRate方法")
    void testQueryRate_NormalCase() {
        // 准备测试数据
        QueryRate.Arg arg = new QueryRate.Arg();
        arg.setCurrencyCode("USD");
        arg.setPageSize(10);
        arg.setPageNumber(1);
        
        // 配置Mock行为 - 修复：返回有效的QueryResult而不是null
        @SuppressWarnings("unchecked")
        com.facishare.paas.metadata.api.QueryResult mockQueryResult = mock(com.facishare.paas.metadata.api.QueryResult.class);
        when(mockQueryResult.getTotalNumber()).thenReturn(5);
        when(mockQueryResult.getData()).thenReturn(Lists.newArrayList());

        when(multiCurrencyLogicService.queryRate(anyString(), any(), any(), anyInt(), anyInt(), any(User.class)))
                .thenReturn(mockQueryResult);
        
        // 执行被测试方法
        QueryRate.Result result = service.queryRate(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getPageNumber());
        assertEquals(Integer.valueOf(10), result.getPageSize());
        
        // 验证Mock交互
        verify(multiCurrencyLogicService).queryRate(eq("USD"), any(), any(), eq(10), eq(1), eq(testUser));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(service);
        assertNotNull(multiCurrencyLogicService);
    }
}
