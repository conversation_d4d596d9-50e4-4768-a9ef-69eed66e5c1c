package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectExtensionService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectExtensionService单元测试")
class ObjectExtensionServiceTest {

    @Mock
    private ServiceFacade serviceFacade;
    
    @InjectMocks
    private ObjectExtensionService objectExtensionService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "TestObj__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "extension", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象扩展服务基本功能
     */
    @Test
    @DisplayName("测试对象扩展服务基本功能")
    void testObjectExtensionServiceBasicFunctionality() {
        // Assert - 验证服务能够正常实例化
        assertNotNull(objectExtensionService);
        assertNotNull(serviceFacade);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象字段扩展
     */
    @Test
    @DisplayName("测试对象字段扩展")
    void testObjectFieldExtension() {
        // Arrange
        IObjectDescribe mockExtendedObject = mock(IObjectDescribe.class);
        when(mockExtendedObject.getApiName()).thenReturn("ExtendedObj__c");
        when(mockExtendedObject.getDisplayName()).thenReturn("Extended Object");
        
        IFieldDescribe mockExtensionField = mock(IFieldDescribe.class);
        when(mockExtensionField.getApiName()).thenReturn("extensionField__c");
        // IFieldDescribe没有getDisplayName方法，删除这行
        
        List<IFieldDescribe> mockFields = Arrays.asList(mockExtensionField);
        when(mockExtendedObject.getFieldDescribes()).thenReturn(mockFields);
        
        when(serviceFacade.findObject(TENANT_ID, "ExtendedObj__c")).thenReturn(mockExtendedObject);

        // Act & Assert
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, "ExtendedObj__c");
        assertNotNull(result);
        assertEquals("ExtendedObj__c", result.getApiName());
        assertEquals("Extended Object", result.getDisplayName());
        assertNotNull(result.getFieldDescribes());
        assertEquals(1, result.getFieldDescribes().size());
        assertEquals("extensionField__c", result.getFieldDescribes().get(0).getApiName());
        
        verify(serviceFacade).findObject(TENANT_ID, "ExtendedObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象行为扩展
     */
    @Test
    @DisplayName("测试对象行为扩展")
    void testObjectBehaviorExtension() {
        // Arrange
        IObjectDescribe mockBehaviorObject = mock(IObjectDescribe.class);
        when(mockBehaviorObject.getApiName()).thenReturn("BehaviorObj__c");
        when(mockBehaviorObject.getDisplayName()).thenReturn("Behavior Extended Object");
        
        when(serviceFacade.findObject(TENANT_ID, "BehaviorObj__c")).thenReturn(mockBehaviorObject);

        // Act & Assert
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, "BehaviorObj__c");
        assertNotNull(result);
        assertEquals("BehaviorObj__c", result.getApiName());
        assertEquals("Behavior Extended Object", result.getDisplayName());
        
        verify(serviceFacade).findObject(TENANT_ID, "BehaviorObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象关系扩展
     */
    @Test
    @DisplayName("测试对象关系扩展")
    void testObjectRelationshipExtension() {
        // Arrange
        IObjectDescribe mockParentObject = mock(IObjectDescribe.class);
        when(mockParentObject.getApiName()).thenReturn("ParentObj__c");

        IObjectDescribe mockChildObject = mock(IObjectDescribe.class);
        when(mockChildObject.getApiName()).thenReturn("ChildObj__c");

        when(serviceFacade.findObject(TENANT_ID, "ParentObj__c")).thenReturn(mockParentObject);
        when(serviceFacade.findObject(TENANT_ID, "ChildObj__c")).thenReturn(mockChildObject);

        // Act & Assert
        IObjectDescribe parentResult = serviceFacade.findObject(TENANT_ID, "ParentObj__c");
        IObjectDescribe childResult = serviceFacade.findObject(TENANT_ID, "ChildObj__c");

        assertNotNull(parentResult);
        assertNotNull(childResult);
        assertEquals("ParentObj__c", parentResult.getApiName());
        assertEquals("ChildObj__c", childResult.getApiName());

        verify(serviceFacade).findObject(TENANT_ID, "ParentObj__c");
        verify(serviceFacade).findObject(TENANT_ID, "ChildObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据扩展
     */
    @Test
    @DisplayName("测试对象数据扩展")
    void testObjectDataExtension() {
        // Arrange
        IObjectData mockExtendedData = mock(IObjectData.class);
        when(mockExtendedData.getId()).thenReturn("extended_data_1");
        when(mockExtendedData.get(eq("name"), eq(String.class))).thenReturn("Extended Data");
        when(mockExtendedData.get(eq("extensionValue"), eq(String.class))).thenReturn("Extension Value");
        when(mockExtendedData.get(eq("customField__c"), eq(String.class))).thenReturn("Custom Field Value");

        // Act & Assert
        assertNotNull(mockExtendedData);
        assertEquals("extended_data_1", mockExtendedData.getId());
        assertEquals("Extended Data", mockExtendedData.get("name", String.class));
        assertEquals("Extension Value", mockExtendedData.get("extensionValue", String.class));
        assertEquals("Custom Field Value", mockExtendedData.get("customField__c", String.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象权限扩展
     */
    @Test
    @DisplayName("测试对象权限扩展")
    void testObjectPermissionExtension() {
        // Arrange
        IObjectDescribe mockPermissionObject = mock(IObjectDescribe.class);
        when(mockPermissionObject.getApiName()).thenReturn("PermissionObj__c");

        when(serviceFacade.findObject(TENANT_ID, "PermissionObj__c")).thenReturn(mockPermissionObject);

        // Act & Assert
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, "PermissionObj__c");
        assertNotNull(result);
        assertEquals("PermissionObj__c", result.getApiName());

        verify(serviceFacade).findObject(TENANT_ID, "PermissionObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象验证规则扩展
     */
    @Test
    @DisplayName("测试对象验证规则扩展")
    void testObjectValidationRuleExtension() {
        // Arrange
        IObjectDescribe mockValidationObject = mock(IObjectDescribe.class);
        when(mockValidationObject.getApiName()).thenReturn("ValidationObj__c");
        when(mockValidationObject.getDisplayName()).thenReturn("Validation Extended Object");
        
        when(serviceFacade.findObject(TENANT_ID, "ValidationObj__c")).thenReturn(mockValidationObject);

        // Act & Assert
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, "ValidationObj__c");
        assertNotNull(result);
        assertEquals("ValidationObj__c", result.getApiName());
        assertEquals("Validation Extended Object", result.getDisplayName());
        
        verify(serviceFacade).findObject(TENANT_ID, "ValidationObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象工作流扩展
     */
    @Test
    @DisplayName("测试对象工作流扩展")
    void testObjectWorkflowExtension() {
        // Arrange
        IObjectDescribe mockWorkflowObject = mock(IObjectDescribe.class);
        when(mockWorkflowObject.getApiName()).thenReturn("WorkflowObj__c");
        when(mockWorkflowObject.getDisplayName()).thenReturn("Workflow Extended Object");
        
        when(serviceFacade.findObject(TENANT_ID, "WorkflowObj__c")).thenReturn(mockWorkflowObject);

        // Act & Assert
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, "WorkflowObj__c");
        assertNotNull(result);
        assertEquals("WorkflowObj__c", result.getApiName());
        assertEquals("Workflow Extended Object", result.getDisplayName());
        
        verify(serviceFacade).findObject(TENANT_ID, "WorkflowObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象触发器扩展
     */
    @Test
    @DisplayName("测试对象触发器扩展")
    void testObjectTriggerExtension() {
        // Arrange
        IObjectDescribe mockTriggerObject = mock(IObjectDescribe.class);
        when(mockTriggerObject.getApiName()).thenReturn("TriggerObj__c");
        when(mockTriggerObject.getDisplayName()).thenReturn("Trigger Extended Object");
        
        when(serviceFacade.findObject(TENANT_ID, "TriggerObj__c")).thenReturn(mockTriggerObject);

        // Act & Assert
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, "TriggerObj__c");
        assertNotNull(result);
        assertEquals("TriggerObj__c", result.getApiName());
        assertEquals("Trigger Extended Object", result.getDisplayName());
        
        verify(serviceFacade).findObject(TENANT_ID, "TriggerObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象API扩展
     */
    @Test
    @DisplayName("测试对象API扩展")
    void testObjectAPIExtension() {
        // Arrange
        IObjectDescribe mockAPIObject = mock(IObjectDescribe.class);
        when(mockAPIObject.getApiName()).thenReturn("APIObj__c");
        when(mockAPIObject.getDisplayName()).thenReturn("API Extended Object");
        
        when(serviceFacade.findObject(TENANT_ID, "APIObj__c")).thenReturn(mockAPIObject);

        // Act & Assert
        IObjectDescribe result = serviceFacade.findObject(TENANT_ID, "APIObj__c");
        assertNotNull(result);
        assertEquals("APIObj__c", result.getApiName());
        assertEquals("API Extended Object", result.getDisplayName());
        
        verify(serviceFacade).findObject(TENANT_ID, "APIObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象多租户扩展
     */
    @Test
    @DisplayName("测试对象多租户扩展")
    void testObjectMultiTenantExtension() {
        // Arrange
        String tenant1 = "tenant1";
        String tenant2 = "tenant2";
        
        IObjectDescribe mockTenant1Object = mock(IObjectDescribe.class);
        when(mockTenant1Object.getApiName()).thenReturn("MultiTenantObj__c");
        when(mockTenant1Object.getDisplayName()).thenReturn("Multi-Tenant Object for Tenant 1");
        
        IObjectDescribe mockTenant2Object = mock(IObjectDescribe.class);
        when(mockTenant2Object.getApiName()).thenReturn("MultiTenantObj__c");
        when(mockTenant2Object.getDisplayName()).thenReturn("Multi-Tenant Object for Tenant 2");
        
        when(serviceFacade.findObject(tenant1, "MultiTenantObj__c")).thenReturn(mockTenant1Object);
        when(serviceFacade.findObject(tenant2, "MultiTenantObj__c")).thenReturn(mockTenant2Object);

        // Act & Assert
        IObjectDescribe result1 = serviceFacade.findObject(tenant1, "MultiTenantObj__c");
        IObjectDescribe result2 = serviceFacade.findObject(tenant2, "MultiTenantObj__c");
        
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals("MultiTenantObj__c", result1.getApiName());
        assertEquals("MultiTenantObj__c", result2.getApiName());
        assertTrue(result1.getDisplayName().contains("Tenant 1"));
        assertTrue(result2.getDisplayName().contains("Tenant 2"));
        
        verify(serviceFacade).findObject(tenant1, "MultiTenantObj__c");
        verify(serviceFacade).findObject(tenant2, "MultiTenantObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectExtensionService);
        assertNotNull(serviceFacade);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(user, serviceContext.getRequestContext().getUser());
        assertEquals("extension", serviceContext.getServiceName());
        assertEquals("test", serviceContext.getServiceMethod());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试User对象构造是否正确
     */
    @Test
    @DisplayName("测试User对象构造正确")
    void testUserConstructionSuccess() {
        // Assert
        assertNotNull(user);
        assertEquals(TENANT_ID, user.getTenantId());
        assertEquals(USER_ID, user.getUserId());
    }
}
