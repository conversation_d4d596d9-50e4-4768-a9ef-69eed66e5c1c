package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.predef.service.dto.datasync.SyncField;
import com.facishare.paas.appframework.common.service.ManageGroupService;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.mtresource.IMtResourceService;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GenerateByAI
 * ObjectDataSyncService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectDataSyncService单元测试")
class ObjectDataSyncServiceJunitTest {

    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private LicenseService licenseService;
    
    @Mock
    private IMtResourceService mtResourceService;
    
    @Mock
    private ManageGroupService manageGroupService;
    
    @InjectMocks
    private ObjectDataSyncService objectDataSyncService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "TestObj__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "sync", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试基本的Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectDataSyncService);
        assertNotNull(describeLogicService);
        assertNotNull(licenseService);
        assertNotNull(mtResourceService);
        assertNotNull(manageGroupService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试SyncField.Arg的基本构造
     */
    @Test
    @DisplayName("测试SyncField.Arg构造")
    void testSyncFieldArgConstruction() {
        // Arrange & Act
        SyncField.Arg arg = new SyncField.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTenantIds(Sets.newHashSet("target1", "target2"));
        arg.setFieldApiNames(Sets.newHashSet("field1", "field2"));
        arg.setLockDownstram(false);

        // Assert
        assertNotNull(arg);
        assertEquals(DESCRIBE_API_NAME, arg.getDescribeApiName());
        assertEquals(2, arg.getTenantIds().size());
        assertEquals(2, arg.getFieldApiNames().size());
        assertFalse(arg.isLockDownstram());
        assertTrue(arg.getTenantIds().contains("target1"));
        assertTrue(arg.getTenantIds().contains("target2"));
        assertTrue(arg.getFieldApiNames().contains("field1"));
        assertTrue(arg.getFieldApiNames().contains("field2"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试SyncField.Result的基本构造
     */
    @Test
    @DisplayName("测试SyncField.Result构造")
    void testSyncFieldResultConstruction() {
        // Arrange & Act
        SyncField.Result result = SyncField.Result.builder()
                .errorInfo(null)
                .build();

        // Assert
        assertNotNull(result);
        assertNull(result.getErrorInfo());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext的正确构造
     */
    @Test
    @DisplayName("测试ServiceContext构造")
    void testServiceContextConstruction() {
        // Assert
        assertNotNull(serviceContext);
        assertNotNull(serviceContext.getRequestContext());
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(user, serviceContext.getRequestContext().getUser());
        assertEquals(RequestContext.RequestSource.CEP, serviceContext.getRequestContext().getRequestSource());
        assertEquals(Lang.zh_CN, serviceContext.getRequestContext().getLang());
    }
}
