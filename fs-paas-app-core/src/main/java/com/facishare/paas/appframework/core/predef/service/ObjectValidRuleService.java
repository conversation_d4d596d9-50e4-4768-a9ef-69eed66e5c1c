package com.facishare.paas.appframework.core.predef.service;


import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.validateRule.*;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.DescribeLayoutValidateModel;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.Rule;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Created by liyiguang on 2017/10/11.
 */
@ServiceModule("validate_rule")
@Component
@Slf4j
public class ObjectValidRuleService {

    @Autowired
    private ValidateRuleServiceImpl validateRuleService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private LogService logService;
    @Autowired
    private LayoutRuleLogicService layoutRuleLogicService;
    @Autowired
    private RecordTypeLogicService recordTypeLogicService;
    @Autowired
    private LayoutLogicService layoutService;


    private String buildLogMessage(String actionKey, String ruleApi) {
        return String.join("",
                I18NExt.text(actionKey),
                " ",
                I18NExt.text(I18NKey.VALIDATE_RULE),
                ": ",
                ruleApi);
    }

    @ServiceMethod("create")
    public CreateRule.Result create(CreateRule.Arg arg, ServiceContext context) {
        CreateRule.Result result = new CreateRule.Result();
        IRule rule = new Rule();
        rule.fromJsonString(arg.getJson_data());
        rule.setTenantId(context.getTenantId());
        rule.setCreatedBy(context.getUser().getUserId());
        rule.setLastModifiedBy(context.getUser().getUserId());
        RuleResult ruleResult = validateRuleService.create(rule);
        result.setSuccess(ruleResult.isSuccess());
        logService.log(context.getUser(), EventType.ADD, ActionType.UPDATE_OBJ, rule.getDescribeApiName(),
                buildLogMessage(I18NKey.CREATE, rule.getApiName())
        );
        return result;
    }

    @ServiceMethod("update")
    public UpdateRule.Result update(UpdateRule.Arg arg, ServiceContext context) {
        UpdateRule.Result result = new UpdateRule.Result();

        IRule rule = new Rule();
        rule.fromJsonString(arg.getJson_data());
        RuleResult ruleResult = validateRuleService.update(context.getUser(), rule);
        result.setSuccess(ruleResult.isSuccess());
        logService.log(context.getUser(), EventType.MODIFY, ActionType.UPDATE_OBJ, rule.getDescribeApiName(),
                buildLogMessage(I18NKey.EDIT, rule.getApiName())
        );
        return result;
    }

    @ServiceMethod("delete")
    public DeleteRule.Result delete(DeleteRule.Arg arg, ServiceContext context) {
        DeleteRule.Result result = new DeleteRule.Result();
        RuleResult ruleResult = validateRuleService.delete(arg.getDescribeApiName(), context.getTenantId(), arg.getRuleApiName());
        result.setSuccess(ruleResult.isSuccess());
        logService.log(context.getUser(), EventType.DELETE, ActionType.UPDATE_OBJ, arg.getDescribeApiName(),
                buildLogMessage(I18NKey.DELETED, arg.getRuleApiName())
        );
        return result;
    }

    @ServiceMethod("findRuleList")
    public FindValidRuleList.Result findRuleList(FindValidRuleList.Arg arg, ServiceContext context) {
        FindValidRuleList.Result result = new FindValidRuleList.Result();
        RuleResult ruleResult = validateRuleService.findRuleList(arg.getDescribeApiName(), context.getTenantId(), arg.getRuleName());
        result.setSuccess(ruleResult.isSuccess());
        result.setRuleList(ruleResult.getRuleList());
        return result;
    }

    @ServiceMethod("findRuleInfo")
    public FindRuleInfo.Result findRuleInfo(FindRuleInfo.Arg arg, ServiceContext context) {
        FindRuleInfo.Result result = new FindRuleInfo.Result();
        RuleResult ruleResult = validateRuleService.findRuleInfo(arg.getDescribeApiName(), context.getTenantId(), arg.getRuleApiName());
        result.setSuccess(ruleResult.isSuccess());
        result.setRule(ruleResult.getRule());
        return result;
    }

    @ServiceMethod("isActive")
    public ActiveRule.Result isActive(ActiveRule.Arg arg, ServiceContext context) {
        ActiveRule.Result result = new ActiveRule.Result();
        RuleResult ruleResult = validateRuleService.isActive(arg.getDescribeApiName(), context.getUser(), arg.getRuleApiName(), arg.isActive());
        result.setSuccess(ruleResult.isSuccess());
        boolean toActive = arg.isActive();
        logService.log(context.getUser(), toActive ? EventType.ENABLE : EventType.DISABLE, ActionType.UPDATE_OBJ,
                arg.getDescribeApiName(), buildLogMessage(toActive ? I18NKey.ENABLE : I18NKey.DISABLE, arg.getRuleApiName())
        );
        return result;
    }

    @ServiceMethod("validateRule")
    public ValidateRule.Result validateRule(ValidateRule.Arg arg, ServiceContext context) {
        validateParam(arg, context.getTenantId());
        ValidateRule.Result result = new ValidateRule.Result();
        Map<String, List<IObjectData>> details = Maps.newHashMap();
        arg.getDetails()
                .forEach((key, value) -> details.put(key, value.stream()
                        .map(ObjectDataDocument::toObjectData)
                        .collect(Collectors.toList())));
        IObjectData objectData = arg.getObjectData().toObjectData();
        Set<String> apiNames = getApiNames(arg, details);
        Map<String, IObjectDescribe> objectDescribes = describeLogicService.findObjects(context.getTenantId(), apiNames);
        RuleResult ruleResult = validateRuleService.validateRule(context.getUser(), arg.getOption(), objectDescribes, objectData, details);
        if (ruleResult.isMatch()) {
            List<String> messages = Lists.newArrayList(ruleResult.getBlockMessages());
            messages.addAll(ruleResult.getNonBlockMessages());
            result.setMessage(Joiner.on("; ").join(messages));
            result.setBlockMessages(ruleResult.getBlockMessages());
            result.setNonBlockMessages(ruleResult.getNonBlockMessages());
        }
        result.setSave(ruleResult.isSave());
        return result;
    }

    private void validateParam(ValidateRule.Arg arg, String tenantId) {
        if (Objects.isNull(arg)) {
            log.warn("param is empty: ei:{}", tenantId);
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_EMPTY));
        }
        if (Objects.isNull(arg.getObjectData())) {
            log.warn("param error, ei:{}", tenantId);
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
    }

    private Set<String> getApiNames(ValidateRule.Arg arg, Map<String, List<IObjectData>> details) {
        Set<String> apiNames = Sets.newHashSet(details.keySet());
        String objectApiName = arg.getObjectApiName();
        if (!Strings.isNullOrEmpty(objectApiName)) {
            apiNames.add(objectApiName);
        }
        IObjectData objectData = arg.getObjectData().toObjectData();
        objectApiName = objectData.getDescribeApiName();
        if (!Strings.isNullOrEmpty(objectApiName)) {
            apiNames.add(objectApiName);
        }
        return apiNames;
    }

    @ServiceMethod("cacheValidate")
    public CacheValidate.Result cacheValidate(CacheValidate.Arg arg, ServiceContext context) {
        CacheValidate.Result result = CacheValidate.Result.builder()
                .success(false)
                .build();
        if (Objects.isNull(arg) || StringUtils.isEmpty(arg.getObjectApiName()) || Objects.isNull(arg.getDescribeVersion())) {
            return result;
        }
        String recordType = arg.getRecordType();
        String objectApiName = arg.getObjectApiName();
        String layoutType = arg.getLayoutType();
        User user = context.getUser();
        Map<String, Long> describeApiNameAndVersionMap = CollectionUtils.nullToEmpty(arg.getDetailCacheValid()).stream()
                .collect(Collectors.toMap(CacheValidate.DetailObjCacheValid::getObjectApiName, CacheValidate.DetailObjCacheValid::getDescribeVersion));
        describeApiNameAndVersionMap.put(objectApiName, arg.getDescribeVersion());
        Map<String, IObjectDescribe> objectDescribes = describeLogicService.findObjectsWithoutCopy(user.getTenantId(), describeApiNameAndVersionMap.keySet());
        for (IObjectDescribe describe : objectDescribes.values()) {
            if (describe.getVersion() > describeApiNameAndVersionMap.get(describe.getApiName())) {
                return result;
            }
        }
        List<DescribeLayoutValidateModel> layoutRuleValidateModels = buildDescribeLayoutValidateModels(arg);
        // 校验从对象个数和逻辑
        if (!validateDetailObjectLogic(user, layoutRuleValidateModels, objectDescribes, getLayoutSupplier(user, objectDescribes, recordType, objectApiName, layoutType))) {
            return result;
        }
        if (!layoutRuleLogicService.layoutRuleValidate(user, layoutRuleValidateModels)) {
            return result;
        }
        if (StringUtils.isNoneEmpty(layoutType, recordType)
                && !recordTypeLogicService.validateLayoutAndRecordType(user, objectDescribes, layoutRuleValidateModels)) {
            return result;
        }
        result.setSuccess(true);
        return result;
    }

    private Supplier<ILayout> getLayoutSupplier(User user, Map<String, IObjectDescribe> objectDescribes, String recordType, String objectApiName, String layoutType) {
        return () -> layoutService.findObjectLayoutWithType(
                LayoutLogicService.LayoutContext.of(user),
                recordType,
                objectDescribes.get(objectApiName),
                layoutType,
                null,
                false);
    }

    private List<DescribeLayoutValidateModel> buildDescribeLayoutValidateModels(CacheValidate.Arg arg) {
        List<LayoutRuleInfo> layoutRuleInfos = CollectionUtils.nullToEmpty(arg.getLayoutRule()).stream()
                .map(x -> LayoutRuleExt.of(x).getRuleInfo()).
                collect(Collectors.toList());
        List<DescribeLayoutValidateModel> layoutRuleValidateModels = Lists.newArrayList();
        DescribeLayoutValidateModel.LayoutRuleValidateInfo layoutRuleValidateInfo = DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                .layoutApiName(arg.getLayoutApiName())
                .layoutLastModifiedTime(arg.getLayoutLastModifiedTime())
                .layoutRuleInfos(layoutRuleInfos)
                .build();
        DescribeLayoutValidateModel masterValidateModel = DescribeLayoutValidateModel.builder()
                .objectApiName(arg.getObjectApiName())
                .isMaster(true)
                .layoutVersion(arg.getLayoutVersion())
                .layoutRuleValidateInfos(Lists.newArrayList(layoutRuleValidateInfo))
                .masterRecordType(arg.getRecordType())
                .layoutType(arg.getLayoutType())
                .build();
        layoutRuleValidateModels.add(masterValidateModel);
        CollectionUtils.nullToEmpty(arg.getDetailCacheValid()).forEach(detail -> {
            List<DescribeLayoutValidateModel.LayoutRuleValidateInfo> layoutRuleValidateInfos = Lists.newArrayList();
            for (CacheValidate.DetailLayoutRelatedInfo detailLayoutRelatedInfo : CollectionUtils.nullToEmpty(detail.getDetailLayoutList())) {
                List<LayoutRuleInfo> detailLayoutRuleInfos = CollectionUtils.nullToEmpty(detailLayoutRelatedInfo.getLayoutRule()).stream()
                        .map(x -> LayoutRuleExt.of(x).getRuleInfo()).
                        collect(Collectors.toList());
                DescribeLayoutValidateModel.LayoutRuleValidateInfo detailLayoutValidateInfo = DescribeLayoutValidateModel.LayoutRuleValidateInfo.builder()
                        .layoutApiName(detailLayoutRelatedInfo.getLayoutApiName())
                        .layoutLastModifiedTime(detailLayoutRelatedInfo.getLayoutLastModifiedTime())
                        .layoutRuleInfos(detailLayoutRuleInfos)
                        .recordType(detailLayoutRelatedInfo.getRecordType())
                        .build();
                layoutRuleValidateInfos.add(detailLayoutValidateInfo);
            }
            DescribeLayoutValidateModel detailValidateModel = DescribeLayoutValidateModel.builder()
                    .objectApiName(detail.getObjectApiName())
                    .layoutRuleValidateInfos(layoutRuleValidateInfos)
                    .build();
            layoutRuleValidateModels.add(detailValidateModel);
        });
        return layoutRuleValidateModels;
    }

    private boolean validateDetailObjectLogic(User user, List<DescribeLayoutValidateModel> layoutRuleValidateModels, Map<String, IObjectDescribe> objectDescribes, Supplier<ILayout> layoutSupplier) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_VALIDATE_DETAIL_OBJECT_LOGIC_GRAY_EI, user.getTenantId())) {
            return true;
        }
        // 提取主对象信息
        Optional<DescribeLayoutValidateModel> masterModelOpt = layoutRuleValidateModels.stream()
                .filter(model -> BooleanUtils.isTrue(model.getIsMaster()))
                .findFirst();

        if (!masterModelOpt.isPresent()) {
            return false;
        }
        DescribeLayoutValidateModel masterModel = masterModelOpt.get();
        String masterObjectApiName = masterModel.getObjectApiName();
        String layoutType = masterModel.getLayoutType();
        // 提取从对象信息
        List<DescribeLayoutValidateModel> detailModels = layoutRuleValidateModels.stream()
                .filter(model -> !BooleanUtils.isTrue(model.getIsMaster()))
                .collect(Collectors.toList());
        // 获取主对象描述
        IObjectDescribe masterDescribe = objectDescribes.get(masterObjectApiName);
        if (Objects.isNull(masterDescribe)) {
            return false;
        }
        // 提取期望的从对象API名称
        Set<String> expectedDetailApiNames = detailModels.stream()
                .map(DescribeLayoutValidateModel::getObjectApiName)
                .collect(Collectors.toSet());

        // 获取实际的从对象列表
        List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribes(user.getTenantId(), masterObjectApiName);
        // 1. 激活状态过滤（默认只包含激活的从对象，与 handleDetailObject 逻辑一致）
        detailDescribes = detailDescribes.stream()
                .filter(IObjectDescribe::isActive)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(detailDescribes) && CollectionUtils.notEmpty(expectedDetailApiNames)) {
            log.warn("detail object is empty, tenantId:{}, masterObjectApiName:{}", user.getTenantId(), masterObjectApiName);
            return false;
        }

        // 2. 权限过滤
        if (CollectionUtils.notEmpty(detailDescribes)) {
            String actionCode = getActionCodeByLayoutType(layoutType);
            detailDescribes = describeLogicService.filterDescribesWithActionCode(user, detailDescribes, actionCode);
        }
        // 3. 布局过滤
        ILayout layout = layoutSupplier.get();
        if (Objects.nonNull(layout)) {
            detailDescribes = detailDescribes.stream()
                    .filter(detailDescribe -> !LayoutExt.of(layout).isComponentHidden(ComponentExt.getDetailComponentName(detailDescribe.getApiName())))
                    .collect(Collectors.toList());
        }

        // 提取实际的从对象API名称
        Set<String> actualDetailApiNames = detailDescribes.stream()
                .map(IObjectDescribe::getApiName)
                .collect(Collectors.toSet());

        log.info("Detail object validation summary, tenantId:{}, masterObjectApiName:{}, expectedCount:{}, actualCount:{}, expectedApiNames:{}, actualApiNames:{}",
                user.getTenantId(), masterObjectApiName, expectedDetailApiNames.size(), actualDetailApiNames.size(),
                expectedDetailApiNames, actualDetailApiNames);

        // 校验从对象API名称是否匹配
        if (!expectedDetailApiNames.equals(actualDetailApiNames)) {
            return false;
        }

        // 校验每个从对象是否具有有效的主从关系字段
        for (IObjectDescribe detailObject : detailDescribes) {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(detailObject);
            Optional<MasterDetailFieldDescribe> masterDetailField = describeExt.getMasterDetailFieldDescribe();
            if (!masterDetailField.isPresent()) {
                log.warn("Master-detail field not found in detail object, tenantId:{}, masterObjectApiName:{}, detailObjectApiName:{}",
                        user.getTenantId(), masterObjectApiName, detailObject.getApiName());
                return false;  // 从对象必须有主从关系字段
            }
        }

        // 校验业务类型匹配关系
        boolean recordTypeValidationResult = validateRecordTypeMatchRelation(user, layoutRuleValidateModels, masterDescribe, detailDescribes);

        log.info("validateDetailObjectLogic completed, tenantId:{}, masterObjectApiName:{}, result:{}",
                user.getTenantId(), masterObjectApiName, recordTypeValidationResult);

        return recordTypeValidationResult;
    }

    private String getActionCodeByLayoutType(String layoutType) {
        if (LayoutTypes.ADD.equals(layoutType)) {
            return ObjectAction.CREATE.getActionCode();
        } else if (LayoutTypes.EDIT.equals(layoutType)) {
            return ObjectAction.UPDATE.getActionCode();
        } else {
            return ObjectAction.VIEW_LIST.getActionCode();
        }
    }

    /**
     * 校验业务类型匹配关系
     *
     * @param user 用户信息
     * @param layoutRuleValidateModels 布局规则校验模型列表
     * @param masterDescribe 主对象描述
     * @param detailDescribes 从对象描述列表
     * @return 校验结果，true表示通过，false表示失败
     */
    private boolean validateRecordTypeMatchRelation(User user, List<DescribeLayoutValidateModel> layoutRuleValidateModels,
                                                    IObjectDescribe masterDescribe,
                                                    List<IObjectDescribe> detailDescribes) {
        // 提取主对象业务类型
        String masterRecordType = layoutRuleValidateModels.stream()
                .filter(model -> BooleanUtils.isTrue(model.getIsMaster()))
                .map(DescribeLayoutValidateModel::getMasterRecordType)
                .filter(StringUtils::isNotEmpty)
                .findFirst()
                .orElse(null);

        try {
            // 如果没有指定主对象业务类型，跳过校验
            if (StringUtils.isEmpty(masterRecordType)) {
                return true;
            }
            // 如果没有从对象，跳过校验
            if (CollectionUtils.empty(detailDescribes)) {
                return true;
            }
            // 构建从对象业务类型映射
            Map<String, Set<String>> detailRecordTypeMap = buildDetailRecordTypeMap(layoutRuleValidateModels);
            // 获取从对象API名称列表
            List<String> detailApiNames = detailDescribes.stream()
                    .map(IObjectDescribe::getApiName)
                    .collect(Collectors.toList());

            // 获取有效业务类型并过滤匹配关系
            Map<String, List<IRecordTypeOption>> validRecordTypeMap = recordTypeLogicService.findValidRecordTypeListMap(detailApiNames, user);
            Map<String, List<IRecordTypeOption>> filteredRecordTypeMap = recordTypeLogicService.filterUnMatchRecordTypes(user.getTenantId(),
                    validRecordTypeMap, masterDescribe.getApiName(), masterRecordType);

            log.debug("Record type filtering completed, tenantId:{}, masterApiName:{}, masterRecordType:{}, validRecordTypeMapSize:{}, filteredRecordTypeMapSize:{}",
                    user.getTenantId(), masterDescribe.getApiName(), masterRecordType,
                    validRecordTypeMap.size(), filteredRecordTypeMap.size());

            // 校验从对象缓存中的业务类型
            boolean validationResult = validateDetailRecordTypes(detailRecordTypeMap, filteredRecordTypeMap);

            log.debug("validateRecordTypeMatchRelation completed, tenantId:{}, masterApiName:{}, result:{}",
                    user.getTenantId(), masterDescribe.getApiName(), validationResult);

            return validationResult;
        } catch (Exception e) {
            log.warn("validateRecordTypeMatchRelation error, tenantId:{}, masterApiName:{}, recordType:{}",
                    user.getTenantId(), masterDescribe.getApiName(), masterRecordType, e);
            // 异常情况下允许通过，避免影响正常流程
            return true;
        }
    }

    /**
     * 从 layoutRuleValidateModels 构建业务类型映射
     *
     * @param layoutRuleValidateModels 布局规则校验模型列表
     * @return 从对象API名称到业务类型集合的映射
     */
    private Map<String, Set<String>> buildDetailRecordTypeMap(List<DescribeLayoutValidateModel> layoutRuleValidateModels) {
        Map<String, Set<String>> detailRecordTypeMap = new HashMap<>();

        layoutRuleValidateModels.stream()
                .filter(model -> !BooleanUtils.isTrue(model.getIsMaster()))
                .forEach(detailModel -> {
                    String detailApiName = detailModel.getObjectApiName();
                    Set<String> recordTypes = detailModel.getLayoutRuleValidateInfos().stream()
                            .map(DescribeLayoutValidateModel.LayoutRuleValidateInfo::getRecordType)
                            .filter(StringUtils::isNotEmpty)
                            .collect(Collectors.toSet());
                    detailRecordTypeMap.put(detailApiName, recordTypes);
                });

        return detailRecordTypeMap;
    }

    /**
     * 校验从对象业务类型
     *
     * @param detailRecordTypeMap 从对象业务类型映射
     * @param filteredRecordTypeMap 过滤后的有效业务类型映射
     * @return 校验结果，true表示通过，false表示失败
     */
    private boolean validateDetailRecordTypes(Map<String, Set<String>> detailRecordTypeMap,
                                              Map<String, List<IRecordTypeOption>> filteredRecordTypeMap) {
        for (Map.Entry<String, Set<String>> entry : detailRecordTypeMap.entrySet()) {
            String detailApiName = entry.getKey();
            Set<String> expectedRecordTypes = entry.getValue();

            List<IRecordTypeOption> validRecordTypes = filteredRecordTypeMap.get(detailApiName);
            if (CollectionUtils.empty(validRecordTypes)) {
                return false;
            }

            Set<String> validRecordTypeNames = validRecordTypes.stream()
                    .map(IRecordTypeOption::getApiName)
                    .collect(Collectors.toSet());

            // 检查是否有不匹配的业务类型
            for (String expectedRecordType : expectedRecordTypes) {
                if (StringUtils.isNotEmpty(expectedRecordType)
                        && !validRecordTypeNames.contains(expectedRecordType)) {
                    log.warn("Record type mismatch found: detailApiName={}, recordType={}, validRecordTypes={}",
                            detailApiName, expectedRecordType, validRecordTypeNames);
                    return false; // 发现不匹配的业务类型
                }
            }
        }
        return true;
    }
}
