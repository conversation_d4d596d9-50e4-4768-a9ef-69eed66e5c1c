package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.ValidationResultDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.InternationalItem;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2020/06/12
 */
@Slf4j
public abstract class AbstractCustomButtonAction<A> extends BaseObjectApprovalAction<A, CustomButtonAction.Result> {

    protected IObjectData objectData;
    protected Map<String, List<IObjectData>> detailObjectData;

    protected abstract boolean skipTriggerApprovalFlow();

    protected abstract String getObjectDataId();

    protected abstract ButtonExecutor.Result startCustomButton();

    protected abstract boolean skipLockValidate();

    @Override
    protected List<String> getDataPrivilegeIds(A arg) {
        return null;
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(actionContext.getUser(), actionContext.getAppId());
    }

    protected void triggerValidateRule(){

    }

    @Override
    protected void before(A arg) {
        super.before(arg);
        validate();
    }

    private void validate() {
        if (skipValidate()) {
            return;
        }
        // 数据锁定，判断按钮是否可以展示
        if (!skipLockValidate() && ObjectDataExt.of(objectData).isLock() && !ButtonExt.of(udefButton).isShowWhenLock()) {
            throw new ValidateException(I18N.text(I18NKey.DATA_HAS_been_locked));
        }
        //若参数中存在掩码字段且值发生变化则抛出异常提示
        validateMaskField();
        //校验验证规则
        validateRule();
    }

    private void validateRule() {
        triggerValidateRule();
    }

    protected boolean skipValidate() {
        return ButtonExt.of(udefButton).isListNormalButton();
    }

    private void validateMaskField() {
        if (CollectionUtils.empty(udefButton.getParamForm())) {
            return;
        }
        List<IFieldDescribe> maskFields = ParamForm.fromList(udefButton.getParamForm()).stream()
                .filter(x -> objectDescribe.getApiName().equals(x.getObjectApiName()))
                .map(IParamForm::convertToFieldApiName)
                .filter(x -> objectDescribe.containsField(x))
                .map(x -> objectDescribe.getFieldDescribe(x))
                .filter(x -> FieldDescribeExt.of(x).isShowMask())
                .collect(Collectors.toList());
        if (CollectionUtils.empty(maskFields)) {
            return;
        }
        serviceFacade.fillMaskFieldValue(actionContext.getUser(), Lists.newArrayList(objectData), maskFields, false);
        maskFields.stream()
                .filter(x -> ObjectDataExt.of(objectData).containsField(FieldDescribeExt.getShowFieldName(x.getApiName())))
                .forEach(x -> {
                    Object dbValue = objectData.get(x.getApiName());
                    Object newValue = getArgs().get(ParamForm.buildFormApiName(x.getApiName()));
                    if (!ObjectDataExt.isValueEqual(dbValue, newValue, x)) {
                        throw new ValidateException(I18N.text(I18NKey.CANNOT_UPDATE_MASK_FIELD));
                    }
                });
    }

    @Override
    protected void init() {
        super.init();
        initButton();
        initObjectData();
        // 清理 args 中 form_图片/附件字段可能存在的 signedUrl
        cleanSignedUrl();
        //加工参数，比如从objectData补充隐藏的字段的值
        processArg();
    }

    @SuppressWarnings("unchecked")
    private void cleanSignedUrl() {
        if (CollectionUtils.empty(getArgs())) {
            return;
        }
        getArgs().forEach((formField, value) -> {
            if (!(value instanceof Map)) {
                return;
            }
            ((Map<String, Object>) value).put(ImageExt.SIGNATURE_URL, null);
        });
    }

    protected void initObjectData() {
        String objectDataId = getObjectDataId();
        if (Strings.isNullOrEmpty(objectDataId)) {
            return;
        }
        User user = actionContext.getUser();

        objectData = serviceFacade.findObjectData(actionContext.getUser(), objectDataId, actionContext.getObjectApiName());
        if (serviceFacade.isMasterObject(actionContext.getUser().getTenantId(), objectDescribe.getApiName())) {
            //获取所有的从describe
            List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribes(user.getTenantId(), objectDescribe.getApiName());
            //获取所有的从数据
            detailObjectData = serviceFacade.findDetailObjectDataList(detailDescribes, objectData, user);
        }
    }

    private void processArg() {
        if (CollectionUtils.empty(udefButton.getParamForm())) {
            return;
        }

        ParamForm.fromList(udefButton.getParamForm()).stream()
                .filter(x -> objectDescribe.getApiName().equals(x.getObjectApiName()))
                .filter(x -> !getArgs().containsKey(x.getApiName()))
                .forEach(x -> getArgs().put(x.getApiName(), objectData.get(x.convertToFieldApiName())));
    }

    @Override
    protected boolean skipNonBlockingPreFunction() {
        return false;
    }

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    protected IObjectData getPostObjectData() {
        return objectData;
    }


    @Override
    protected Map<String, List<IObjectData>> getPostObjectDetails() {
        return detailObjectData;
    }

    /**
     * 跳过按钮的前置执行动作
     *
     * @return
     */
    @Override
    protected boolean skipPreFunction() {
        return true;
    }

    @Override
    protected CustomButtonAction.Result buildValidateResult() {
        String message = validatedFunctionResult.getReturnValue() == null ? "" : validatedFunctionResult.getReturnValue().toString();
        BaseObjectSaveAction.ValidationMessage validateMessage = BaseObjectSaveAction.ValidationMessage
                .builder()
                .isMatch(true)
                .build()
                .setMessage(message, validatedFunctionResult.isBlock());
        return CustomButtonAction.Result.builder().validationMessage(validateMessage).build();
    }

    @Override
    protected boolean skipPostFunction() {
        return true;
    }

    @Override
    protected CustomButtonAction.Result doAct(A arg) {
        // 触发审批流
        if (triggerApprovalFlow()) {
            stopWatch.lap("triggerApprovalFlow");
            return CustomButtonAction.Result.builder().build();
        }
        stopWatch.lap("triggerApprovalFlow");
        // 执行按钮动作
        ButtonExecutor.Result executorResult = startCustomButton();
        stopWatch.lap("startCustomButton");
        Object returnValue = executorResult.getReturnValue();
        CustomButtonAction.Result result = CustomButtonAction.Result.builder()
                .objectData(ObjectDataDocument.of(executorResult.getObjectData()))
                .details(ObjectDataDocument.ofMap(executorResult.getDetails()))
                .targetDescribeApiName(executorResult.getTargetDescribeApiName())
                .hasReturnValue(executorResult.isHasReturnValue())
                .returnValue(returnValue)
                .returnType(executorResult.getReturnType())
                .build();

        return result;
    }

    @Override
    protected final CustomButtonAction.Result buildValidationResult(ValidationResultDocument validationResultDocument) {
        return CustomButtonAction.Result.builder().validationResult(validationResultDocument).build();
    }

    private boolean triggerApprovalFlow() {
        if (skipTriggerApprovalFlow()) {
            return false;
        }
        //只有自定义按钮触发审批流
        if (!ButtonExt.COMMON_BUTTON_TYPE.equals(udefButton.getButtonType())) {
            return false;
        }

        // 当前数据的生命状态为变更中，提示有正在进行中的审批流程
        if (ObjectLifeStatus.IN_CHANGE == ObjectDataExt.of(objectData).getLifeStatus()) {
            throw new ValidateException(ApprovalFlowStartResult.ALREADY_EXIST.getMessage());
        }

        // 当前按钮是否有有匹配的审批流程
        boolean matchRule = serviceFacade.matchRule(objectData.getId(), actionContext.getObjectApiName(), actionContext.getUser(), udefButton.getApiName());
        if (matchRule) {
            // 当前数据的生命状态为审核中，提示有正在进行中的审批流程
            if (ObjectLifeStatus.UNDER_REVIEW == ObjectDataExt.of(objectData).getLifeStatus()) {
                throw new ValidateException(ApprovalFlowStartResult.ALREADY_EXIST.getMessage());
            }
            // 触发审批流
            Map<String, ApprovalFlowStartResult> approvalFlowStartResultMap = startApprovalFlow(objectData, udefButton);
            if (!approvalFlowStartResultMap.containsValue(ApprovalFlowStartResult.APPROVAL_NOT_EXIST)) {
                if (approvalFlowStartResultMap.containsValue(ApprovalFlowStartResult.ALREADY_EXIST)) {
                    throw new ValidateException(ApprovalFlowStartResult.ALREADY_EXIST.getMessage());
                }
                return true;
            }
        }

        return false;
    }

    /**
     * 整理：
     * (流程需要的找流程确认)dataMap:
     * {
     * objectDataId : {
     * buttonName: buttonName,
     * buttonDescription: buttonDescription
     * params: {
     * "name1": "value1",
     * "name2": "value2"
     * }
     * }
     * }
     * (审批成功流程回调StandardFlowCompletedAction)callBackData:
     * {
     * objectDataId: {
     * buttonApiName: buttonApiName,
     * args: {
     * "name1": "value1",
     * "name2": "value2"
     * },
     * oldStatus: oldStatus,
     * last_life_status: last_life_status
     * }
     * }
     *
     * @param objectData
     * @param button
     * @return
     */
    protected Map<String, ApprovalFlowStartResult> startApprovalFlow(IObjectData objectData, IUdefButton button) {

        Map<String, Object> data = Maps.newHashMap();
        data.put("buttonName", button.getLabel());
        data.put("buttonDescription", button.getDescription());
        if (CollectionUtils.notEmpty(getArgs())) {
            fillExtendInfos();
        }
        data.put("params", getArgs());
        Map<String, Map<String, Object>> dataMap = Maps.newHashMap();
        dataMap.put(getObjectDataId(), data);
        Map<String, Object> callbackData = Maps.newHashMap();
        callbackData.put("buttonApiName", button.getApiName());
        callbackData.put("args", getArgs());

        Object oldStatus = Objects.nonNull(objectData.get("account_status"))
                ? objectData.get("account_status")
                : objectData.get("leads_status");
        if (Objects.nonNull(oldStatus)) {
            callbackData.put("oldStatus", oldStatus);
        }
        //将原状态放入callbackData，用于审批通过和驳回之后恢复原状态
        callbackData.put(ObjectLifeStatus.LAST_LIFE_STATUS_API_NAME, ObjectDataExt.of(objectData).getLifeStatusText());
        Map<String, Map<String, Object>> callbackDataMap = Maps.newHashMap();
        callbackDataMap.put(getObjectDataId(), callbackData);

        //触发审批成功以后的生命状态
        actionContext.setAttribute(ExtraDataKeys.NEW_LIFE_STATUS, ObjectLifeStatus.IN_CHANGE.getCode());

        Map<String, ApprovalFlowStartResult> resultMap = startApprovalFlow(Lists.newArrayList(objectData),
                button.getApiName(), dataMap, callbackDataMap);

        return resultMap;
    }

    private void fillExtendInfos() {
        IObjectData newObjectData = new ObjectData();
        List<IFieldDescribe> fieldDescribes = ParamForm.fromList(udefButton.getParamForm()).stream()
                .filter(x -> Objects.equals(x.getObjectApiName(), objectDescribe.getApiName()))
                .filter(x -> getArgs().containsKey(x.getApiName()))
                .map(x -> x.convertToFieldApiName())
                .filter(x -> ObjectDescribeExt.of(objectDescribe).containsField(x))
                .map(x -> ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribe(x))
                .collect(Collectors.toList());
        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            String fieldApiName = fieldDescribe.getApiName();
            Object fieldValue = getArgs().get(ParamForm.buildFormApiName(fieldApiName));
            if (Objects.isNull(fieldValue)) {
                continue;
            }
            newObjectData.set(fieldApiName, fieldValue);
        }

        serviceFacade.fillExtendFieldInfo(objectDescribe, Lists.newArrayList(newObjectData), actionContext.getUser());
        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            String fieldExtendName = FieldDescribeExt.of(fieldDescribe).getFieldExtendName();
            if (StringUtils.isBlank(fieldExtendName)) {
                continue;
            }
            Object fieldValue = newObjectData.get(fieldExtendName);
            if (Objects.isNull(fieldValue)) {
                continue;
            }
            getArgs().put(ParamForm.buildFormApiName(fieldExtendName), fieldValue);
        }
    }

    protected final Map<String, Object> getSearchQuery(QueryParam queryParam) {
        if (!AppFrameworkConfig.isListNormalUiActionSupportQueryParam(actionContext.getTenantId(), actionContext.getObjectApiName(), getButtonApiName())) {
            return Collections.emptyMap();
        }
        // 下游暂不支持
        if (actionContext.getUser().isOutUser()) {
            log.warn("downstream enterprises not support, apiName:{}, user:{}", actionContext.getObjectApiName(), actionContext.getUser());
            throw new ValidateException(I18NExt.text(I18NKey.DOWNSTREAM_ENTERPRISES_DO_NOT_SUPPORT));
        }
        if (RequestUtil.isMobileOrMobileDeviceRequest()) {
            log.warn("downstream enterprises not support, apiName:{}, user:{}", actionContext.getObjectApiName(), actionContext.getUser());
            throw new ValidateException(I18NExt.text(I18NKey.MOBILE_NOT_SUPPORT));
        }
        if (Objects.isNull(queryParam)) {
            return Collections.emptyMap();
        }
        if (queryParam.fromAllFieldsSearch()) {
            log.warn("from all fields search, ei:{}, apiName:{}", actionContext.getTenantId(), actionContext.getObjectApiName());
            throw new ValidateException(I18NExt.text(I18NKey.NOT_SUPPORT_FROM_GLOBAL_SEARCH));
        }
        return JacksonUtils.fromJson(JacksonUtils.toJson(queryParam), new TypeReference<Map<String, Object>>() {
        });
    }

    protected Set<String> getDataIds() {
        return Sets.newHashSet();
    }

    protected void recordModificationRecord() {
        try {
            Set<String> dataIds = getDataIds();
            if (CollectionUtils.empty(dataIds)
                    || Objects.isNull(objectDescribe)
                    || Objects.isNull(udefButton)
                    || !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.BUTTON_CLICK_RECORD_EI, actionContext.getTenantId())) {
                return;
            }
            ButtonExt buttonExt = ButtonExt.of(udefButton);
            if (!(buttonExt.isUIAction() || buttonExt.isCommonButton())) {
                return;
            }

            String msg = I18NExt.text(I18NKey.CUSTOM_BUTTON_CLICK, buttonExt.getLabel(), getButtonApiName());
            User user = actionContext.getUser();
            // xxx 点击了自定义按钮: {按钮名称}({按钮api})
            String buttonI18NLabelKey = buttonExt.getButtonI18NLabelKey(objectDescribe.getApiName());
            InternationalItem internationalItem = InternationalItem.builder()
                    .internationalKey(I18NKey.CUSTOM_BUTTON_CLICK)
                    .defaultInternationalValue(msg)
                    .internationalParameters(Lists.newArrayList(buttonI18NLabelKey, "mockKey"))
                    .defaultParameterValues(new HashMap<String, String>() {{
                        put(buttonI18NLabelKey, buttonExt.getLabel());
                        put("mockKey", getButtonApiName());
                    }})
                    .build();

            dataIds.forEach(dataId -> serviceFacade.logInternational(user, EventType.MODIFY, ActionType.None,
                    objectDescribe, objectDescribe.getApiName(), dataId,
                    msg, internationalItem));
        } catch (Exception ex) {
            log.warn("recordModificationRecord error", ex);
        }
    }


    @Override
    protected void finallyDo() {
        // 记录修改记录
        recordModificationRecord();
        super.finallyDo();
    }

    @Data
    public static class QueryParam {
        @JsonProperty("search_query_info")
        private String searchQueryInfo;

        @JsonProperty("search_template_id")
        private String searchTemplateId;

        @JsonProperty("ignore_scene_filter")
        private boolean isIgnoreSceneFilter;

        @JsonProperty("search_template_type")
        private String searchTemplateType;

        @JsonProperty("ignore_scene_record_type")
        private boolean isIgnoreSceneRecordType;

        @JsonProperty("data_id_list")
        private List<String> dataIdList;

        public boolean fromAllFieldsSearch() {
            if (!Strings.isNullOrEmpty(searchTemplateId)) {
                return false;
            }
            ISearchTemplateQuery searchTemplateQuery = SearchTemplateQuery.fromJsonString(searchQueryInfo);
            if (Objects.isNull(searchTemplateQuery)) {
                return false;
            }
            return SearchTemplateQueryExt.of(searchTemplateQuery).onlyIdFilter();
        }
    }
}
