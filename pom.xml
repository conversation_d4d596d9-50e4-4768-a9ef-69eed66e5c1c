<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>fs-paas-app-api</module>
        <module>fs-paas-app-core</module>
        <module>fs-paas-app-privilege</module>
        <module>fs-paas-app-privilege-temp</module>
        <module>fs-paas-app-metadata</module>
        <module>fs-paas-app-web</module>
        <module>fs-paas-app-license</module>
        <module>fs-paas-app-log</module>
        <module>fs-paas-app-flow</module>
        <module>fs-paas-app-common</module>
        <module>fs-paas-app-metadata-restdriver</module>
        <module>fs-paas-app-fcp</module>
        <module>fs-paas-app-metadata-util</module>
        <module>fs-paas-app-coordination</module>
        <module>fs-paas-app-payment</module>
        <module>fs-paas-app-config</module>
        <module>fs-paas-app-udobj-rest</module>
        <module>fs-paas-app-function</module>
        <module>fs-paas-app-button</module>
        <module>fs-paas-app-udobj-web</module>
        <module>fs-paas-app-prm</module>
        <module>fs-paas-app-metadata-dao</module>
        <module>fs-paas-app-tool</module>
    </modules>

    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <properties>
        <netty3.version>3.10.6.Final</netty3.version>
        <restproxy.version>6.6.5-SNAPSHOT</restproxy.version>
        <fs-metadata-provider.version>9.6.0-SNAPSHOT</fs-metadata-provider.version>
        <fs-paas-task.version>9.4.0-SNAPSHOT</fs-paas-task.version>
        <fs-paas-function.version>9.2.0-SNAPSHOT</fs-paas-function.version>
        <fs-paas-expression.version>8.8.0-SNAPSHOT</fs-paas-expression.version>
        <fs-redisson-support.version>1.0.0-SNAPSHOT</fs-redisson-support.version>
        <mongo-driver.version>3.10.1</mongo-driver.version>
        <idempotent-util.version>1.2.0-SNAPSHOT</idempotent-util.version>
        <fs-timezone.version>1.0.7-SNAPSHOT</fs-timezone.version>
        <fs-qixin-api.version>0.1.1-SNAPSHOT</fs-qixin-api.version>
        <fs-enterpriserelation-rest-api2.version>2.1.6-SNAPSHOT</fs-enterpriserelation-rest-api2.version>
        <mockito.version>4.11.0</mockito.version>
        <powermock.version>2.0.9</powermock.version>
        <sonar.findbugs.allowuncompiledcode>true</sonar.findbugs.allowuncompiledcode>
        <global-transaction.version>1.0.1-SNAPSHOT</global-transaction.version>
        <fs-social-richtext.version>8.8.0-SNAPSHOT</fs-social-richtext.version>
        <fs-webpage-customer-api.version>1.1.1-SNAPSHOT</fs-webpage-customer-api.version>
        <fs-organization-adapter-api.version>3.0.4-SNAPSHOT</fs-organization-adapter-api.version>
        <fs-organization-api.version>3.0.3-SNAPSHOT</fs-organization-api.version>
    </properties>

    <groupId>com.facishare</groupId>
    <artifactId>fs-paas-appframework</artifactId>
    <version>9.6.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-stone-commons-client</artifactId>
                <version>${stone-commons-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke.transaction</groupId>
                <artifactId>global-transaction-http-support</artifactId>
                <version>${global-transaction.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>retrofit-spring2</artifactId>
                <version>2.0.3-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-material-api</artifactId>
                <version>1.0.47-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-eservice-common</artifactId>
                <version>1.5-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-appcenter-rest-api</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.3.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-msg-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-common-utils</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-callback-api</artifactId>
                <version>0.0.3-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.6.2</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>1.33</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-common-storage</artifactId>
                <version>0.0.5</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-common-result</artifactId>
                <version>0.0.7</version>
            </dependency>
            <dependency>
                <groupId>commons-pool</groupId>
                <artifactId>commons-pool</artifactId>
                <version>1.6</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>4.4.13</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-cep-spring-plugin</artifactId>
                <version>1.0.3-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>4.2.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-client</artifactId>
                <version>4.2.0</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.10.0</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>commons-configuration</groupId>
                <artifactId>commons-configuration</artifactId>
                <version>1.10</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege-temp</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-license</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-log</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-runtime</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-flow</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-restdriver</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-util</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-coordination</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-payment</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-config</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-function</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-prm</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-metadata-provider</artifactId>
                <version>${fs-metadata-provider.version}</version>
            </dependency>

            <!--       业务引入函数调用-->
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-function-biz-api</artifactId>
                <version>${fs-paas-function.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-fsi-proxy</artifactId>
                <version>${fs-fsi-proxy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-qixin-api</artifactId>
                <version>${fs-qixin-api.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>org.javassist</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>javassist-3.14.0-GA</artifactId>
                        <groupId>org.ow2.util.bundles</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mongo-java-driver</artifactId>
                        <groupId>org.mongodb</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 指定 redisson 版本-->
            <dependency>
                <groupId>com.fxiaoke.common</groupId>
                <artifactId>fs-redisson-support</artifactId>
                <version>${fs-redisson-support.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongo-java-driver</artifactId>
                <version>${mongo-driver.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb.morphia</groupId>
                <artifactId>morphia</artifactId>
                <version>1.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-idempotent-util</artifactId>
                <version>${idempotent-util.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.0.Final</version>
            </dependency>

            <!-- Resteasy begin -->
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jaxrs</artifactId>
                <version>${resteasy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-spring</artifactId>
                <version>${resteasy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-servlet-initializer</artifactId>
                <version>${resteasy.version}</version>
            </dependency>
            <!--序列化和反序列化-->
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jackson2-provider</artifactId>
                <version>${resteasy.version}</version>
            </dependency>
            <!--RestEasy整合BeanValidation-->
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-validator-provider</artifactId>
                <version>${resteasy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jackson-provider</artifactId>
                <version>${resteasy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-client</artifactId>
                <version>${resteasy.version}</version>
            </dependency>
            <!-- Resteasy end -->

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-timezone-api</artifactId>
                <version>${fs-timezone.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-timezone-core</artifactId>
                <version>${fs-timezone.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-task-async</artifactId>
                <version>${fs-paas-task.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-enterpriserelation-rest-api2</artifactId>
                <version>${fs-enterpriserelation-rest-api2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-expression</artifactId>
                <version>${fs-paas-expression.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-webpage-customer-api</artifactId>
                <version>${fs-webpage-customer-api.version}</version>
            </dependency>
            <!-- JUnit 5 dependencies -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit-jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit-jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit-jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-params</artifactId>
                <version>${junit-jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- JUnit 4 to JUnit 5 migration support -->
            <dependency>
                <groupId>org.junit.vintage</groupId>
                <artifactId>junit-vintage-engine</artifactId>
                <version>${junit-jupiter.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-reflect</artifactId>
                <version>${powermock.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency> <!-- enables mocking of classes (in addition to interfaces) -->
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>1.15.4</version>
                <scope>test</scope>
            </dependency>
            <dependency> <!-- only required if Hamcrest matchers are used -->
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-core</artifactId>
                <version>1.3</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>1.15.4</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>i18n-util</artifactId>
                <version>1.6.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>net.sf.jopt-simple</groupId>
                <artifactId>jopt-simple</artifactId>
                <version>5.0.3</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty</artifactId>
                <version>${netty3.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.21</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-organization-adapter-api</artifactId>
                <!-- 使用 EnterpriseLanguageClient#getLanguagesInLicenseAndSettings -->
                <version>${fs-organization-adapter-api.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>fs-sentinel-resteasy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <!-- Resteasy begin -->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jaxrs</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-spring</artifactId>
        </dependency>
        <!-- Resteasy end -->

        <!-- groovy begin -->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <scope>compile</scope>
        </dependency>
        <!-- groovy end   -->

        <!-- Spring begin -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <!-- Spring end -->

        <!-- netty  begin -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
        </dependency>
        <!-- netty end -->

        <!-- Log begin -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Log end -->

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>i18n-util</artifactId>
        </dependency>

        <!-- test begin -->
        <!-- JUnit 5 dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-reflect</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>groovy-all</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>groovy-all</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <artifactId>javassist-3.14.0-GA</artifactId>
            <groupId>org.ow2.util.bundles</groupId>
            <version>1.0.0</version>
            <scope>test</scope>
        </dependency>

        <dependency> <!-- enables mocking of classes (in addition to interfaces) -->
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency> <!-- only required if Hamcrest matchers are used -->
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- test end -->

        <!-- AI使用的提示词打分组件 -->
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
            <version>8.11.4</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>JaCoCo Agent</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>
                                prepare-agent
                            </goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>JaCoCo Report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>
                                report-aggregate
                            </goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.9.1.2184</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <systemPropertyVariables>
                        <logback.configurationFile>${project.parent.basedir}/fs-paas-app-common/src/test/resources/logback-test.xml</logback.configurationFile>
                        <org.apache.commons.logging.Log>org.apache.commons.logging.impl.SimpleLog</org.apache.commons.logging.Log>
                        <org.apache.commons.logging.simplelog.defaultlog>error</org.apache.commons.logging.simplelog.defaultlog>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                    <showWarnings>true</showWarnings>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <version>3.0.0-M8</version>
                <configuration>
                    <showSuccess>false</showSuccess>
                </configuration>
            </plugin>
        </plugins>
    </reporting>

</project>
