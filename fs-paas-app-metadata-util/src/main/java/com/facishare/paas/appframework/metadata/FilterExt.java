package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplate;
import com.facishare.paas.timezone.DateTimeFormat;
import com.facishare.paas.timezone.DateUtils;
import com.facishare.paas.timezone.TimeZoneContext;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.metadata.TeamMember.TEAM_MEMBER_EMPLOYEE_API_NAME;
import static com.facishare.paas.common.util.UdobjConstants.RELEVANT_TEAM_API_NAME;
import static com.facishare.paas.timezone.TimeZoneContextHolder.getUserTimeZone;

/**
 * Created by zhouwr on 2018/2/3
 */
@Slf4j
public class FilterExt {
    public static final String ALL_SCENE_FILTER_VALUE = "${current_user}||${current_user_subordinates}||${current_user_dept_users}||${current_user_shared_users}";
    public static final String CRITERIA_ALL_OF_MINE = "${current_user}";
    public static final String FUNCTION_WHERE_TYPE = "function";
    public static final String FIELD_TYPE = "field";
    public static final String CURRENT_LOGIN_USER_ID = "currentLoginUserId__g";
    /**
     * 当前互联企业关联的客户
     */
    public static final String CURRENT_OUT_TENANT_MAPPER_ACCOUNT = "${currentOutTenantMapperAccount__g}";
    /**
     * 当前互联企业关联的合作伙伴
     */
    public static final String CURRENT_OUT_TENANT_MAPPER_PARTNER = "${currentOutTenantMapperPartner__g}";

    @Getter
    @Delegate
    private IFilter filter;

    private static final FilterExt DEFAULT_ALL_SCENE_FILTER = buildAllSceneFilter();
    private static final FilterExt DEFAULT_CRITERIA_ALL_OF_MINE = buildCriteriaAllOfMine();
    private static final List<IFilter> DEFAULT_SCENE_FILTER = Lists.newArrayList(DEFAULT_ALL_SCENE_FILTER.getFilter(), DEFAULT_CRITERIA_ALL_OF_MINE.getFilter());

    public static final Set<Integer> UNCHECK_FILTER_VALUE_TYPES = ImmutableSet.of(FilterValueTypes.FUNCTION_VARIABLE, FilterValueTypes.CONVERT_RULE_VARIABLE, FilterValueTypes.TAG_PARAMETER_VARIABLE, FilterValueTypes.PLAIN_CONTENT);
    private static final Set<String> RANGE_TIME_VALUES = ImmutableSet.of("1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15");

    private FilterExt(IFilter filter) {
        this.filter = filter;
    }

    public static FilterExt of(IFilter filter) {
        return new FilterExt(filter);
    }

    public static FilterExt of(Operator operator, String fieldName, String fieldValue) {
        return of(operator, fieldName, Lists.newArrayList(fieldValue));
    }

    public static FilterExt of(Operator operator, String fieldName, List<String> fieldValue) {
        IFilter filter = new Filter();
        filter.setOperator(operator);
        filter.setFieldName(fieldName);
        filter.setFieldValues(fieldValue);
        return new FilterExt(filter);
    }

    public static FilterExt of(Operator operator, String fieldName, List<String> fieldValue, int filterValueType) {
        IFilter filter = new Filter();
        filter.setOperator(operator);
        filter.setFieldName(fieldName);
        filter.setFieldValues(fieldValue);
        filter.setValueType(filterValueType);
        return new FilterExt(filter);
    }

    public static FilterExt buildAllSceneFilter() {
        return FilterExt.of(Operator.IN, String.format("%s.%s", RELEVANT_TEAM_API_NAME, TEAM_MEMBER_EMPLOYEE_API_NAME), ALL_SCENE_FILTER_VALUE);
    }

    public static FilterExt buildCriteriaAllOfMine() {
        return FilterExt.of(Operator.IN, String.format("%s.%s", RELEVANT_TEAM_API_NAME, TEAM_MEMBER_EMPLOYEE_API_NAME), CRITERIA_ALL_OF_MINE);
    }

    public static FilterExt buildObjectApiNameFilter(String apiName) {
        return FilterExt.of(Operator.EQ, IObjectData.DESCRIBE_API_NAME, apiName);
    }

    public static List<Wheres> handleWheres(List<Wheres> wheres, IObjectDescribe describe) {
        List<IFilter> filters = SearchTemplateExt.wheresToFilters(wheres);
        return SearchTemplateExt.filterToWheres(handleFilter(filters, describe, true));
    }

    public static List<IFilter> handleFilter(List<IFilter> filters, IObjectDescribe describe) {
        return handleFilter(filters, describe, false);
    }

    public static List<IFilter> handleFilter(List<IFilter> filters, IObjectDescribe describe, boolean includeUnActive) {
        List<IFilter> result = Lists.newArrayList();
        for (IFilter filter : filters) {
            // value_type 为 9 的函数过滤条件, 11 标签过滤条件，不需要校验
            if (UNCHECK_FILTER_VALUE_TYPES.contains(filter.getValueType())) {
                result.add(filter);
                continue;
            }
            // 使用主对象字段过滤从对象数据的筛选条件需要保留
            if (BooleanUtils.isTrue(filter.getIsMasterField())) {
                result.add(filter);
                continue;
            }
            if (StringUtils.contains(filter.getFieldName(), ".")) {
                result.add(filter);
                continue;
            }
            // 多对多字段支持搜索
            if (AppFrameworkConfig.isMultiField(filter.getFieldName())) {
                result.add(filter);
                continue;
            }
            // 过滤禁用删除的字段、和被删除的选项
            ObjectDescribeExt.of(describe)
                    .getFieldDescribeSilently(filter.getFieldName())
                    .filter(it -> includeUnActive || it.isActive())
                    .filter(fieldDescribe -> includeUnActive || handleSelectFilter(filter, fieldDescribe))
                    .ifPresent(x -> result.add(filter));

        }
        return result;
    }

    public static void handlePlainContentFilter(List<IFilter> filters) {
        if (CollectionUtils.empty(filters)) {
            return;
        }
        Optional<IFilter> firstPlantFilter = filters.stream().filter(x -> Objects.equals(FilterValueTypes.PLAIN_CONTENT, x.getValueType())).findFirst();
        if (firstPlantFilter.isPresent()) {
            filters.removeIf(x -> Objects.equals(FilterValueTypes.PLAIN_CONTENT, x.getValueType()));
            filters.add(firstPlantFilter.get());
        }
    }

    public static boolean handleSelectFilter(IFilter filter, IFieldDescribe fieldDescribe) {
        if (isSelectOneAndNotInOperators(filter, fieldDescribe, Operator.EQ, Operator.N, Operator.LIKE, Operator.NLIKE)) {
            return true;
        }
        SelectOne selectOne = (SelectOne) fieldDescribe;
        List<String> selectValue = selectOne.getSelectOptions().stream().map(ISelectOption::getValue).collect(Collectors.toList());
        filter.getFieldValues().removeIf(x -> !selectValue.contains(x));
        return CollectionUtils.notEmpty(filter.getFieldValues());
    }

    private static boolean isSelectOneAndNotInOperators(IFilter filter, IFieldDescribe fieldDescribe, Operator... operators) {
        if (FieldDescribeExt.of(fieldDescribe).isSelectOne() || FieldDescribeExt.of(fieldDescribe).isSelectMany()) {
            return !Sets.newHashSet(operators).contains(filter.getOperator());
        }
        return true;
    }

    public static IFilter buildGeoFilter(String apiName, String longitude, String latitude, String distance) {
        IFilter filter = new Filter();
        filter.setFieldName(apiName);
        filter.setFieldValues(Lists.newArrayList(String.format("(%s,%s)", longitude, latitude), distance));
        filter.setOperator(Operator.LTE);
        return filter;
    }

    public void renderVariable(Map<String, String> variablesResult) {
        boolean includeVariable = getIncludeVariable();
        if (!includeVariable) {
            return;
        }
        if (getFieldValues() == null || getFieldValues().isEmpty()) {
            return;
        }

        List<String> values = getFieldValues().stream().map(value -> {
            if (StringUtils.isBlank(value)) {
                return value;
            }
            return variablesResult.getOrDefault(value, value);
        }).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        setFieldValues(values);
        if (CollectionUtils.empty(values)) {
            // 替换后 fieldValues 为空，则改为比较是否为空
            setOperator(Operator.IS);
        }
    }

    public void replaceCurrentUser(String currentUserId) {
        // 子查询
        if (Objects.equals(10, getValueType())) {
            if (CollectionUtils.empty(getFieldValues())) {
                return;
            }
            // 递归处理子查询中 currentUser 的替换
            String json = getFieldValues().get(0);
            ISearchTemplate searchTemplate = new SearchTemplate();
            searchTemplate.fromJsonString(json);
            SearchTemplateExt templateExt = SearchTemplateExt.of(searchTemplate);
            List<IFilter> filters = templateExt.getFilters();
            filters.stream().map(FilterExt::of).forEach(it -> it.replaceCurrentUser(currentUserId));
            templateExt.setFilters(filters);
            // json 写回到 values 中
            getFieldValues().set(0, templateExt.toJsonString());
            return;
        }

        if (getFieldValues() == null) {
            setFieldValues(Collections.emptyList());
            return;
        }
        boolean includeVariable = getIncludeVariable();
        List<String> values = getFieldValues().stream().map(x -> {
            if (CRITERIA_ALL_OF_MINE.equals(x)) {
                return currentUserId;
            } else if (includeVariable && x.contains(CURRENT_LOGIN_USER_ID)) {
                return x.replaceAll(CURRENT_LOGIN_USER_ID, currentUserId);
            } else {
                return x;
            }
        }).collect(Collectors.toList());
        setFieldValues(values);
    }

    public String getVariableNameInValues() {
        String variable = getFieldValues().get(0);
        if (variable.contains(".")) {
            variable = StringUtils.substringAfter(variable, ".");
        }
        return StringUtils.replace(variable, "$", "");
    }

    public Tuple<Boolean, String> getVariableNameInValuesAndIsInMaster() {
        String variable = getFieldValues().get(0);
        String replace = StringUtils.replace(variable, "$", "");
        return replace.contains(".") ? Tuple.of(true, StringUtils.substringAfter(replace, "."))
                : Tuple.of(false, replace);
    }

    public Map<String, Set<String>> getVariableNames(String masterApiName, String subApiName) {
        Map<String, Set<String>> result = Maps.newHashMap();
        result.put(subApiName, Sets.newHashSet(getFieldName()));

        if (CollectionUtils.empty(getFieldValues())) {
            return result;
        }

        if (hasObjectVariableValueType()) {
            result.get(subApiName).add(StringUtils.replace(getFieldValues().get(0), "$", ""));
        } else if (hasRefObjectVariableValueType() || hasSpecialRefObjectVariable()) {
            String variable = getFieldValues().get(0);
            if (variable.contains(".")) {
                variable = StringUtils.replace(getFieldValues().get(0), "$", "");
                String[] variableNames = StringUtils.split(variable, ".");
                result.get(subApiName).add(StringUtils.substringBefore(variableNames[0], "__r"));

                if (variableNames.length > 1) {
                    result.put(masterApiName, Sets.newHashSet(variableNames[1]));
                }
            } else {
                result.put(masterApiName, Sets.newHashSet(StringUtils.replace(variable, "$", "")));
            }
        }

        return result;
    }

    public boolean hasConstantValueType() {
        return getValueType() == null || FilterValueTypes.CONSTANT == getValueType();
    }

    public boolean hasObjectVariableValueType() {
        return getValueType() != null && FilterValueTypes.OBJECT_VARIABLE == getValueType();
    }

    public boolean hasRefObjectVariableValueType() {
        return getValueType() != null && FilterValueTypes.REF_OBJECT_VARIABLE == getValueType();
    }

    public boolean hasRelatedChainObjectVariableValueType() {
        return getValueType() != null && FilterValueTypes.RELATED_CHAIN_OBJECT_VARIABLE == getValueType();
    }

    public boolean hasLookupI18NVariableValueType() {
        return getValueType() != null && FilterValueTypes.LOOKUP_I18N_VARIABLE == getValueType();
    }

    public boolean hasMasterFieldVariableValueType() {
        return getValueType() != null && FilterValueTypes.MASTER_FIELD_VARIABLE == getValueType();
    }

    public boolean hasNativeObjectVariable() {
        return getValueType() != null && FilterValueTypes.NATIVE_OBJECT_VARIABLE == getValueType();
    }

    /**
     * 特殊的四角、五角关系
     * ｜-----订单 ------订单产品
     * ｜                 ｜
     * 客户               ｜
     * ｜                 ｜
     * ｜_____ 回款 _____回款明细
     * <p>
     * 回款明细选择的订单产品，需要是回款对象关联的客户下的订单下的订单产品
     * order_id.account_id = $payment_id__r.account_id$
     * <p>
     * 解析逻辑：
     * $payment_id__r.account_id$ 与四角关系相同，需要新建编辑页面中的主对象数据。
     * order_id.account_id 处理逻辑需要设置 is_master_field 为true
     *
     * @return
     */
    public boolean hasSpecialRefObjectVariable() {
        return getValueType() != null && FilterValueTypes.SPECIAL_REF_OBJECT_VARIABLE == getValueType();
    }

    public void setMasterFieldWhenHasSpecialRefObjectVariable() {
        if (hasSpecialRefObjectVariable()) {
            setIsMasterField(true);
        }
    }

    /**
     * lookup 函数过滤条件
     *
     * @return
     */
    public boolean hasFunctionVariableValueType() {
        return getValueType() != null && FilterExt.FilterValueTypes.FUNCTION_VARIABLE == getValueType();
    }

    /**
     * 是四角关系
     *
     * @return
     */
    public boolean isRectangleRelation() {
        return hasRefObjectVariableValueType()
                && CollectionUtils.notEmpty(getFieldValues())
                && !FieldDescribeExt.isMultiCurrencyFields(getFieldName())
                && (Strings.isNullOrEmpty(getFieldValueType()) || IFieldType.OBJECT_REFERENCE.equals(getFieldValueType()))
                && getFieldValues().stream().anyMatch(filter -> StringUtils.contains(filter, "."));
    }

    /**
     * 是三角关系
     *
     * @return
     */
    public boolean isTriangleRelation() {
        return hasRefObjectVariableValueType()
                && CollectionUtils.notEmpty(getFieldValues())
                && !FieldDescribeExt.isMultiCurrencyFields(getFieldName())
                && (Strings.isNullOrEmpty(getFieldValueType()) || IFieldType.OBJECT_REFERENCE.equals(getFieldValueType()))
                && getFieldValues().stream().anyMatch(filter -> !StringUtils.contains(filter, "."));
    }

    public String getFieldApiNameWithPentagonRelation() {
        if (!hasRelatedChainObjectVariableValueType()) {
            throw new ValidateException(String.format("filter type not is pentagon relation, fieldName=>%s, valueType=>%s",
                    getFieldName(), getValueType()));
        }
        return StringUtils.substringBetween(StringUtils.substringBetween(getFieldValues().get(0), "->"), ".");
    }

    public String getFieldApiNameWithRectangleRelation() {
        if (!isRectangleRelation()) {
            throw new ValidateException(String.format("filter type not is rectangle relation, fieldName=>%s, valueType=>%s",
                    getFieldName(), getValueType()));
        }
        return StringUtils.substringAfter(StringUtils.replace(filter.getFieldValues().get(0), "$", ""), ".");
    }

    public String getFieldApiNameWithTriangleRelation() {
        if (!isTriangleRelation()) {
            throw new ValidateException(String.format("filter type not is triangle relation, fieldName=>%s, valueType=>%s",
                    getFieldName(), getValueType()));
        }
        return StringUtils.replace(filter.getFieldValues().get(0), "$", "");
    }

    public static boolean isDefaultAllSceneFilter(IFilter filter) {
        return equals(DEFAULT_ALL_SCENE_FILTER.getFilter(), filter);
    }

    public static boolean equals(IFilter a, IFilter b) {
        return a == b || (a != null && a.getFieldName().equals(b.getFieldName())
                && a.getFieldValues().equals(b.getFieldValues())
                && a.getOperator().equals(b.getOperator()));
    }

    public static boolean isDefaultSceneFilter(IFilter filter) {
        return DEFAULT_SCENE_FILTER.stream().anyMatch(x -> equals(x, filter));
    }

    public String getFieldApiName() {
        return StringUtils.substringBefore(getFieldName(), ".");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FilterExt filterExt = (FilterExt) o;
        if (!Objects.equals(getFieldName(), filterExt.getFieldName())) {
            return false;
        }
        if (!Objects.equals(getOperator(), filterExt.getOperator())) {
            return false;
        }
        if (!Objects.equals(getValueType(), filterExt.getValueType())) {
            return false;
        }
        if (!CollectionUtils.isEqual(CollectionUtils.nullToEmpty(getFieldValues()),
                CollectionUtils.nullToEmpty(filterExt.getFieldValues()))) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        List<Object> values = Lists.newArrayList();
        values.add(getFieldName());
        values.add(getOperator());
        values.add(getValueType());
        values.addAll(CollectionUtils.nullToEmpty(getFieldValues()));

        return Objects.hash(values.toArray());
    }

    public boolean isSystemFilter() {
        return IObjectDescribe.DEFINE_TYPE_SYSTEM.equals(getDefineType());
    }

    public boolean handleFilterValueReturnSkipData(Object value) {
        if (ObjectDataExt.isValueEmpty(value)) {
            return convertFilterWhenValueIsEmpty();
        }
        if (value instanceof List) {
            // 兼容类型 obj ==> string
            setFieldValues(((List<Object>) value).stream().map(String::valueOf).collect(Collectors.toList()));
        } else {
            setFieldValues(Lists.newArrayList(value.toString()));
        }
        filter.setValueType(FilterValueTypes.CONSTANT);
        return false;
    }

    private boolean convertFilterWhenValueIsEmpty() {
        switch (filter.getOperator()) {
            case EQ:
            case EQL:
            case IN:
            case LIKE:
            case HASANYOF:
                filter.setOperator(Operator.IS);
                break;
            case NIN:
            case N:
            case NLIKE:
            case NHASANYOF:
                filter.setOperator(Operator.ISN);
                break;
            default:
                //筛选变量时，变量为空，大于，小于，开始于等操作符会无法筛选，根据其业务属性，与空做该类筛选应该筛选不出数据，
                // 因此与元数据约定，遇到该类情况，将valueType设为14，fieldValues设为["1=2"]，valueType = 14时，只支持1=1，1=2。
                // 其他情况会有问题，慎用！！！
                filter.setFieldValues(Lists.newArrayList("1 = 2"));
                filter.setValueType(14);
                return true;
        }
        filter.setFieldValues(Collections.singletonList(null));
        filter.setValueType(FilterValueTypes.CONSTANT);
        return false;
    }

    public interface FilterValueTypes {
        int CONSTANT = 0;
        int OBJECT_VARIABLE = 1;
        int REF_OBJECT_VARIABLE = 2;
        // 五角关系
        int RELATED_CHAIN_OBJECT_VARIABLE = 8;

        int FUNCTION_VARIABLE = 9;
        int TAG_PARAMETER_VARIABLE = 11;
        //lookup中国际化相关的字段，比如币种、时区
        int LOOKUP_I18N_VARIABLE = 13;
        //联表查询
        int COMBINE_TABLE_VARIABLE = 10;

        int MASTER_FIELD_VARIABLE = 19;

        int NATIVE_OBJECT_VARIABLE = 20;
        //全字段搜索
        int PLAIN_CONTENT = 22;
        //没有任何一个匹配
        int N_HAS_ANY_OF = 14;

        int CONVERT_RULE_VARIABLE = 25;
        int SPECIAL_REF_OBJECT_VARIABLE = 26;
    }

    public static final class FiltersContainer {
        private final Stream<IFilter> filtersStream;

        private FiltersContainer(Stream<IFilter> filtersStream) {
            this.filtersStream = filtersStream;
        }

        public static FiltersContainer of(List<IFilter> filters) {
            return new FiltersContainer(CollectionUtils.nullToEmpty(filters).stream());
        }

        public FiltersContainer filter(Predicate<IFilter> predicate) {
            return new FiltersContainer(filtersStream.filter(predicate));
        }

        public Stream<List<IFilter>> group() {
            // 根据 filterGroup 分组，使用 treeMap 容器，保证有序
            return filtersStream.collect(Collectors.groupingBy(it -> Strings.nullToEmpty(it.getFilterGroup()), TreeMap::new,
                            Collectors.mapping(it -> it, Collectors.toList())))
                    .values()
                    .stream()
                    // filterGroup 为和前端的约定字段，不需要存库
                    .peek(filters -> filters.forEach(filter -> filter.setFilterGroup(null)));
        }
    }

    public static void convert2SystemZone(ObjectDescribeExt describeExt, IFilter filter) {
        convertDateFieldFilter(describeExt, filter, true);
    }

    public static void convert2CustomZone(ObjectDescribeExt describeExt, IFilter filter) {
        convertDateFieldFilter(describeExt, filter, false);
    }

    public static void convertDateFieldFilter(ObjectDescribeExt describeExt, IFilter filter, boolean toSystemZone) {
        if (Objects.nonNull(filter.getValueType()) && 0 != filter.getValueType()) {
            return;
        }
        Optional<IFieldDescribe> optional = describeExt.getFieldDescribeSilently(filter.getFieldName())
                .filter(it -> FieldDescribeExt.of(it).realTypeIsDate());
        if (!optional.isPresent()) {
            return;
        }

        List<String> fieldValues = filter.getFieldValues();
        if (CollectionUtils.empty(fieldValues)) {
            return;
        }
        Operator operator = filter.getOperator();
        if (Operator.IS.equals(operator) || Operator.ISN.equals(operator)) {
            return;
        }
        List<String> values = Lists.newArrayList();
        if (Operator.BETWEEN == operator || Operator.NBETWEEN == operator) {
            long leftValue = new BigDecimal(fieldValues.get(0)).longValue();
            long rightValue = new BigDecimal(fieldValues.get(1)).longValue();
            Pair<Long, Long> pair;
            if (toSystemZone) {
                pair = DateUtils.getStartTimeAndEndTime2SystemZone(leftValue, rightValue);
            } else {
                pair = DateUtils.getStartTimeAndEndTime2CustomZone(leftValue, rightValue);
            }
            values.add(String.valueOf(pair.getLeft()));
            values.add(String.valueOf(pair.getRight()));
        } else {
            long value = new BigDecimal(fieldValues.get(0)).longValue();
            values.add(getStartTime(value, toSystemZone));
        }
        filter.setFieldValues(values);
    }

    private static String getEndTime(long rightValue, boolean toSystemZone) {
        if (toSystemZone) {
            LocalDateTime localDateTime = DateTimeFormat.DATE_TIME.convertToLocalDateTime(rightValue, getUserTimeZone());
            long endTime = DateUtils.getDayEndTime(localDateTime.toLocalDate(), TimeZoneContext.DEFAULT_TIME_ZONE);
            return String.valueOf(endTime);
        }
        LocalDateTime localDateTime = DateTimeFormat.DATE_TIME.convertToLocalDateTime(rightValue, TimeZoneContext.DEFAULT_TIME_ZONE);
        long endTime = DateUtils.getDayEndTime(localDateTime.toLocalDate(), getUserTimeZone());
        return String.valueOf(endTime);
    }

    private static String getStartTime(long value, boolean toSystemZone) {
        if (toSystemZone) {
            return String.valueOf(DateUtils.convertDateValueToSystem(value));
        }
        return String.valueOf(DateUtils.convertDateValueToCustom(value));
    }

    public void validateFilter(IObjectDescribe describe) {
        validateFilter(describe, false);
    }

    public void validateFilter(IObjectDescribe describe, boolean validateFilter) {
        if (BooleanUtils.isTrue(this.getIsMasterField())) {
            //用主对象字段查从暂不校验
            return;
        }
        if (UNCHECK_FILTER_VALUE_TYPES.contains(filter.getValueType())) {
            return;
        }
        String fieldApiName = filter.getFieldName();
        // 暂时不校验,用别的对象下字段筛选的情况
        if (StringUtils.contains(fieldApiName, ".")) {
            validateLookupField(describe, fieldApiName, validateFilter);
            return;
        }
        List<String> values = CollectionUtils.nullToEmpty(filter.getFieldValues());
        if (Strings.isNullOrEmpty(fieldApiName) || CollectionUtils.empty(values)) {
            return;
        }

        Optional<IFieldDescribe> field = ObjectDescribeExt.of(describe).getFieldDescribeSilently(fieldApiName);
        if (!field.isPresent()) {
            if (StringUtils.endsWith(fieldApiName, "__c") || (validateFilter && !AppFrameworkConfig.ignoreFilterValidate(fieldApiName))) {
                throw new ValidateException(fieldApiName + I18NExt.text(I18NKey.NOT_EXIST));
            } else {
                return;
            }
        }

        if (validateFilter && validateIsIndex(describe.getApiName(), field.get())) {
            throw new ValidateException(I18NExt.text(I18NKey.FIELD_NOT_SUPPORT_FILTER, describe.getDisplayName() + "." + field.get().getLabel()));
        }

        if (Objects.isNull(getOperator())) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.FILTER_OPERATOR_WRONG,
                    I18NKey.FILTER_OPERATOR_WRONG, fieldApiName));
        }

        if (Objects.equals(filter.getOperator(), Operator.IS) || Objects.equals(filter.getOperator(), Operator.ISN)) {
            return;
        }

        if (hasConstantValueType() && Objects.equals(filter.getOperator(), Operator.BETWEEN)) {
            if (values.size() != 2) {
                throw new ValidateException(I18NExt.getOrDefault(I18NKey.FILTER_FIELD_VALUE_TYPE_WRONG,
                        I18NKey.FILTER_FIELD_VALUE_TYPE_WRONG, fieldApiName, values));
            }
        }


        if (ObjectDescribeExt.NUMBER_TYPE_FIELD.contains(field.get().getType()) && hasConstantValueType()) {
            String rawValue = String.valueOf(values.get(0));
            if (!NumberUtils.isCreatable(rawValue)) {
                log.warn("field value in filter wrong, filter:{}", filter);
                throw new ValidateException(I18NExt.getOrDefault(I18NKey.FILTER_FIELD_VALUE_TYPE_WRONG,
                        I18NKey.FILTER_FIELD_VALUE_TYPE_WRONG, fieldApiName, values.get(0)));
            }

            if (rawValue.endsWith(".")) {
                values.set(0, StringUtils.substring(rawValue, 0, rawValue.length() - 1));
            }
        }


        if (ObjectDescribeExt.DATE_TYPE_FIELD.contains(field.get().getType()) && hasConstantValueType()) {
            //values 里面是非非法的时间戳
            String rawValue = String.valueOf(values.get(0));
            if (!isValidTimeStamp(rawValue)) {
                throw new ValidateException(I18NExt.getOrDefault(I18NKey.FILTER_FIELD_VALUE_TYPE_WRONG,
                        I18NKey.FILTER_FIELD_VALUE_TYPE_WRONG, fieldApiName, values));
            }
        }

        //value_type 是3时，values里第二个值需要是个数字
        if (isDateTimeRange()) {
            if (values.size() < 1) {
                throw new ValidateException(I18NExt.getOrDefault(I18NKey.FILTER_FIELD_VALUE_TYPE_WRONG,
                        I18NKey.FILTER_FIELD_VALUE_TYPE_WRONG, fieldApiName, values));
            }

            if (Objects.equals(1, values.size())) {
                String num = values.get(0);
                if (!RANGE_TIME_VALUES.contains(num)) {
                    throw new ValidateException(I18NExt.getOrDefault(I18NKey.FILTER_FIELD_VALUE_TYPE_WRONG,
                            I18NKey.FILTER_FIELD_VALUE_TYPE_WRONG, fieldApiName, values));
                }
            } else {
                String num = values.get(1);
                if (Strings.isNullOrEmpty(num) || !NumberUtils.isDigits(num)) {
                    throw new ValidateException(I18NExt.getOrDefault(I18NKey.FILTER_FIELD_VALUE_TYPE_WRONG,
                            I18NKey.FILTER_FIELD_VALUE_TYPE_WRONG, fieldApiName, values));
                }
            }
        }
    }

    private boolean validateIsIndex(String describeApiName, IFieldDescribe field) {
        String fieldApiName = field.getApiName();
        if (field.isIndex()) {
            return false;
        }
        if (StringUtils.endsWith(fieldApiName, "__c")) {
            return true;
        }
        return ObjectDescribeExt.VALIDATE_IS_INDEX_TYPE_FIELD.contains(field.getType())
                && AppFrameworkConfig.validateIsIndexFields(describeApiName, fieldApiName);
    }

    private void validateLookupField(IObjectDescribe describe, String fieldApiName, boolean validateFilter) {
        if (!validateFilter || !StringUtils.contains(fieldApiName, ".")) {
            return;
        }
        fieldApiName = fieldApiName.substring(0, fieldApiName.indexOf("."));
        ObjectDescribeExt.of(describe).getActiveFieldDescribe(fieldApiName);
    }

    private boolean isDateTimeRange() {
        return Objects.nonNull(this.getValueType()) && Objects.equals(3, this.getValueType());
    }

    public static boolean isValidTimeStamp(String value) {
        if (Objects.isNull(value)) {
            return true;
        }

        if (value.isEmpty()) {
            return false;
        }

        if (Objects.equals(value.charAt(0), '-')) {
            return NumberUtils.isDigits(StringUtils.substring(value, 1));
        } else {
            return NumberUtils.isDigits(value);
        }
    }

    /**
     * like 操作,没有传值,认为是无效的条件
     *
     * @return false 无效的条件
     */
    public boolean isEffectiveFilter() {
        if (Operator.LIKE == getOperator()) {
            List<String> fieldValues = getFieldValues();
            return CollectionUtils.notEmpty(fieldValues) && !Strings.isNullOrEmpty(fieldValues.get(0));
        }
        return true;
    }

    public boolean usingLastModifiedTime() {
        return Objects.equals(getFieldName(), DBRecord.LAST_MODIFIED_TIME)
                || StringUtils.endsWith(getFieldApiName(), "." + DBRecord.LAST_MODIFIED_TIME);
    }
}
