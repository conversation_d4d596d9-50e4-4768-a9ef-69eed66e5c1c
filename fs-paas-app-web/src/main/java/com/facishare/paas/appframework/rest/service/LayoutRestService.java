package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.rest.dto.data.*;
import com.facishare.paas.appframework.rest.dto.layout.IsLayoutExist;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Craeted by Yuanxl on 2018/6/11
 */
@Slf4j
@Service
public class LayoutRestService {

    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private LayoutLogicService layoutLogicService;

    @Autowired
    @Qualifier("recordTypeLogicService")
    private RecordTypeLogicServiceImpl recordTypeLogicService;

    public findLayoutById.Result findLayoutById(findLayoutById.Arg arg, RequestContext requestContext) {
        findLayoutById.Result result = new findLayoutById.Result();

        ILayout layout = layoutLogicService.findLayoutById(arg.getLayoutId(), requestContext.getTenantId());
        result.setLayoutDocument(LayoutDocument.of(layout));
        return result;
    }

    public CreateLayout.Result createLayout(CreateLayout.Arg arg, RequestContext requestContext) {
        CreateLayout.Result result = new CreateLayout.Result();
        ILayout layout = makeLayout(arg.getLayoutJson(), requestContext);
        ILayout createdLayout = layoutLogicService.createLayout(requestContext.getUser(), layout);
        result.setLayoutDocument(LayoutDocument.of(createdLayout));
        return result;
    }

    public UpdateLayout.Result updateLayout(UpdateLayout.Arg arg, RequestContext context) {
        UpdateLayout.Result result = new UpdateLayout.Result();
        ILayout layout = makeLayout(arg.getLayoutJson(), context);
        ILayout createdLayout = layoutLogicService.updateLayout(context.getUser(), layout);
        result.setLayoutDocument(LayoutDocument.of(createdLayout));
        return result;
    }

    private ILayout makeLayout(String layoutJson, RequestContext context) {
        ILayout layout = new Layout();
        try {
            layout.fromJsonString(layoutJson);
            layout.setTenantId(context.getTenantId());
            layout.setCreatedBy(context.getUser().getUserId());
            layout.setLastModifiedBy(context.getUser().getUserId());
            return layout;
        } catch (Exception e) {
            log.warn("createLayout: 参数{}有误。", layoutJson);
            throw new ValidateException(I18N.text(I18NKey.MESSAGE_FORMAT_ERROR));
        }
    }

    public DeleteLayout.Result deleteLayout(DeleteLayout.Arg arg, RequestContext context) {
        DeleteLayout.Result result = new DeleteLayout.Result();
        ILayout deleteLayout = layoutLogicService.deleteLayout(context.getUser(), arg.getLayoutId());
        result.setLayoutDocument(LayoutDocument.of(deleteLayout));
        return result;
    }

    public FindLayoutByApiName.Result findLayoutByApiNameAndDescribeApiName(FindLayoutByApiName.Arg arg,
                                                                            RequestContext context) {
        FindLayoutByApiName.Result result = new FindLayoutByApiName.Result();
        ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), arg.getLayoutApiName(),
                arg.getDescObjApiName());
        result.setLayoutDocument(LayoutDocument.of(layout));
        return result;
    }

    public FindLayoutByDescApiName.Result findLayoutByDescApiName(FindLayoutByDescApiName.Arg arg,
                                                                  RequestContext context) {
        FindLayoutByDescApiName.Result result = new FindLayoutByDescApiName.Result();
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        List<ILayout> layoutList = layoutLogicService.findLayoutByObjectApiName(layoutContext,
                arg.getDescApiName());
        List<LayoutDocument> resultList = layoutList.stream().map(x -> LayoutDocument.of(x))
                .collect(Collectors.toList());
        result.setLayoutDocuments(resultList);
        return result;
    }

    public FindDefaultLayout.Result findDefaultLayout(FindDefaultLayout.Arg arg, RequestContext context) {
        FindDefaultLayout.Result result = new FindDefaultLayout.Result();
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        // 如果LayoutType是list，则查询移动端列表页布局
        if (ILayout.LIST_LAYOUT_TYPE.equals(arg.getLayoutType())) {
            IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getDescApiName());
            List<ILayout> mobileListLayout = layoutLogicService.findMobileListLayout(layoutContext, describe, true);
            if (CollectionUtils.empty(mobileListLayout)) {
                return result;
            }
            result.setLayoutDocument(LayoutDocument.of(mobileListLayout.get(0)));
            return result;
        }
        ILayout layout = layoutLogicService.findDefaultLayout(layoutContext, arg.getLayoutType(), arg.getDescApiName());
        result.setLayoutDocument(LayoutDocument.of(layout));
        return result;
    }

    public FindLayoutByRecordType.Result findLayoutByRecordType(FindLayoutByRecordType.Arg arg,
                                                                RequestContext requestContext) {
        FindLayoutByRecordType.Result result = new FindLayoutByRecordType.Result();
        if (null == arg.getDescribeAPIName() || null == arg.getRecordAPIName()) {
            throw new ValidateException("parameter is null");
        }

        IObjectDescribe describe = describeLogicService.findObject(requestContext.getTenantId(), arg.getDescribeAPIName());
        RecordTypeResult typeResult = recordTypeLogicService.findLayoutByRecordType(describe,
                arg.getRecordAPIName(), requestContext.getUser(), arg.getLayoutType());
        //兼容处理
        if (null == typeResult || null == typeResult.getLayout()) {
            LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(requestContext.getUser(), arg.getAppId());
            ILayout iLayout = layoutLogicService.findDefaultLayout(layoutContext,
                    ILayout.DETAIL_LAYOUT_TYPE, describe.getApiName());
            result.setLayoutDocument(LayoutDocument.of(iLayout));
        } else {
            result.setLayoutDocument(LayoutDocument.of(typeResult.getLayout()));
        }

        return result;
    }

    public IsLayoutExist.Result isLayoutExist(IsLayoutExist.Arg arg, RequestContext requestContext) {
        if (Strings.isNullOrEmpty(arg.getObjectApiName()) || CollectionUtils.empty(arg.getLayoutApiNames())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        Map<String, Layout> layoutMap = layoutLogicService.findLayoutByApiNames(requestContext.getTenantId(),
                arg.getLayoutApiNames(), arg.getObjectApiName());
        return IsLayoutExist.Result.builder().existLayoutApiNames(Lists.newArrayList(layoutMap.keySet())).build();
    }

}
