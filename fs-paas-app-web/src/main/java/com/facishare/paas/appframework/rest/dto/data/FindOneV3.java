package com.facishare.paas.appframework.rest.dto.data;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.*;

import java.util.Objects;

/**
 * Created by zhouwr on 2022/5/5.
 */
public interface FindOneV3 {

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Arg extends FindBySearchTemplateQueryV3.Arg {
        @Override
        public void processSearchQuery(SearchTemplateQuery query) {
            super.processSearchQuery(query);
            query.setNeedReturnCountNum(false);
            query.setLimit(1);
        }

        @Override
        public MetaDataFindService.QueryContext buildQueryContext() {
            MetaDataFindService.QueryContext queryContext = super.buildQueryContext();
            //findOne默认返回富文本的原值
            if (Objects.isNull(getSearchRichTextExtra())) {
                queryContext.setSearchRichTextExtra(true);
            }
            return queryContext;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private ObjectDataDocument objectData;
    }
}
