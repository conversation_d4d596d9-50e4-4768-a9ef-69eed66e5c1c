package com.facishare.paas.appframework.metadata;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.bizconf.arg.QueryConfigDefArg;
import com.fxiaoke.bizconf.bean.BizType;
import com.fxiaoke.bizconf.bean.ConfigDefPojo;
import com.fxiaoke.bizconf.bean.ConfigPojo;
import com.fxiaoke.bizconf.factory.BizConfClient;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * create by z<PERSON><PERSON> on 2020/09/04
 */
@Slf4j
@Primary
@Service
public class BizConfServiceImpl implements BizConfService {
    public static final String SUPPORT_MANUAL_ADD = "support_manual_add";
    public static final String SUPPORT_TEAM_ADD_CREATOR = "support_team_add_creator";

    @Autowired
    private BizConfClient bizConfClient;

    @Override
    public List<ConfigDefPojo> queryConfigDef(String tenantId, String describeApiName, String bizType) {
        QueryConfigDefArg queryConfigDefArg = QueryConfigDefArg.builder()
                .tenantId(tenantId)
                .describeApiName(describeApiName)
                .bizType(convertBizType(bizType))
                .build();
        try {
            List<ConfigDefPojo> configDefList = bizConfClient.queryConfigDef(queryConfigDefArg);
            //按照display_order排一下序
            if (CollectionUtils.notEmpty(configDefList)) {
                configDefList.sort(Comparator.comparing(o -> Optional.ofNullable(o.getDisplayOrder()).orElse(0)));
            }
            return configDefList;
        } catch (FRestClientException e) {
            log.warn("queryConfigDef failed,tenantId:{},describeApiName:{},bizType:{}", tenantId, describeApiName, bizType);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public List<ConfigPojo> queryConfigData(String tenantId, String configCode, List<String> bizTypeValueList) {
        try {
            return bizConfClient.queryConfigByMultipleKey(tenantId, DefObjConstants.PACKAGE_NAME_CRM, configCode, Sets.newHashSet(bizTypeValueList));
        } catch (FRestClientException e) {
            log.warn("queryConfigData failed, tenantId:{},configCode:{},bizTypeValueList:{}", tenantId, configCode, bizTypeValueList);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public Map<String, ConfigPojo> queryConfigDataIncludeDefault(String tenantId, String describeApiName, String bizType,
                                                                 String configCode, List<String> bizTypeValueList) {
        List<ConfigDefPojo> configDefList = queryConfigDef(tenantId, describeApiName, bizType);
        if (CollectionUtils.empty(configDefList)) {
            return Collections.emptyMap();
        }
        Map<String, ConfigDefPojo> configDefMap = configDefList.stream().collect(Collectors.toMap(ConfigDefPojo::getConfigCode, it -> it));
        ConfigDefPojo configDefPojo = configDefMap.get(configCode);
        if (Objects.isNull(configDefPojo)) {
            return Collections.emptyMap();
        }

        List<ConfigPojo> configPojoList = queryConfigData(tenantId, configCode, bizTypeValueList);
        Map<String, ConfigPojo> dbConfigDataMap = configPojoList.stream().collect(Collectors.toMap(ConfigPojo::getAssistantKey, it -> it));

        Map<String, ConfigPojo> resultMap = Maps.newHashMap();
        for (String bizTypeValue : bizTypeValueList) {
            ConfigPojo configPojo = dbConfigDataMap.get(bizTypeValue);
            if (Objects.isNull(configPojo)) {
                configPojo = ConfigPojo.builder()
                        .tenantId(tenantId)
                        .pkg(DefObjConstants.PACKAGE_NAME_CRM)
                        .assistantKey(bizTypeValue)
                        .key(configCode)
                        .configValue(configDefPojo.getDefaultValue())
                        .build();
            }
            resultMap.put(bizTypeValue, configPojo);
        }
        return resultMap;
    }

    private BizType convertBizType(String bizType) {
        return Arrays.stream(BizType.values()).filter(x -> x.getValue().equals(bizType)).findFirst()
                .orElseThrow(() -> new MetaDataBusinessException("bizType:{} is unsupported"));
    }

    public static String getSupportManualAddConfigCode(@NonNull String describeApiName) {
        return String.format("%s_%s", describeApiName, SUPPORT_MANUAL_ADD);
    }

    public static String getSupportTeamAddCreatorConfigCode(@NonNull String describeApiName) {
        return String.format("%s_%s", describeApiName, SUPPORT_TEAM_ADD_CREATOR);
    }
}
