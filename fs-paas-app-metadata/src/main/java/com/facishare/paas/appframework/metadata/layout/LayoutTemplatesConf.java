package com.facishare.paas.appframework.metadata.layout;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.fasterxml.jackson.databind.JsonNode;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/3/2
 */
@Slf4j
public final class LayoutTemplatesConf {
    private static Map<String, List<LayoutTemplatesConfItem>> TEMPLATES = Maps.newHashMap();

    public static final String GRAY_KEY = "gray_ei";

    public static final String Un_USE_SCENE = "unUseScene";

    static {
        ConfigFactory.getConfig("card_template_config", config -> {
            log.warn("reload config card_template_config");
            Map<String, List<LayoutTemplatesConfItem>> map = Maps.newHashMap();
            JsonNode layoutTemplates = JacksonUtils.readTree(config.getString());
            if (Objects.isNull(layoutTemplates)) {
                return;
            }
            for (JsonNode layoutTemplate : layoutTemplates) {
                String describeApiName = JacksonUtils.convertValue(layoutTemplate.get(IFieldDescribe.DESCRIBE_API_NAME), String.class);
                if (Strings.isNullOrEmpty(describeApiName)) {
                    describeApiName = DefObjConstants.ALLOBJ;
                }
                map.computeIfAbsent(describeApiName, x -> Lists.newArrayList())
                        .add(new LayoutTemplatesConfItem(layoutTemplate));
            }
            TEMPLATES = map;
        });
    }

    public static List<Map<String, Object>> getLayoutTemplates(User user, String describeApiName, String business, Integer cardStyle) {
        List<Map<String, Object>> result = Lists.newArrayList();
        List<LayoutTemplatesConfItem> templates = TEMPLATES.get(describeApiName);
        if (CollectionUtils.notEmpty(templates)) {
            result.addAll(LayoutTemplatesConfItem.getAllowTemplates(user.getTenantId(), templates, business, cardStyle));
        }

        templates = TEMPLATES.get(DefObjConstants.ALLOBJ);
        if (CollectionUtils.notEmpty(templates)) {
            result.addAll(LayoutTemplatesConfItem.getAllowTemplates(user.getTenantId(), templates, business, cardStyle));
        }
        return result;
    }

    private static Map<String, Object> restTemplateName(Map<String, Object> template) {
        String nameI18nKey = (String) template.get("templateNameI18nKey");
        if (StringUtils.isBlank(nameI18nKey)) {
            return template;
        }
        String transValue = I18N.text(nameI18nKey);
        if (StringUtils.isBlank(transValue)) {
            return template;
        }
        Map<String, Object> newTemplate = Maps.newHashMap();
        newTemplate.putAll(template);
        newTemplate.put("label", transValue);
        return newTemplate;
    }

    private static class LayoutTemplatesConfItem {
        private final GrayRule grayRule;
        private final Map<String, Object> template;
        private final List<String> business;

        public LayoutTemplatesConfItem(JsonNode layoutTemplate) {
            this.grayRule = getGrayRule(layoutTemplate);
            this.template = ImmutableMap.copyOf(JacksonUtils.convertValue(layoutTemplate, Map.class));
            if (Objects.isNull(layoutTemplate.get(Un_USE_SCENE))) {
                this.business = Lists.newArrayList();
            } else {
                this.business = ImmutableList.copyOf(JacksonUtils.convertValue(layoutTemplate.get(Un_USE_SCENE), List.class));
            }
        }

        private static List<Map<String, Object>> getAllowTemplates(String ei, List<LayoutTemplatesConfItem> layoutTemplatesConfItems, String business, Integer cardStyle) {
            if (CollectionUtils.empty(layoutTemplatesConfItems)) {
                return Lists.newArrayList();
            }
            return layoutTemplatesConfItems.stream()
                    .filter(it -> it.isAllow(ei) && it.isAllowBusiness(business) && it.isAllowCardStyle(cardStyle))
                    .map(LayoutTemplatesConfItem::getTemplate)
                    .map(LayoutTemplatesConf::restTemplateName)
                    .collect(Collectors.toList());
        }


        private boolean isAllowCardStyle(Integer cardStyle) {
            if (Objects.isNull(cardStyle)) {
                return true;
            }
            //卡片模版中没有指定cardStyle，则默认是一行多列
            if (Objects.isNull(template.get("card_style"))) {
                return CardStyle.MULTI_COLUMN.code.equals(cardStyle);
            }
            return cardStyle.equals(template.get("card_style"));
        }

        private boolean isAllowBusiness(String business) {
            if (StringUtils.isBlank(business)) {
                business = "abstract";
            }
            return !this.business.contains(business);
        }

        public boolean isAllow(String ei) {
            return Objects.isNull(grayRule) || grayRule.isAllow(ei);
        }

        public Map<String, Object> getTemplate() {
            return template;
        }

        private GrayRule getGrayRule(JsonNode layoutTemplate) {
            String gray = JacksonUtils.convertValue(layoutTemplate.get(GRAY_KEY), String.class);
            if (Strings.isNullOrEmpty(gray)) {
                return null;
            }
            return new GrayRule(gray);
        }
    }


    enum CardStyle {
        MULTI_COLUMN(1),
        FULL_COLUMN(2);

        private Integer code;

        CardStyle(Integer code) {
            this.code = code;
        }

    }
}
