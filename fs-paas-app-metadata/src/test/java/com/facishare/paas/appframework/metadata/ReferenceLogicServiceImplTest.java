package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.ReferenceServiceProxy;
import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReferenceLogicServiceImplTest {

    @Mock
    private ReferenceServiceProxy referenceServiceProxy;

    @InjectMocks
    private ReferenceLogicServiceImpl referenceLogicService;

    private String testTenantId;
    private String testTargetType;
    private String testTargetValue;
    private String testSourceType;
    private String testSourceValue;
    private List<String> testSourceValues;
    private List<String> testTargetValues;
    private List<ReferenceData> testReferenceDataList;
    private User testUser;

    @BeforeEach
    void setUp() {
        testTenantId = "74255";
        testTargetType = "FUNCTION";
        testTargetValue = "testTarget";
        testSourceType = "FIELD";
        testSourceValue = "testSource";
        testSourceValues = Lists.newArrayList("source1", "source2");
        testTargetValues = Lists.newArrayList("target1", "target2");

        ReferenceData mockReferenceData = new ReferenceData();
        mockReferenceData.setSourceType(testSourceType);
        mockReferenceData.setSourceValue(testSourceValue);
        mockReferenceData.setTargetType(testTargetType);
        mockReferenceData.setTargetValue(testTargetValue);
        testReferenceDataList = Lists.newArrayList(mockReferenceData);

        testUser = new User();
        testUser.setTenantId(testTenantId);
        testUser.setUserId("testUserId");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据目标查找引用的正常场景，验证基本的引用查询功能
     */
    @Test
    @DisplayName("正常场景 - 根据目标查找引用成功")
    void testFindReferenceByTarget_Success() {
        // 准备测试数据
        FindReferenceByTarget.Result mockResult = mock(FindReferenceByTarget.Result.class);
        when(mockResult.getValues()).thenReturn(testReferenceDataList);

        when(referenceServiceProxy.findByTarget(any(HashMap.class), eq(testTargetType), 
                eq(testTargetValue), eq("false"), eq("1000")))
                .thenReturn(mockResult);

        // 执行被测试方法
        List<ReferenceData> result = referenceLogicService.findReferenceByTarget(
                testTenantId, testTargetType, testTargetValue);

        // 验证结果
        assertNotNull(result);
        assertEquals(testReferenceDataList, result);
        verify(referenceServiceProxy).findByTarget(any(HashMap.class), eq(testTargetType), 
                eq(testTargetValue), eq("false"), eq("1000"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带前缀匹配参数的根据目标查找引用的正常场景
     */
    @Test
    @DisplayName("正常场景 - 带前缀匹配参数的根据目标查找引用成功")
    void testFindReferenceByTargetWithPrefixMatch_Success() {
        // 准备测试数据
        boolean isPrefixMatch = true;
        FindReferenceByTarget.Result mockResult = mock(FindReferenceByTarget.Result.class);
        when(mockResult.getValues()).thenReturn(testReferenceDataList);

        when(referenceServiceProxy.findByTarget(any(HashMap.class), eq(testTargetType), 
                eq(testTargetValue), eq("true"), eq("1000")))
                .thenReturn(mockResult);

        // 执行被测试方法
        List<ReferenceData> result = referenceLogicService.findReferenceByTarget(
                testTenantId, testTargetType, testTargetValue, isPrefixMatch);

        // 验证结果
        assertNotNull(result);
        assertEquals(testReferenceDataList, result);
        verify(referenceServiceProxy).findByTarget(any(HashMap.class), eq(testTargetType), 
                eq(testTargetValue), eq("true"), eq("1000"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带限制数量参数的根据目标查找引用的正常场景
     */
    @Test
    @DisplayName("正常场景 - 带限制数量参数的根据目标查找引用成功")
    void testFindReferenceByTargetWithLimit_Success() {
        // 准备测试数据
        boolean isPrefixMatch = true;
        int limit = 500;
        FindReferenceByTarget.Result mockResult = mock(FindReferenceByTarget.Result.class);
        when(mockResult.getValues()).thenReturn(testReferenceDataList);

        when(referenceServiceProxy.findByTarget(any(HashMap.class), eq(testTargetType), 
                eq(testTargetValue), eq("true"), eq("500")))
                .thenReturn(mockResult);

        // 执行被测试方法
        List<ReferenceData> result = referenceLogicService.findReferenceByTarget(
                testTenantId, testTargetType, testTargetValue, isPrefixMatch, limit);

        // 验证结果
        assertNotNull(result);
        assertEquals(testReferenceDataList, result);
        verify(referenceServiceProxy).findByTarget(any(HashMap.class), eq(testTargetType), 
                eq(testTargetValue), eq("true"), eq("500"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除并创建引用的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除并创建引用成功")
    void testDeleteAndCreateReference_Success() {
        // 准备测试数据
        DeleteAndCreateReference.Result mockResult = mock(DeleteAndCreateReference.Result.class);
        when(referenceServiceProxy.deleteAndCreate(any(HashMap.class), any(DeleteAndCreateReference.Arg.class)))
                .thenReturn(mockResult);

        // 执行被测试方法
        referenceLogicService.deleteAndCreateReference(testTenantId, testReferenceDataList);

        // 验证结果
        verify(referenceServiceProxy).deleteAndCreate(any(HashMap.class), any(DeleteAndCreateReference.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除并创建引用时，引用列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 删除并创建引用时引用列表为空直接返回")
    void testDeleteAndCreateReference_EmptyList() {
        // 准备测试数据
        List<ReferenceData> emptyList = Lists.newArrayList();

        // 执行被测试方法
        referenceLogicService.deleteAndCreateReference(testTenantId, emptyList);

        // 验证结果
        verify(referenceServiceProxy, never()).deleteAndCreate(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除引用的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除引用成功")
    void testDeleteReference_Success() {
        // 准备测试数据
        DeleteReference.Result mockResult = mock(DeleteReference.Result.class);
        when(referenceServiceProxy.deleteReference(testTenantId, testSourceType, testSourceValue))
                .thenReturn(mockResult);

        // 执行被测试方法
        referenceLogicService.deleteReference(testTenantId, testSourceType, testSourceValue);

        // 验证结果
        verify(referenceServiceProxy).deleteReference(testTenantId, testSourceType, testSourceValue);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量删除引用的正常场景
     */
    @Test
    @DisplayName("正常场景 - 批量删除引用成功")
    void testBatchDeleteReference_Success() {
        // 准备测试数据
        BatchDeleteReference.Result mockResult = mock(BatchDeleteReference.Result.class);
        when(referenceServiceProxy.batchDeleteReference(eq(testTenantId), any(BatchDeleteReference.Arg.class)))
                .thenReturn(mockResult);

        // 执行被测试方法
        referenceLogicService.batchDeleteReference(testTenantId, testSourceType, testSourceValues);

        // 验证结果
        verify(referenceServiceProxy).batchDeleteReference(eq(testTenantId), any(BatchDeleteReference.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量删除引用时，源值列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 批量删除引用时源值列表为空直接返回")
    void testBatchDeleteReference_EmptySourceValues() {
        // 准备测试数据
        List<String> emptySourceValues = Lists.newArrayList();

        // 执行被测试方法
        referenceLogicService.batchDeleteReference(testTenantId, testSourceType, emptySourceValues);

        // 验证结果
        verify(referenceServiceProxy, never()).batchDeleteReference(anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据目标列表查询引用的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据目标列表查询引用成功")
    void testQueryByTargetList_Success() {
        // 准备测试数据
        QueryByTargetList.Result mockResult = mock(QueryByTargetList.Result.class);
        when(mockResult.getValues()).thenReturn(testReferenceDataList);

        when(referenceServiceProxy.queryByTargetList(eq(testTenantId), any(QueryByTargetList.Arg.class)))
                .thenReturn(mockResult);

        // 执行被测试方法
        List<ReferenceData> result = referenceLogicService.queryByTargetList(testTenantId, testTargetValues);

        // 验证结果
        assertNotNull(result);
        assertEquals(testReferenceDataList, result);
        verify(referenceServiceProxy).queryByTargetList(eq(testTenantId), any(QueryByTargetList.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据目标列表查询引用时，目标值列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 根据目标列表查询引用时目标值列表为空返回空列表")
    void testQueryByTargetList_EmptyTargetValues() {
        // 准备测试数据
        List<String> emptyTargetValues = Lists.newArrayList();

        // 执行被测试方法
        List<ReferenceData> result = referenceLogicService.queryByTargetList(testTenantId, emptyTargetValues);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(referenceServiceProxy, never()).queryByTargetList(anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据目标列表查询引用时，服务返回null的场景
     */
    @Test
    @DisplayName("正常场景 - 根据目标列表查询引用时服务返回null返回空列表")
    void testQueryByTargetList_ServiceReturnsNull() {
        // 准备测试数据
        QueryByTargetList.Result mockResult = mock(QueryByTargetList.Result.class);
        when(mockResult.getValues()).thenReturn(null);

        when(referenceServiceProxy.queryByTargetList(eq(testTenantId), any(QueryByTargetList.Arg.class)))
                .thenReturn(mockResult);

        // 执行被测试方法
        List<ReferenceData> result = referenceLogicService.queryByTargetList(testTenantId, testTargetValues);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(referenceServiceProxy).queryByTargetList(eq(testTenantId), any(QueryByTargetList.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除并创建关系的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除并创建关系成功")
    void testDeleteAndCreateRelation_Success() {
        // 准备测试数据
        DeleteAndCreateReference.Result mockResult = mock(DeleteAndCreateReference.Result.class);
        when(referenceServiceProxy.deleteAndCreate(any(HashMap.class), any(DeleteAndCreateReference.Arg.class)))
                .thenReturn(mockResult);

        // 执行被测试方法
        referenceLogicService.deleteAndCreateRelation(testUser, testReferenceDataList);

        // 验证结果
        verify(referenceServiceProxy).deleteAndCreate(any(HashMap.class), any(DeleteAndCreateReference.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除并创建关系时，引用列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 删除并创建关系时引用列表为空直接返回")
    void testDeleteAndCreateRelation_EmptyList() {
        // 准备测试数据
        List<ReferenceData> emptyList = Lists.newArrayList();

        // 执行被测试方法
        referenceLogicService.deleteAndCreateRelation(testUser, emptyList);

        // 验证结果
        verify(referenceServiceProxy, never()).deleteAndCreate(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除并创建关系时，引用列表为null的场景
     */
    @Test
    @DisplayName("正常场景 - 删除并创建关系时引用列表为null直接返回")
    void testDeleteAndCreateRelation_NullList() {
        // 执行被测试方法
        referenceLogicService.deleteAndCreateRelation(testUser, null);

        // 验证结果
        verify(referenceServiceProxy, never()).deleteAndCreate(any(), any());
    }
}
