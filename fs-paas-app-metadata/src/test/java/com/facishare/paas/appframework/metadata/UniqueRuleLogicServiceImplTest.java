package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.dto.UniqueQuery;
import com.facishare.paas.appframework.metadata.dto.UniqueRuleQuery;
import com.facishare.paas.appframework.metadata.dto.UniqueRuleSearchResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService;
import com.facishare.paas.foundation.boot.exception.ResponsiveException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IUniqueRuleService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UniqueRuleLogicServiceImplTest {

    @Mock
    private IUniqueRuleService uniqueRuleService;

    @Mock
    private DuplicateSearchProxy duplicateSearchProxy;

    @Mock
    private RedisDao redisDao;

    @Mock
    private SwitchCacheService switchCacheService;

    @InjectMocks
    private UniqueRuleLogicServiceImpl uniqueRuleLogicService;

    private String testTenantId;
    private String testDescribeApiName;
    private IUniqueRule testUniqueRule;
    private IObjectDescribe mockObjectDescribe;
    private User testUser;
    private List<IObjectData> testObjectDataList;

    @BeforeEach
    void setUp() {
        testTenantId = "74255";
        testDescribeApiName = "TestObj";

        testUniqueRule = mock(IUniqueRule.class);
        when(testUniqueRule.getDescribeApiName()).thenReturn(testDescribeApiName);
        when(testUniqueRule.getTenantId()).thenReturn(testTenantId);

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn(testDescribeApiName);

        testUser = new User();
        testUser.setTenantId(testTenantId);
        testUser.setUserId("testUserId");

        IObjectData testData = new ObjectData();
        testData.setId("testDataId");
        testObjectDataList = Lists.newArrayList(testData);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新唯一性规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 创建或更新唯一性规则成功")
    void testCreateOrUpdate_Success() {
        try (MockedStatic<UniqueRuleExt> mockedUniqueRuleExt = mockStatic(UniqueRuleExt.class)) {
            // 准备测试数据
            UniqueRuleExt mockUniqueRuleExt = mock(UniqueRuleExt.class);
            mockedUniqueRuleExt.when(() -> UniqueRuleExt.of(testUniqueRule))
                    .thenReturn(mockUniqueRuleExt);

            when(uniqueRuleService.createOrUpdate(testTenantId, testUniqueRule))
                    .thenReturn(testUniqueRule);

            // 执行被测试方法
            IUniqueRule result = uniqueRuleLogicService.createOrUpdate(testTenantId, testUniqueRule);

            // 验证结果
            assertNotNull(result);
            assertEquals(testUniqueRule, result);
            assertEquals(testTenantId, testUniqueRule.getTenantId());
            verify(mockUniqueRuleExt).checkParam();
            verify(uniqueRuleService).createOrUpdate(testTenantId, testUniqueRule);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新唯一性规则时，规则为null的场景
     */
    @Test
    @DisplayName("异常场景 - 创建或更新唯一性规则时规则为null抛出ValidateException")
    void testCreateOrUpdateThrowsValidateException_NullRule() {
        try (MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class)) {
            // 准备测试数据
            mockedI18NExt.when(() -> I18NExt.text(I18NKey.PARAM_ERROR))
                    .thenReturn("参数错误");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                uniqueRuleLogicService.createOrUpdate(testTenantId, null);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(uniqueRuleService, never()).createOrUpdate(anyString(), any(IUniqueRule.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新唯一性规则时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 创建或更新唯一性规则时服务抛出ResponsiveException")
    void testCreateOrUpdateThrowsMetaDataBusinessException_ServiceException() {
        try (MockedStatic<UniqueRuleExt> mockedUniqueRuleExt = mockStatic(UniqueRuleExt.class)) {
            // 准备测试数据
            UniqueRuleExt mockUniqueRuleExt = mock(UniqueRuleExt.class);
            mockedUniqueRuleExt.when(() -> UniqueRuleExt.of(testUniqueRule))
                    .thenReturn(mockUniqueRuleExt);

            ResponsiveException serviceException = new ResponsiveException("Service error");
            when(uniqueRuleService.createOrUpdate(testTenantId, testUniqueRule))
                    .thenThrow(serviceException);

            // 执行并验证异常
            MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
                uniqueRuleLogicService.createOrUpdate(testTenantId, testUniqueRule);
            });

            // 验证异常信息
            assertNotNull(exception);
            assertEquals("Service error", exception.getMessage());
            verify(uniqueRuleService).createOrUpdate(testTenantId, testUniqueRule);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据对象描述API名称查找唯一性规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据对象描述API名称查找唯一性规则成功")
    void testFindByDescribeApiName_Success() {
        // 准备测试数据
        when(uniqueRuleService.findByDescribeApiName(testTenantId, testDescribeApiName))
                .thenReturn(testUniqueRule);

        // 执行被测试方法
        IUniqueRule result = uniqueRuleLogicService.findByDescribeApiName(testTenantId, testDescribeApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals(testUniqueRule, result);
        verify(uniqueRuleService).findByDescribeApiName(testTenantId, testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试过滤字段的正常场景，验证黑名单和白名单过滤逻辑
     */
    @Test
    @DisplayName("正常场景 - 过滤字段成功")
    void testFilterField_Success() {
        // 准备测试数据
        IFieldDescribe mockField1 = mock(IFieldDescribe.class);
        when(mockField1.getApiName()).thenReturn("testField1");
        when(mockField1.getType()).thenReturn("TEXT");
        when(mockField1.isActive()).thenReturn(true);
        when(mockField1.getDefineType()).thenReturn(IFieldDescribe.DEFINE_TYPE_CUSTOM);

        IFieldDescribe mockField2 = mock(IFieldDescribe.class);
        when(mockField2.getApiName()).thenReturn("testField2");
        when(mockField2.getType()).thenReturn("NUMBER");
        when(mockField2.isActive()).thenReturn(true);
        when(mockField2.getDefineType()).thenReturn(IFieldDescribe.DEFINE_TYPE_CUSTOM);

        List<IFieldDescribe> fieldDescribes = Lists.newArrayList(mockField1, mockField2);
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(fieldDescribes);

        // 执行被测试方法
        List<IFieldDescribe> result = uniqueRuleLogicService.filterField(mockObjectDescribe);

        // 验证结果
        assertNotNull(result);
        verify(mockObjectDescribe).getFieldDescribes();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找重复数据的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找重复数据成功")
    void testFindDuplicateData_Success() {
        // 准备测试数据
        when(uniqueRuleService.findByDescribeApiName(testUser.getTenantId(), testDescribeApiName))
                .thenReturn(testUniqueRule);

        UniqueRuleQuery.Result mockResult = mock(UniqueRuleQuery.Result.class);
        when(mockResult.isEmpty()).thenReturn(true);

        // 由于方法内部逻辑复杂，我们主要验证服务调用
        when(duplicateSearchProxy.findDuplicateData(anyString(), any(UniqueRuleQuery.Arg.class), 
                any(IObjectDescribe.class), any(IUniqueRule.class)))
                .thenReturn(mockResult);

        // 执行被测试方法
        List<UniqueRuleSearchResult.DuplicateData> result = uniqueRuleLogicService.findDuplicateData(
                testUser, testObjectDataList, mockObjectDescribe);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(uniqueRuleService).findByDescribeApiName(testUser.getTenantId(), testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找重复数据时，唯一性规则不存在的场景
     */
    @Test
    @DisplayName("正常场景 - 查找重复数据时唯一性规则不存在返回空列表")
    void testFindDuplicateData_UniqueRuleNotExists() {
        // 准备测试数据
        when(uniqueRuleService.findByDescribeApiName(testUser.getTenantId(), testDescribeApiName))
                .thenReturn(null);

        // 执行被测试方法
        List<UniqueRuleSearchResult.DuplicateData> result = uniqueRuleLogicService.findDuplicateData(
                testUser, testObjectDataList, mockObjectDescribe);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(uniqueRuleService).findByDescribeApiName(testUser.getTenantId(), testDescribeApiName);
        verify(duplicateSearchProxy, never()).findDuplicateData(anyString(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找重复数据（带参数）的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找重复数据（带参数）成功")
    void testFindDuplicateDataWithArgs_Success() {
        // 准备测试数据
        UniqueRuleQuery.Arg mockArg = mock(UniqueRuleQuery.Arg.class);
        UniqueRuleQuery.Result mockResult = mock(UniqueRuleQuery.Result.class);

        when(duplicateSearchProxy.findDuplicateData(testTenantId, mockArg, mockObjectDescribe, testUniqueRule))
                .thenReturn(mockResult);

        // 执行被测试方法
        UniqueRuleQuery.Result result = uniqueRuleLogicService.findDuplicateData(
                testTenantId, mockArg, mockObjectDescribe, testUniqueRule);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockResult, result);
        verify(duplicateSearchProxy).findDuplicateData(testTenantId, mockArg, mockObjectDescribe, testUniqueRule);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找重复数据（带唯一查询）的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找重复数据（带唯一查询）成功")
    void testFindDuplicateDataWithUniqueQuery_Success() {
        // 准备测试数据
        UniqueQuery mockUniqueQuery = mock(UniqueQuery.class);
        UniqueRuleQuery.Result mockResult = mock(UniqueRuleQuery.Result.class);

        when(duplicateSearchProxy.findDuplicateData(testTenantId, mockUniqueQuery, mockObjectDescribe, testUniqueRule))
                .thenReturn(mockResult);

        // 执行被测试方法
        UniqueRuleQuery.Result result = uniqueRuleLogicService.findDuplicateData(
                testTenantId, mockUniqueQuery, mockObjectDescribe, testUniqueRule);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockResult, result);
        verify(duplicateSearchProxy).findDuplicateData(testTenantId, mockUniqueQuery, mockObjectDescribe, testUniqueRule);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除唯一性规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除唯一性规则成功")
    void testDelete_Success() {
        // 准备测试数据
        when(uniqueRuleService.findByDescribeApiName(testTenantId, testDescribeApiName))
                .thenReturn(testUniqueRule);

        // 执行被测试方法
        uniqueRuleLogicService.delete(testTenantId, testDescribeApiName);

        // 验证结果
        verify(uniqueRuleService).findByDescribeApiName(testTenantId, testDescribeApiName);
        verify(uniqueRuleService).delete(testUniqueRule);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除唯一性规则时，规则不存在的场景
     */
    @Test
    @DisplayName("正常场景 - 删除唯一性规则时规则不存在直接返回")
    void testDelete_RuleNotExists() {
        // 准备测试数据
        when(uniqueRuleService.findByDescribeApiName(testTenantId, testDescribeApiName))
                .thenReturn(null);

        // 执行被测试方法
        uniqueRuleLogicService.delete(testTenantId, testDescribeApiName);

        // 验证结果
        verify(uniqueRuleService).findByDescribeApiName(testTenantId, testDescribeApiName);
        verify(uniqueRuleService, never()).delete(any(IUniqueRule.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查唯一性的正常场景
     */
    @Test
    @DisplayName("正常场景 - 检查唯一性成功")
    void testCheckUnique_Success() {
        // 准备测试数据
        IObjectData testData = new ObjectData();
        testData.setId("testDataId");

        when(uniqueRuleService.findByDescribeApiName(testUser.getTenantId(), testDescribeApiName))
                .thenReturn(testUniqueRule);

        UniqueRuleQuery.Result mockResult = mock(UniqueRuleQuery.Result.class);
        when(mockResult.isEmpty()).thenReturn(true);

        when(duplicateSearchProxy.findDuplicateData(anyString(), any(UniqueQuery.class), 
                any(IObjectDescribe.class), any(IUniqueRule.class)))
                .thenReturn(mockResult);

        // 执行被测试方法
        boolean result = uniqueRuleLogicService.checkUnique(testUser, testData, mockObjectDescribe);

        // 验证结果
        assertTrue(result);
        verify(uniqueRuleService).findByDescribeApiName(testUser.getTenantId(), testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查唯一性时，唯一性规则不存在的场景
     */
    @Test
    @DisplayName("正常场景 - 检查唯一性时唯一性规则不存在返回true")
    void testCheckUnique_UniqueRuleNotExists() {
        // 准备测试数据
        IObjectData testData = new ObjectData();
        testData.setId("testDataId");

        when(uniqueRuleService.findByDescribeApiName(testUser.getTenantId(), testDescribeApiName))
                .thenReturn(null);

        // 执行被测试方法
        boolean result = uniqueRuleLogicService.checkUnique(testUser, testData, mockObjectDescribe);

        // 验证结果
        assertTrue(result);
        verify(uniqueRuleService).findByDescribeApiName(testUser.getTenantId(), testDescribeApiName);
        verify(duplicateSearchProxy, never()).findDuplicateData(anyString(), any(), any(), any());
    }
}
