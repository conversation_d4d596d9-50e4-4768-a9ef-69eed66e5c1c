package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.dto.UniqueQuery;
import com.facishare.paas.appframework.metadata.dto.UniqueRuleQuery;
import com.facishare.paas.appframework.metadata.dto.UniqueRuleSearchResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IUniqueRuleService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.data.UniqueRule;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UniqueRuleLogicServiceImplTest {

    @Mock
    private IUniqueRuleService uniqueRuleService;

    @Mock
    private DuplicateSearchProxy duplicateSearchProxy;

    @Mock
    private SwitchCacheService switchCacheService;

    @Mock
    private RedisDao redisDao;

    @InjectMocks
    private UniqueRuleLogicServiceImpl uniqueRuleLogicService;

    private User testUser;
    private String testTenantId;
    private IObjectDescribe mockObjectDescribe;
    private IObjectData testObjectData;
    private IUniqueRule mockUniqueRule;
    private List<IUniqueRule> testUniqueRules;
    private IFieldDescribe mockFieldDescribe;

    @BeforeEach
    void setUp() {
        testUser = new User("74255", "testUserId");
        testTenantId = "74255";

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn("TestObj");
        when(mockObjectDescribe.getTenantId()).thenReturn(testTenantId);

        testObjectData = new ObjectData();
        testObjectData.setId("testDataId");
        testObjectData.put("name__c", "Test Name");

        mockUniqueRule = mock(IUniqueRule.class);
        when(mockUniqueRule.getId()).thenReturn("uniqueRuleId1");
        when(mockUniqueRule.getName()).thenReturn("Test Unique Rule");
        when(mockUniqueRule.getFieldApiNames()).thenReturn(Lists.newArrayList("name__c"));
        testUniqueRules = Lists.newArrayList(mockUniqueRule);

        mockFieldDescribe = mock(IFieldDescribe.class);
        when(mockFieldDescribe.getApiName()).thenReturn("name__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证唯一性规则的正常场景，验证数据通过唯一性检查
     */
    @Test
    @DisplayName("正常场景 - 验证唯一性规则成功（数据通过验证）")
    void testValidateUniqueRule_Success_DataPassesValidation() throws MetadataServiceException {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            // 准备测试数据
            mockedAppFrameworkConfig.when(AppFrameworkConfig::isOpenUniqueRule).thenReturn(true);
            when(switchCacheService.isOpen(anyString(), anyString(), anyString())).thenReturn(true);
            when(uniqueRuleService.findByDescribe(mockObjectDescribe)).thenReturn(testUniqueRules);

            UniqueRuleSearchResult searchResult = new UniqueRuleSearchResult();
            searchResult.setSuccess(true);
            searchResult.setDuplicateCount(0);
            when(duplicateSearchProxy.searchDuplicate(any(UniqueRuleQuery.class)))
                    .thenReturn(searchResult);

            // 执行被测试方法
            boolean result = uniqueRuleLogicService.validateUniqueRule(testUser, testObjectData, mockObjectDescribe);

            // 验证结果
            assertTrue(result);
            verify(uniqueRuleService).findByDescribe(mockObjectDescribe);
            verify(duplicateSearchProxy).searchDuplicate(any(UniqueRuleQuery.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证唯一性规则时，发现重复数据的场景
     */
    @Test
    @DisplayName("异常场景 - 验证唯一性规则时发现重复数据抛出ValidateException")
    void testValidateUniqueRuleThrowsValidateException_DuplicateDataFound() throws MetadataServiceException {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class);
             MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class)) {

            // 准备测试数据
            mockedAppFrameworkConfig.when(AppFrameworkConfig::isOpenUniqueRule).thenReturn(true);
            when(switchCacheService.isOpen(anyString(), anyString(), anyString())).thenReturn(true);
            when(uniqueRuleService.findByDescribe(mockObjectDescribe)).thenReturn(testUniqueRules);

            UniqueRuleSearchResult searchResult = new UniqueRuleSearchResult();
            searchResult.setSuccess(true);
            searchResult.setDuplicateCount(1);
            when(duplicateSearchProxy.searchDuplicate(any(UniqueRuleQuery.class)))
                    .thenReturn(searchResult);

            mockedI18NExt.when(() -> I18NExt.text(I18NKey.UNIQUE_RULE_VALIDATE_ERROR))
                    .thenReturn("唯一性规则验证失败");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                uniqueRuleLogicService.validateUniqueRule(testUser, testObjectData, mockObjectDescribe);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(uniqueRuleService).findByDescribe(mockObjectDescribe);
            verify(duplicateSearchProxy).searchDuplicate(any(UniqueRuleQuery.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证唯一性规则时，唯一性规则功能关闭的场景
     */
    @Test
    @DisplayName("正常场景 - 验证唯一性规则时功能关闭直接通过")
    void testValidateUniqueRule_FeatureDisabled_PassesValidation() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            // 准备测试数据
            mockedAppFrameworkConfig.when(AppFrameworkConfig::isOpenUniqueRule).thenReturn(false);

            // 执行被测试方法
            boolean result = uniqueRuleLogicService.validateUniqueRule(testUser, testObjectData, mockObjectDescribe);

            // 验证结果
            assertTrue(result);
            verify(uniqueRuleService, never()).findByDescribe(any());
            verify(duplicateSearchProxy, never()).searchDuplicate(any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证唯一性规则时，没有找到唯一性规则的场景
     */
    @Test
    @DisplayName("正常场景 - 验证唯一性规则时没有找到规则直接通过")
    void testValidateUniqueRule_NoRulesFound_PassesValidation() throws MetadataServiceException {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            // 准备测试数据
            mockedAppFrameworkConfig.when(AppFrameworkConfig::isOpenUniqueRule).thenReturn(true);
            when(switchCacheService.isOpen(anyString(), anyString(), anyString())).thenReturn(true);
            when(uniqueRuleService.findByDescribe(mockObjectDescribe)).thenReturn(Lists.newArrayList());

            // 执行被测试方法
            boolean result = uniqueRuleLogicService.validateUniqueRule(testUser, testObjectData, mockObjectDescribe);

            // 验证结果
            assertTrue(result);
            verify(uniqueRuleService).findByDescribe(mockObjectDescribe);
            verify(duplicateSearchProxy, never()).searchDuplicate(any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建唯一性规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 创建唯一性规则成功")
    void testCreateUniqueRule_Success() throws MetadataServiceException {
        // 准备测试数据
        UniqueRule newRule = new UniqueRule();
        newRule.setName("New Unique Rule");
        newRule.setFieldApiNames(Lists.newArrayList("name__c"));

        UniqueRule createdRule = new UniqueRule();
        createdRule.setId("newUniqueRuleId");
        createdRule.setName("New Unique Rule");
        when(uniqueRuleService.create(newRule)).thenReturn(createdRule);

        // 执行被测试方法
        IUniqueRule result = uniqueRuleLogicService.createUniqueRule(testUser, newRule);

        // 验证结果
        assertNotNull(result);
        assertEquals("newUniqueRuleId", result.getId());
        verify(uniqueRuleService).create(newRule);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建唯一性规则时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 创建唯一性规则时服务抛出MetaDataBusinessException")
    void testCreateUniqueRuleThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        UniqueRule newRule = new UniqueRule();
        newRule.setName("New Unique Rule");

        MetadataServiceException serviceException = new MetadataServiceException("创建失败");
        when(uniqueRuleService.create(newRule)).thenThrow(serviceException);

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            uniqueRuleLogicService.createUniqueRule(testUser, newRule);
        });

        // 验证异常信息
        assertNotNull(exception);
        verify(uniqueRuleService).create(newRule);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新唯一性规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 更新唯一性规则成功")
    void testUpdateUniqueRule_Success() throws MetadataServiceException {
        // 准备测试数据
        UniqueRule ruleToUpdate = new UniqueRule();
        ruleToUpdate.setId("ruleToUpdate");
        ruleToUpdate.setName("Updated Unique Rule");

        UniqueRule updatedRule = new UniqueRule();
        updatedRule.setId("ruleToUpdate");
        updatedRule.setName("Updated Unique Rule");
        when(uniqueRuleService.update(ruleToUpdate)).thenReturn(updatedRule);

        // 执行被测试方法
        IUniqueRule result = uniqueRuleLogicService.updateUniqueRule(testUser, ruleToUpdate);

        // 验证结果
        assertNotNull(result);
        assertEquals("ruleToUpdate", result.getId());
        verify(uniqueRuleService).update(ruleToUpdate);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除唯一性规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除唯一性规则成功")
    void testDeleteUniqueRule_Success() throws MetadataServiceException {
        // 准备测试数据
        String ruleId = "ruleToDelete";

        // 执行被测试方法
        uniqueRuleLogicService.deleteUniqueRule(testUser, ruleId);

        // 验证结果
        verify(uniqueRuleService).delete(ruleId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除唯一性规则时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 删除唯一性规则时服务抛出MetaDataBusinessException")
    void testDeleteUniqueRuleThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        String ruleId = "ruleToDelete";
        MetadataServiceException serviceException = new MetadataServiceException("删除失败");
        doThrow(serviceException).when(uniqueRuleService).delete(ruleId);

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            uniqueRuleLogicService.deleteUniqueRule(testUser, ruleId);
        });

        // 验证异常信息
        assertNotNull(exception);
        verify(uniqueRuleService).delete(ruleId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找唯一性规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找唯一性规则成功")
    void testFindUniqueRules_Success() throws MetadataServiceException {
        // 准备测试数据
        when(uniqueRuleService.findByDescribe(mockObjectDescribe)).thenReturn(testUniqueRules);

        // 执行被测试方法
        List<IUniqueRule> result = uniqueRuleLogicService.findUniqueRules(testUser, mockObjectDescribe);

        // 验证结果
        assertNotNull(result);
        assertEquals(testUniqueRules, result);
        verify(uniqueRuleService).findByDescribe(mockObjectDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找唯一性规则时，没有找到规则的场景
     */
    @Test
    @DisplayName("正常场景 - 查找唯一性规则时没有找到规则返回空列表")
    void testFindUniqueRules_NoRulesFound() throws MetadataServiceException {
        // 准备测试数据
        when(uniqueRuleService.findByDescribe(mockObjectDescribe)).thenReturn(Lists.newArrayList());

        // 执行被测试方法
        List<IUniqueRule> result = uniqueRuleLogicService.findUniqueRules(testUser, mockObjectDescribe);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(uniqueRuleService).findByDescribe(mockObjectDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试搜索重复数据的正常场景
     */
    @Test
    @DisplayName("正常场景 - 搜索重复数据成功")
    void testSearchDuplicateData_Success() {
        // 准备测试数据
        UniqueQuery uniqueQuery = new UniqueQuery();
        uniqueQuery.setObjectApiName("TestObj");
        uniqueQuery.setFieldApiNames(Lists.newArrayList("name__c"));

        UniqueRuleSearchResult searchResult = new UniqueRuleSearchResult();
        searchResult.setSuccess(true);
        searchResult.setDuplicateCount(2);
        when(duplicateSearchProxy.searchDuplicate(any(UniqueRuleQuery.class)))
                .thenReturn(searchResult);

        // 执行被测试方法
        UniqueRuleSearchResult result = uniqueRuleLogicService.searchDuplicateData(testUser, uniqueQuery);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(2, result.getDuplicateCount());
        verify(duplicateSearchProxy).searchDuplicate(any(UniqueRuleQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试搜索重复数据时，没有找到重复数据的场景
     */
    @Test
    @DisplayName("正常场景 - 搜索重复数据时没有找到重复数据")
    void testSearchDuplicateData_NoDuplicatesFound() {
        // 准备测试数据
        UniqueQuery uniqueQuery = new UniqueQuery();
        uniqueQuery.setObjectApiName("TestObj");
        uniqueQuery.setFieldApiNames(Lists.newArrayList("name__c"));

        UniqueRuleSearchResult searchResult = new UniqueRuleSearchResult();
        searchResult.setSuccess(true);
        searchResult.setDuplicateCount(0);
        when(duplicateSearchProxy.searchDuplicate(any(UniqueRuleQuery.class)))
                .thenReturn(searchResult);

        // 执行被测试方法
        UniqueRuleSearchResult result = uniqueRuleLogicService.searchDuplicateData(testUser, uniqueQuery);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getDuplicateCount());
        verify(duplicateSearchProxy).searchDuplicate(any(UniqueRuleQuery.class));
    }
}
