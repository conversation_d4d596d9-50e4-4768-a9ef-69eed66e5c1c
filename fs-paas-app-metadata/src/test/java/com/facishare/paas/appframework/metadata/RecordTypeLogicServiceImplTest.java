package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.ManageGroupService;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.config.ApplicationLayeredGrayService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.auth.RoleInfoModel;
import com.facishare.paas.appframework.metadata.dto.auth.RoleInfoPojo;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IRecordTypeDescribe;
import com.facishare.paas.metadata.api.service.IRecordTypeService;
import com.facishare.paas.metadata.api.service.IRelationMatchService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.RecordTypeOption;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.data.RoleInfoData;
import com.fxiaoke.enterpriserelation2.service.AppOuterRoleService;
import com.fxiaoke.paas.auth.factory.ViewClient;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RecordTypeLogicServiceImplTest {

    @Mock
    private IRecordTypeService recordTypeService;

    @Mock
    private LicenseService licenseService;

    @Mock
    private RecordTypeAuthProxy recordTypeAuthProxy;

    @Mock
    private UserRoleInfoService userRoleInfoService;

    @Mock
    private LayoutLogicService layoutService;

    @Mock
    private LogService logService;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private IRelationMatchService relationMatchServiceImpl;

    @Mock
    private FunctionPrivilegeService functionPrivilegeService;

    @Mock
    private FieldRelationCalculateService fieldRelationCalculateService;

    @Mock
    private ViewClient viewClient;

    @Mock
    private ManageGroupService manageGroupService;

    @Mock
    private ChangeOrderLogicService changeOrderLogicService;

    @Mock
    private ApplicationLayeredGrayService applicationLayeredGrayService;

    @Mock
    private AppOuterRoleService appOuterRoleService;

    @InjectMocks
    private RecordTypeLogicServiceImpl recordTypeLogicService;

    private User testUser;
    private String testTenantId;
    private String testDescribeApiName;
    private String testAppId;
    private List<RoleInfoPojo> testRoleInfoList;
    private IObjectDescribe mockObjectDescribe;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setTenantId("74255");
        testUser.setUserId("testUserId");

        testTenantId = "74255";
        testDescribeApiName = "TestObj";
        testAppId = "testAppId";

        RoleInfoPojo mockRoleInfo = new RoleInfoPojo();
        mockRoleInfo.setRoleCode("testRole");
        mockRoleInfo.setRoleName("测试角色");
        testRoleInfoList = Lists.newArrayList(mockRoleInfo);

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn(testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找角色信息列表的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找角色信息列表成功")
    void testFindRoleInfoList_Success() {
        // 准备测试数据
        RoleInfoModel.Result mockResult = mock(RoleInfoModel.Result.class);
        RoleInfoModel.Result.RoleInfoResultData mockResultData = mock(RoleInfoModel.Result.RoleInfoResultData.class);
        when(mockResultData.getRoles()).thenReturn(testRoleInfoList);
        when(mockResult.getResult()).thenReturn(mockResultData);

        when(recordTypeAuthProxy.roleInfo(any(RoleInfoModel.Arg.class), any()))
                .thenReturn(mockResult);

        // 执行被测试方法
        RecordTypeResult result = recordTypeLogicService.findRoleInfoList(testUser);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getRole_list());
        assertEquals(1, result.getRole_list().size());
        verify(recordTypeAuthProxy).roleInfo(any(RoleInfoModel.Arg.class), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找角色信息列表时，角色列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 查找角色信息列表为空时返回null")
    void testFindRoleInfoList_EmptyRoleList() {
        // 准备测试数据
        RoleInfoModel.Result mockResult = mock(RoleInfoModel.Result.class);
        RoleInfoModel.Result.RoleInfoResultData mockResultData = mock(RoleInfoModel.Result.RoleInfoResultData.class);
        when(mockResultData.getRoles()).thenReturn(Lists.newArrayList());
        when(mockResult.getResult()).thenReturn(mockResultData);

        when(recordTypeAuthProxy.roleInfo(any(RoleInfoModel.Arg.class), any()))
                .thenReturn(mockResult);

        // 执行被测试方法
        RecordTypeResult result = recordTypeLogicService.findRoleInfoList(testUser);

        // 验证结果
        assertNotNull(result);
        assertNull(result.getRole_list());
        verify(recordTypeAuthProxy).roleInfo(any(RoleInfoModel.Arg.class), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带管理组的角色信息查找的正常场景
     */
    @Test
    @DisplayName("正常场景 - 带管理组的角色信息查找成功")
    void testFindRoleInfoWithMangeGroup_Success() {
        // 准备测试数据
        String sourceInfo = "testSource";
        boolean excludeOuterRole = false;

        RoleInfoModel.Result mockResult = mock(RoleInfoModel.Result.class);
        RoleInfoModel.Result.RoleInfoResultData mockResultData = mock(RoleInfoModel.Result.RoleInfoResultData.class);
        when(mockResultData.getRoles()).thenReturn(testRoleInfoList);
        when(mockResult.getResult()).thenReturn(mockResultData);

        when(recordTypeAuthProxy.roleInfo(any(RoleInfoModel.Arg.class), any()))
                .thenReturn(mockResult);

        ManageGroup mockManageGroup = mock(ManageGroup.class);
        when(manageGroupService.queryRoleManageGroup(testUser, sourceInfo))
                .thenReturn(mockManageGroup);

        // 执行被测试方法
        List<RoleInfoPojo> result = recordTypeLogicService.findRoleInfoWithMangeGroup(
                testUser, sourceInfo, excludeOuterRole);

        // 验证结果
        assertNotNull(result);
        verify(recordTypeAuthProxy).roleInfo(any(RoleInfoModel.Arg.class), any());
        verify(manageGroupService).queryRoleManageGroup(testUser, sourceInfo);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带管理组的角色信息查找时，角色列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 带管理组的角色信息查找时角色列表为空返回空列表")
    void testFindRoleInfoWithMangeGroup_EmptyRoleList() {
        // 准备测试数据
        String sourceInfo = "testSource";
        boolean excludeOuterRole = false;

        RoleInfoModel.Result mockResult = mock(RoleInfoModel.Result.class);
        RoleInfoModel.Result.RoleInfoResultData mockResultData = mock(RoleInfoModel.Result.RoleInfoResultData.class);
        when(mockResultData.getRoles()).thenReturn(Lists.newArrayList());
        when(mockResult.getResult()).thenReturn(mockResultData);

        when(recordTypeAuthProxy.roleInfo(any(RoleInfoModel.Arg.class), any()))
                .thenReturn(mockResult);

        // 执行被测试方法
        List<RoleInfoPojo> result = recordTypeLogicService.findRoleInfoWithMangeGroup(
                testUser, sourceInfo, excludeOuterRole);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(recordTypeAuthProxy).roleInfo(any(RoleInfoModel.Arg.class), any());
        verify(manageGroupService, never()).queryRoleManageGroup(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据应用ID带管理组查找角色信息的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据应用ID带管理组查找角色信息成功")
    void testFindRoleInfoByAppIdWithMangeGroup_Success() {
        // 准备测试数据
        String sourceInfo = "testSource";

        when(applicationLayeredGrayService.supportAppLayered(testUser, testAppId, testDescribeApiName))
                .thenReturn(false);

        RoleInfoModel.Result mockResult = mock(RoleInfoModel.Result.class);
        RoleInfoModel.Result.RoleInfoResultData mockResultData = mock(RoleInfoModel.Result.RoleInfoResultData.class);
        when(mockResultData.getRoles()).thenReturn(testRoleInfoList);
        when(mockResult.getResult()).thenReturn(mockResultData);

        when(recordTypeAuthProxy.roleInfo(any(RoleInfoModel.Arg.class), any()))
                .thenReturn(mockResult);

        ManageGroup mockManageGroup = mock(ManageGroup.class);
        when(manageGroupService.queryRoleManageGroup(testUser, sourceInfo))
                .thenReturn(mockManageGroup);

        // 执行被测试方法
        List<RoleInfoPojo> result = recordTypeLogicService.findRoleInfoByAppIdWithMangeGroup(
                testUser, sourceInfo, testDescribeApiName, testAppId);

        // 验证结果
        assertNotNull(result);
        verify(applicationLayeredGrayService).supportAppLayered(testUser, testAppId, testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据应用ID带管理组查找角色信息时，支持应用分层的场景
     */
    @Test
    @DisplayName("正常场景 - 根据应用ID带管理组查找角色信息时支持应用分层")
    void testFindRoleInfoByAppIdWithMangeGroup_SupportAppLayered() {
        // 准备测试数据
        String sourceInfo = "testSource";

        when(applicationLayeredGrayService.supportAppLayered(testUser, testAppId, testDescribeApiName))
                .thenReturn(true);

        RoleInfoData mockRoleInfoData = new RoleInfoData();
        mockRoleInfoData.setRoleCode("testRole");
        mockRoleInfoData.setRoleName("测试角色");

        RestResult<List<RoleInfoData>> mockRestResult = new RestResult<>();
        mockRestResult.setSuccess(true);
        mockRestResult.setData(Lists.newArrayList(mockRoleInfoData));

        when(appOuterRoleService.listAppOuterRolesByAppId(any(HeaderObj.class), any()))
                .thenReturn(mockRestResult);

        // 执行被测试方法
        List<RoleInfoPojo> result = recordTypeLogicService.findRoleInfoByAppIdWithMangeGroup(
                testUser, sourceInfo, testDescribeApiName, testAppId);

        // 验证结果
        assertNotNull(result);
        verify(applicationLayeredGrayService).supportAppLayered(testUser, testAppId, testDescribeApiName);
        verify(appOuterRoleService).listAppOuterRolesByAppId(any(HeaderObj.class), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据应用ID查找外部角色列表的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据应用ID查找外部角色列表成功")
    void testFindListAppOuterRolesByAppId_Success() {
        // 准备测试数据
        RoleInfoData mockRoleInfoData = new RoleInfoData();
        mockRoleInfoData.setRoleCode("testRole");
        mockRoleInfoData.setRoleName("测试角色");

        RestResult<List<RoleInfoData>> mockRestResult = new RestResult<>();
        mockRestResult.setSuccess(true);
        mockRestResult.setData(Lists.newArrayList(mockRoleInfoData));

        when(appOuterRoleService.listAppOuterRolesByAppId(any(HeaderObj.class), any()))
                .thenReturn(mockRestResult);

        // 执行被测试方法
        List<RoleInfoPojo> result = recordTypeLogicService.findListAppOuterRolesByAppId(testUser, testAppId);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("testRole", result.get(0).getRoleCode());
        verify(appOuterRoleService).listAppOuterRolesByAppId(any(HeaderObj.class), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据应用ID查找外部角色列表时，服务调用失败的场景
     */
    @Test
    @DisplayName("正常场景 - 根据应用ID查找外部角色列表时服务调用失败返回空列表")
    void testFindListAppOuterRolesByAppId_ServiceFailed() {
        // 准备测试数据
        RestResult<List<RoleInfoData>> mockRestResult = new RestResult<>();
        mockRestResult.setSuccess(false);
        mockRestResult.setErrMsg("Service error");

        when(appOuterRoleService.listAppOuterRolesByAppId(any(HeaderObj.class), any()))
                .thenReturn(mockRestResult);

        // 执行被测试方法
        List<RoleInfoPojo> result = recordTypeLogicService.findListAppOuterRolesByAppId(testUser, testAppId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(appOuterRoleService).listAppOuterRolesByAppId(any(HeaderObj.class), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据应用ID查找外部角色列表时，返回数据为null的场景
     */
    @Test
    @DisplayName("正常场景 - 根据应用ID查找外部角色列表时返回数据为null返回空列表")
    void testFindListAppOuterRolesByAppId_NullData() {
        // 准备测试数据
        RestResult<List<RoleInfoData>> mockRestResult = new RestResult<>();
        mockRestResult.setSuccess(true);
        mockRestResult.setData(null);

        when(appOuterRoleService.listAppOuterRolesByAppId(any(HeaderObj.class), any()))
                .thenReturn(mockRestResult);

        // 执行被测试方法
        List<RoleInfoPojo> result = recordTypeLogicService.findListAppOuterRolesByAppId(testUser, testAppId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(appOuterRoleService).listAppOuterRolesByAppId(any(HeaderObj.class), any());
    }
}
