package com.facishare.paas.appframework.metadata.metadatahandle;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataMiscService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * FillFieldInfoHandleImpl的单元测试
 * 测试字段信息填充处理功能
 */
@ExtendWith(MockitoExtension.class)
class FillFieldInfoHandleImplTest {

    @Mock
    private MetaDataMiscService metaDataMiscService;

    @Mock
    private IObjectDescribe mockObjectDescribe;

    @Mock
    private IObjectData mockObjectData;

    @Mock
    private User mockUser;

    @InjectMocks
    private FillFieldInfoHandleImpl fillFieldInfoHandle;

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息的正常场景
     */
    @Test
    @DisplayName("正常场景 - 异步填充字段信息成功")
    void testAsyncFillFieldInfo_Success() {
        // 准备测试数据
        List<IObjectData> dataList = new ArrayList<>();
        dataList.add(mockObjectData);

        // 配置Mock行为
        when(mockUser.getTenantId()).thenReturn("tenant123");
        when(mockObjectDescribe.getApiName()).thenReturn("Account");

        // 执行测试
        assertDoesNotThrow(() -> {
            fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, dataList, mockUser);
        });

        // 等待一段时间确保异步任务完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 验证各个填充方法被调用
        verify(metaDataMiscService, timeout(6000)).fillObjectDataWithRefObject(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillObjectDataObjectManyWithRefObject(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillUserInfo(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillDepartmentInfo(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillCountryAreaLabel(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillDimensionFieldValue(eq(mockUser), eq(mockObjectDescribe), any(List.class));
        verify(metaDataMiscService, timeout(6000)).fillCurrencyFieldInfo(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillDataVisibilityRange(eq(mockUser), eq(mockObjectDescribe), any(List.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时，数据列表为空的场景
     */
    @Test
    @DisplayName("边界场景 - 异步填充字段信息时数据列表为空")
    void testAsyncFillFieldInfo_EmptyDataList() {
        // 准备测试数据
        List<IObjectData> emptyDataList = Collections.emptyList();

        // 配置Mock行为
        when(mockUser.getTenantId()).thenReturn("tenant123");
        when(mockObjectDescribe.getApiName()).thenReturn("Account");

        // 执行测试
        assertDoesNotThrow(() -> {
            fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, emptyDataList, mockUser);
        });

        // 等待一段时间确保异步任务完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 验证某些方法不会被调用（因为数据列表为空）
        verify(metaDataMiscService, timeout(6000).times(0)).fillObjectDataWithRefObject(any(), any(), any());
        verify(metaDataMiscService, timeout(6000).times(0)).fillObjectDataObjectManyWithRefObject(any(), any(), any());
        verify(metaDataMiscService, timeout(6000).times(0)).fillUserInfo(any(), any(), any());
        verify(metaDataMiscService, timeout(6000).times(0)).fillDepartmentInfo(any(), any(), any());
        verify(metaDataMiscService, timeout(6000).times(0)).fillCountryAreaLabel(any(), any(), any());
        verify(metaDataMiscService, timeout(6000).times(0)).fillDimensionFieldValue(User.systemUser("74255"), new ObjectDescribe(), any());
        
        // 这些方法仍然会被调用，因为它们没有空检查
        verify(metaDataMiscService, timeout(6000)).fillCurrencyFieldInfo(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillDataVisibilityRange(eq(mockUser), eq(mockObjectDescribe), any(List.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时，数据列表为null的场景
     */
    @Test
    @DisplayName("边界场景 - 异步填充字段信息时数据列表为null")
    void testAsyncFillFieldInfo_NullDataList() {
        // 准备测试数据
        List<IObjectData> nullDataList = null;

        // 配置Mock行为
        when(mockUser.getTenantId()).thenReturn("tenant123");
        when(mockObjectDescribe.getApiName()).thenReturn("Account");

        // 执行测试
        assertDoesNotThrow(() -> {
            fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, nullDataList, mockUser);
        });

        // 等待一段时间确保异步任务完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 验证某些方法不会被调用（因为数据列表为null）
        verify(metaDataMiscService, timeout(6000).times(0)).fillObjectDataWithRefObject(any(), any(), any());
        verify(metaDataMiscService, timeout(6000).times(0)).fillObjectDataObjectManyWithRefObject(any(), any(), any());
        verify(metaDataMiscService, timeout(6000).times(0)).fillUserInfo(any(), any(), any());
        verify(metaDataMiscService, timeout(6000).times(0)).fillDepartmentInfo(any(), any(), any());
        verify(metaDataMiscService, timeout(6000).times(0)).fillCountryAreaLabel(any(), any(), any());
        verify(metaDataMiscService, timeout(6000).times(0)).fillDimensionFieldValue(User.systemUser("74255"), new ObjectDescribe(), any());
        
        // 这些方法仍然会被调用
        verify(metaDataMiscService, timeout(6000)).fillCurrencyFieldInfo(eq(mockObjectDescribe), any(), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillDataVisibilityRange(eq(mockUser), eq(mockObjectDescribe), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时，包含多个数据对象的场景
     */
    @Test
    @DisplayName("正常场景 - 异步填充字段信息时包含多个数据对象")
    void testAsyncFillFieldInfo_MultipleDataObjects() {
        // 准备测试数据
        List<IObjectData> dataList = new ArrayList<>();
        IObjectData mockObjectData2 = mock(IObjectData.class);
        IObjectData mockObjectData3 = mock(IObjectData.class);
        dataList.add(mockObjectData);
        dataList.add(mockObjectData2);
        dataList.add(mockObjectData3);

        // 配置Mock行为
        when(mockUser.getTenantId()).thenReturn("tenant123");
        when(mockObjectDescribe.getApiName()).thenReturn("Contact");

        // 执行测试
        assertDoesNotThrow(() -> {
            fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, dataList, mockUser);
        });

        // 等待一段时间确保异步任务完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 验证各个填充方法被调用
        verify(metaDataMiscService, timeout(6000)).fillObjectDataWithRefObject(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillObjectDataObjectManyWithRefObject(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillUserInfo(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillDepartmentInfo(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillCountryAreaLabel(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillDimensionFieldValue(eq(mockUser), eq(mockObjectDescribe), any(List.class));
        verify(metaDataMiscService, timeout(6000)).fillCurrencyFieldInfo(eq(mockObjectDescribe), any(List.class), eq(mockUser));
        verify(metaDataMiscService, timeout(6000)).fillDataVisibilityRange(eq(mockUser), eq(mockObjectDescribe), any(List.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时，MetaDataMiscService抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 异步填充字段信息时服务抛出异常")
    void testAsyncFillFieldInfo_ServiceException() {
        // 准备测试数据
        List<IObjectData> dataList = new ArrayList<>();
        dataList.add(mockObjectData);

        // 配置Mock行为 - 让某个服务方法抛出异常
        when(mockUser.getTenantId()).thenReturn("tenant123");
        when(mockObjectDescribe.getApiName()).thenReturn("Account");
        doThrow(new RuntimeException("Service error")).when(metaDataMiscService)
                .fillObjectDataWithRefObject(any(), any(), any());

        // 执行测试 - 应该不抛出异常，因为异常被捕获了
        assertDoesNotThrow(() -> {
            fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, dataList, mockUser);
        });

        // 等待一段时间确保异步任务完成
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 验证异常方法被调用
        verify(metaDataMiscService, timeout(6000)).fillObjectDataWithRefObject(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试依赖注入的正确性
     */
    @Test
    @DisplayName("依赖注入验证 - 验证MetaDataMiscService正确注入")
    void testDependencyInjection() {
        // 验证依赖注入
        assertNotNull(fillFieldInfoHandle);
        
        // 通过调用方法验证依赖是否正确注入
        List<IObjectData> dataList = new ArrayList<>();
        dataList.add(mockObjectData);

        when(mockUser.getTenantId()).thenReturn("tenant123");
        when(mockObjectDescribe.getApiName()).thenReturn("Account");

        // 执行测试 - 如果依赖注入失败，这里会抛出NullPointerException
        assertDoesNotThrow(() -> {
            fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, dataList, mockUser);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试并行任务的超时处理
     */
    @Test
    @DisplayName("超时场景 - 测试并行任务超时处理")
    void testAsyncFillFieldInfo_Timeout() {
        // 准备测试数据
        List<IObjectData> dataList = new ArrayList<>();
        dataList.add(mockObjectData);

        // 配置Mock行为 - 让服务方法执行时间较长
        when(mockUser.getTenantId()).thenReturn("tenant123");
        when(mockObjectDescribe.getApiName()).thenReturn("Account");
        doAnswer(invocation -> {
            Thread.sleep(6000); // 超过5秒超时时间
            return null;
        }).when(metaDataMiscService).fillObjectDataWithRefObject(any(), any(), any());

        // 执行测试 - 应该在5秒内返回，不会等待6秒
        long startTime = System.currentTimeMillis();
        assertDoesNotThrow(() -> {
            fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, dataList, mockUser);
        });
        long endTime = System.currentTimeMillis();

        // 验证执行时间应该在5秒左右（允许一些误差）
        long executionTime = endTime - startTime;
        assertTrue(executionTime < 7000, "执行时间应该小于7秒，实际：" + executionTime + "ms");
        assertTrue(executionTime > 4000, "执行时间应该大于4秒，实际：" + executionTime + "ms");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试方法的线程安全性
     */
    @Test
    @DisplayName("线程安全验证 - 测试并发调用的安全性")
    void testAsyncFillFieldInfo_ThreadSafety() {
        // 准备测试数据
        List<IObjectData> dataList = new ArrayList<>();
        dataList.add(mockObjectData);

        when(mockUser.getTenantId()).thenReturn("tenant123");
        when(mockObjectDescribe.getApiName()).thenReturn("Account");

        // 并发执行多次
        Runnable task = () -> {
            assertDoesNotThrow(() -> {
                fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, dataList, mockUser);
            });
        };

        Thread thread1 = new Thread(task);
        Thread thread2 = new Thread(task);
        Thread thread3 = new Thread(task);

        thread1.start();
        thread2.start();
        thread3.start();

        // 等待所有线程完成
        try {
            thread1.join();
            thread2.join();
            thread3.join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            fail("线程被中断");
        }

        // 验证没有异常抛出，说明线程安全
        assertTrue(true, "并发执行完成，没有异常");
    }
}
