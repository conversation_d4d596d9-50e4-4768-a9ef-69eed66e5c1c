package com.facishare.paas.appframework.metadata.metadatahandle;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataMiscService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FillFieldInfoHandleImplTest {

    @Mock
    private MetaDataMiscService metaDataMiscService;

    @InjectMocks
    private FillFieldInfoHandleImpl fillFieldInfoHandle;

    private User testUser;
    private IObjectDescribe mockObjectDescribe;
    private List<IObjectData> testDataList;
    private List<IObjectData> synchronizedDataList;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setTenantId("74255");
        testUser.setUserId("testUserId");

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn("TestObj");
        when(mockObjectDescribe.getDisplayName()).thenReturn("测试对象");

        IObjectData testData1 = new ObjectData();
        testData1.setId("testDataId1");
        testData1.set("name", "测试数据1");

        IObjectData testData2 = new ObjectData();
        testData2.setId("testDataId2");
        testData2.set("name", "测试数据2");

        testDataList = Lists.newArrayList(testData1, testData2);
        synchronizedDataList = Lists.newArrayList(testData1, testData2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息的正常场景，验证所有并行任务都被正确提交
     */
    @Test
    @DisplayName("正常场景 - 异步填充字段信息成功")
    void testAsyncFillFieldInfo_Success() {
        try (MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {
            // 准备测试数据
            mockedObjectDataExt.when(() -> ObjectDataExt.synchronize(testDataList))
                    .thenReturn(synchronizedDataList);

            // 执行被测试方法
            fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, testDataList, testUser);

            // 验证结果 - 验证所有相关服务方法都被调用
            verify(metaDataMiscService).fillObjectDataWithRefObject(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService).fillObjectDataObjectManyWithRefObject(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService).fillUserInfo(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService).fillDepartmentInfo(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService).fillCountryAreaLabel(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService).fillDimensionFieldValue(testUser, mockObjectDescribe, synchronizedDataList);
            verify(metaDataMiscService).fillCurrencyFieldInfo(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService).fillDataVisibilityRange(testUser, mockObjectDescribe, synchronizedDataList);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时，数据列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 数据列表为空时部分方法不被调用")
    void testAsyncFillFieldInfo_EmptyDataList() {
        try (MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {
            // 准备测试数据
            List<IObjectData> emptyDataList = Lists.newArrayList();
            List<IObjectData> emptySynchronizedDataList = Lists.newArrayList();

            mockedObjectDataExt.when(() -> ObjectDataExt.synchronize(emptyDataList))
                    .thenReturn(emptySynchronizedDataList);

            // 执行被测试方法
            fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, emptyDataList, testUser);

            // 验证结果 - 由于数据列表为空，某些方法不应该被调用
            verify(metaDataMiscService, never()).fillObjectDataWithRefObject(any(), any(), any());
            verify(metaDataMiscService, never()).fillObjectDataObjectManyWithRefObject(any(), any(), any());
            verify(metaDataMiscService, never()).fillUserInfo(any(), any(), any());
            verify(metaDataMiscService, never()).fillDepartmentInfo(any(), any(), any());
            verify(metaDataMiscService, never()).fillCountryAreaLabel(any(), any(), any());
            verify(metaDataMiscService, never()).fillDimensionFieldValue(any(), any(), any());
            
            // 这些方法仍然会被调用，因为它们没有空检查
            verify(metaDataMiscService).fillCurrencyFieldInfo(mockObjectDescribe, emptySynchronizedDataList, testUser);
            verify(metaDataMiscService).fillDataVisibilityRange(testUser, mockObjectDescribe, emptySynchronizedDataList);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时，服务方法抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 服务方法抛出异常时不影响其他任务执行")
    void testAsyncFillFieldInfo_ServiceThrowsException() {
        try (MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {
            // 准备测试数据
            mockedObjectDataExt.when(() -> ObjectDataExt.synchronize(testDataList))
                    .thenReturn(synchronizedDataList);

            // 模拟其中一个服务方法抛出异常
            doThrow(new RuntimeException("Test exception"))
                    .when(metaDataMiscService).fillUserInfo(mockObjectDescribe, synchronizedDataList, testUser);

            // 执行被测试方法 - 不应该抛出异常
            assertDoesNotThrow(() -> {
                fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, testDataList, testUser);
            });

            // 验证结果 - 其他方法仍然应该被调用
            verify(metaDataMiscService).fillObjectDataWithRefObject(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService).fillObjectDataObjectManyWithRefObject(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService).fillUserInfo(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService).fillDepartmentInfo(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService).fillCountryAreaLabel(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService).fillDimensionFieldValue(testUser, mockObjectDescribe, synchronizedDataList);
            verify(metaDataMiscService).fillCurrencyFieldInfo(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService).fillDataVisibilityRange(testUser, mockObjectDescribe, synchronizedDataList);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时，验证ObjectDataExt.synchronize被正确调用
     */
    @Test
    @DisplayName("正常场景 - 验证数据同步方法被正确调用")
    void testAsyncFillFieldInfo_VerifySynchronizeCall() {
        try (MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {
            // 准备测试数据
            mockedObjectDataExt.when(() -> ObjectDataExt.synchronize(testDataList))
                    .thenReturn(synchronizedDataList);

            // 执行被测试方法
            fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, testDataList, testUser);

            // 验证结果
            mockedObjectDataExt.verify(() -> ObjectDataExt.synchronize(testDataList));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时，验证所有并行任务的执行顺序和调用次数
     */
    @Test
    @DisplayName("正常场景 - 验证并行任务的调用次数")
    void testAsyncFillFieldInfo_VerifyCallCounts() {
        try (MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {
            // 准备测试数据
            mockedObjectDataExt.when(() -> ObjectDataExt.synchronize(testDataList))
                    .thenReturn(synchronizedDataList);

            // 执行被测试方法
            fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, testDataList, testUser);

            // 验证结果 - 每个方法都应该被调用一次
            verify(metaDataMiscService, times(1)).fillObjectDataWithRefObject(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService, times(1)).fillObjectDataObjectManyWithRefObject(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService, times(1)).fillUserInfo(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService, times(1)).fillDepartmentInfo(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService, times(1)).fillCountryAreaLabel(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService, times(1)).fillDimensionFieldValue(testUser, mockObjectDescribe, synchronizedDataList);
            verify(metaDataMiscService, times(1)).fillCurrencyFieldInfo(mockObjectDescribe, synchronizedDataList, testUser);
            verify(metaDataMiscService, times(1)).fillDataVisibilityRange(testUser, mockObjectDescribe, synchronizedDataList);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异步填充字段信息时，验证用户和对象描述参数的正确传递
     */
    @Test
    @DisplayName("正常场景 - 验证参数正确传递给服务方法")
    void testAsyncFillFieldInfo_VerifyParameterPassing() {
        try (MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {
            // 准备测试数据
            mockedObjectDataExt.when(() -> ObjectDataExt.synchronize(testDataList))
                    .thenReturn(synchronizedDataList);

            // 执行被测试方法
            fillFieldInfoHandle.asyncFillFieldInfo(mockObjectDescribe, testDataList, testUser);

            // 验证结果 - 验证参数传递的正确性
            verify(metaDataMiscService).fillObjectDataWithRefObject(eq(mockObjectDescribe), eq(synchronizedDataList), eq(testUser));
            verify(metaDataMiscService).fillObjectDataObjectManyWithRefObject(eq(mockObjectDescribe), eq(synchronizedDataList), eq(testUser));
            verify(metaDataMiscService).fillUserInfo(eq(mockObjectDescribe), eq(synchronizedDataList), eq(testUser));
            verify(metaDataMiscService).fillDepartmentInfo(eq(mockObjectDescribe), eq(synchronizedDataList), eq(testUser));
            verify(metaDataMiscService).fillCountryAreaLabel(eq(mockObjectDescribe), eq(synchronizedDataList), eq(testUser));
            verify(metaDataMiscService).fillDimensionFieldValue(eq(testUser), eq(mockObjectDescribe), eq(synchronizedDataList));
            verify(metaDataMiscService).fillCurrencyFieldInfo(eq(mockObjectDescribe), eq(synchronizedDataList), eq(testUser));
            verify(metaDataMiscService).fillDataVisibilityRange(eq(testUser), eq(mockObjectDescribe), eq(synchronizedDataList));
        }
    }
}
