package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.dto.GlobalVariableResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.privilege.EnterpriseRelationServiceProxy;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe;
import com.facishare.paas.metadata.api.service.IGlobalVariableDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.GlobalVariableDescribe;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GlobalVarServiceImplTest {

    @Mock
    private IGlobalVariableDescribeService globalVariableDescribeService;

    @Mock
    private FieldRelationCalculateService fieldRelationCalculateService;

    @Mock
    private EnterpriseEditionService enterpriseEditionService;

    @Mock
    private EnterpriseRelationServiceProxy enterpriseRelationService;

    @Mock
    private I18nSettingService i18nSettingService;

    @Mock
    private LicenseService licenseService;

    @InjectMocks
    private GlobalVarServiceImpl globalVarService;

    private String testTenantId;
    private String testApiName;
    private IGlobalVariableDescribe testGlobalVariable;
    private List<String> testApiNames;

    @BeforeEach
    void setUp() {
        testTenantId = "74255";
        testApiName = "testGlobalVar";
        testApiNames = Lists.newArrayList(testApiName, "current_intercon_enterprise__g");

        testGlobalVariable = new GlobalVariableDescribe();
        testGlobalVariable.setApiName(testApiName);
        testGlobalVariable.setTenantId(testTenantId);
        testGlobalVariable.setType(IFieldType.TEXT);
        testGlobalVariable.setValue("测试值");
        testGlobalVariable.setDefineType("custom");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找全局变量的正常场景，验证基本的全局变量查询功能
     */
    @Test
    @DisplayName("正常场景 - 查找全局变量成功")
    void testFindGlobalVariables_Success() throws MetadataServiceException {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // 准备测试数据
            List<IGlobalVariableDescribe> mockGlobalVariables = Lists.newArrayList(testGlobalVariable);
            when(globalVariableDescribeService.findByApiNames(anyList(), eq(testTenantId)))
                    .thenReturn(mockGlobalVariables);

            mockedI18N.when(() -> I18N.text(I18NKey.CURRENT_DATE_TIME))
                    .thenReturn("当前日期时间");

            // 执行被测试方法
            Map<String, IGlobalVariableDescribe> result = globalVarService.findGlobalVariables(testTenantId, testApiNames);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size()); // 包含测试变量和current_intercon_enterprise__g
            assertTrue(result.containsKey(testApiName));
            assertTrue(result.containsKey("current_intercon_enterprise__g"));
            verify(globalVariableDescribeService).findByApiNames(anyList(), eq(testTenantId));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找全局变量时，API名称列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 查找全局变量时API名称列表为空返回空Map")
    void testFindGlobalVariables_EmptyApiNames() {
        // 准备测试数据
        Collection<String> emptyApiNames = Lists.newArrayList();

        // 执行被测试方法
        Map<String, IGlobalVariableDescribe> result = globalVarService.findGlobalVariables(testTenantId, emptyApiNames);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(globalVariableDescribeService, never()).findByApiNames(anyList(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找全局变量时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 查找全局变量时服务抛出MetaDataBusinessException")
    void testFindGlobalVariablesThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        MetadataServiceException serviceException = new MetadataServiceException("Service error");
        when(globalVariableDescribeService.findByApiNames(anyList(), eq(testTenantId)))
                .thenThrow(serviceException);

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            globalVarService.findGlobalVariables(testTenantId, testApiNames);
        });

        // 验证异常信息
        assertNotNull(exception);
        verify(globalVariableDescribeService).findByApiNames(anyList(), eq(testTenantId));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建全局变量的正常场景
     */
    @Test
    @DisplayName("正常场景 - 创建全局变量成功")
    void testCreate_Success() throws MetadataServiceException {
        // 准备测试数据
        when(globalVariableDescribeService.create(testGlobalVariable))
                .thenReturn(testGlobalVariable);

        // 执行被测试方法
        GlobalVariableResult result = globalVarService.create(testGlobalVariable);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVariableDescribeService).create(testGlobalVariable);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建全局变量时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 创建全局变量时服务抛出MetaDataBusinessException")
    void testCreateThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        MetadataServiceException serviceException = new MetadataServiceException("Service error");
        when(globalVariableDescribeService.create(testGlobalVariable))
                .thenThrow(serviceException);

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            globalVarService.create(testGlobalVariable);
        });

        // 验证异常信息
        assertNotNull(exception);
        assertEquals("Service error", exception.getMessage());
        verify(globalVariableDescribeService).create(testGlobalVariable);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新全局变量的正常场景
     */
    @Test
    @DisplayName("正常场景 - 更新全局变量成功")
    void testUpdate_Success() throws MetadataServiceException {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // 准备测试数据
            IGlobalVariableDescribe dbGlobalVariable = new GlobalVariableDescribe();
            dbGlobalVariable.setApiName(testApiName);
            dbGlobalVariable.setDefineType("custom");

            when(globalVariableDescribeService.findByApiName(testApiName, testTenantId))
                    .thenReturn(dbGlobalVariable);
            when(globalVariableDescribeService.update(testGlobalVariable))
                    .thenReturn(testGlobalVariable);

            I18N.Context mockContext = mock(I18N.Context.class);
            when(mockContext.getLanguage()).thenReturn("zh_CN");
            mockedI18N.when(I18N::getContext).thenReturn(mockContext);

            // 执行被测试方法
            GlobalVariableResult result = globalVarService.update(testGlobalVariable);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(globalVariableDescribeService).findByApiName(testApiName, testTenantId);
            verify(globalVariableDescribeService).update(testGlobalVariable);
            verify(fieldRelationCalculateService).findRelationFormulaOfGlobalVariable(testGlobalVariable, dbGlobalVariable);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新全局变量时，尝试修改系统变量的场景
     */
    @Test
    @DisplayName("异常场景 - 更新全局变量时尝试修改系统变量抛出MetaDataBusinessException")
    void testUpdateThrowsMetaDataBusinessException_SystemVariable() {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // 准备测试数据
            testGlobalVariable.setDefineType("system");
            mockedI18N.when(() -> I18N.text(I18NKey.GLOBAL_VARIABLE_UNMODIFY))
                    .thenReturn("系统全局变量不可修改");

            // 执行并验证异常
            MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
                globalVarService.update(testGlobalVariable);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(globalVariableDescribeService, never()).update(any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除全局变量的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除全局变量成功")
    void testDelete_Success() throws MetadataServiceException {
        // 准备测试数据
        when(globalVariableDescribeService.findByApiName(testApiName, testTenantId))
                .thenReturn(testGlobalVariable);
        when(globalVariableDescribeService.delete(testGlobalVariable))
                .thenReturn(testGlobalVariable);

        // 执行被测试方法
        GlobalVariableResult result = globalVarService.delete(testApiName, testTenantId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(globalVariableDescribeService).findByApiName(testApiName, testTenantId);
        verify(globalVariableDescribeService).delete(testGlobalVariable);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除全局变量时，变量不存在的场景
     */
    @Test
    @DisplayName("异常场景 - 删除全局变量时变量不存在抛出MetaDataBusinessException")
    void testDeleteThrowsMetaDataBusinessException_VariableNotExists() throws MetadataServiceException {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // 准备测试数据
            when(globalVariableDescribeService.findByApiName(testApiName, testTenantId))
                    .thenReturn(null);
            mockedI18N.when(() -> I18N.text(I18NKey.GLOBAL_VARIABLE_NOT_EXIST_OR_DELETED))
                    .thenReturn("全局变量不存在或已删除");

            // 执行并验证异常
            MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
                globalVarService.delete(testApiName, testTenantId);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(globalVariableDescribeService).findByApiName(testApiName, testTenantId);
            verify(globalVariableDescribeService, never()).delete(any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找全局变量信息的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找全局变量信息成功")
    void testFindGlobalVariableInfo_Success() throws MetadataServiceException {
        // 准备测试数据
        when(globalVariableDescribeService.findByApiName(testApiName, testTenantId))
                .thenReturn(testGlobalVariable);

        // 执行被测试方法
        IGlobalVariableDescribe result = globalVarService.findGlobalVariableInfo(testApiName, testTenantId);

        // 验证结果
        assertNotNull(result);
        assertEquals(testGlobalVariable, result);
        verify(globalVariableDescribeService).findByApiName(testApiName, testTenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找全局变量信息时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 查找全局变量信息时服务抛出MetaDataBusinessException")
    void testFindGlobalVariableInfoThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        MetadataServiceException serviceException = new MetadataServiceException("Service error");
        when(globalVariableDescribeService.findByApiName(testApiName, testTenantId))
                .thenThrow(serviceException);

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            globalVarService.findGlobalVariableInfo(testApiName, testTenantId);
        });

        // 验证异常信息
        assertNotNull(exception);
        verify(globalVariableDescribeService).findByApiName(testApiName, testTenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带实时翻译参数的查找全局变量信息的正常场景
     */
    @Test
    @DisplayName("正常场景 - 带实时翻译参数查找全局变量信息成功")
    void testFindGlobalVariableInfoWithRealTimeTrans_Success() throws MetadataServiceException {
        // 准备测试数据
        Boolean realTimeTrans = true;
        when(globalVariableDescribeService.findByApiName(testApiName, testTenantId))
                .thenReturn(testGlobalVariable);

        // 执行被测试方法
        IGlobalVariableDescribe result = globalVarService.findGlobalVariableInfo(testApiName, realTimeTrans, testTenantId);

        // 验证结果
        assertNotNull(result);
        assertEquals(testGlobalVariable, result);
        verify(globalVariableDescribeService).findByApiName(testApiName, testTenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带语言参数的查找全局变量信息的正常场景
     */
    @Test
    @DisplayName("正常场景 - 带语言参数查找全局变量信息成功")
    void testFindGlobalVariableInfoWithLang_Success() throws MetadataServiceException {
        // 准备测试数据
        Boolean realTimeTrans = true;
        String lang = "zh_CN";
        when(globalVariableDescribeService.findByApiName(testApiName, testTenantId))
                .thenReturn(testGlobalVariable);

        // 执行被测试方法
        IGlobalVariableDescribe result = globalVarService.findGlobalVariableInfo(testApiName, realTimeTrans, lang, testTenantId);

        // 验证结果
        assertNotNull(result);
        assertEquals(testGlobalVariable, result);
        verify(globalVariableDescribeService).findByApiName(testApiName, testTenantId);
    }
}
