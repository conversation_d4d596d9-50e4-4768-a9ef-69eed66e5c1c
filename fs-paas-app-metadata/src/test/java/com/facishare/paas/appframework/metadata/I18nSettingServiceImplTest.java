package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.dto.I18nTrans;
import com.facishare.paas.appframework.metadata.layout.I18nInfo;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.service.IMultipleMultilingualTranslateService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.arg.MultiLingualInfo;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Language;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class I18nSettingServiceImplTest {

    @Mock
    private I18nSettingProxy i18nSettingProxy;

    @Mock
    private LicenseService licenseService;

    @Mock
    private IMultipleMultilingualTranslateService multilingualTranslateService;

    @InjectMocks
    private I18nSettingServiceImpl i18nSettingService;

    private User testUser;
    private String testTenantId;
    private List<String> testKeys;
    private Map<String, String> testTranslations;
    private RequestContext mockRequestContext;

    @BeforeEach
    void setUp() {
        testUser = new User("74255", "testUserId");
        testTenantId = "74255";
        testKeys = Lists.newArrayList("key1", "key2", "key3");
        testTranslations = Maps.newHashMap();
        testTranslations.put("key1", "翻译1");
        testTranslations.put("key2", "翻译2");
        testTranslations.put("key3", "翻译3");

        mockRequestContext = mock(RequestContext.class);
        when(mockRequestContext.getTenantId()).thenReturn(Long.valueOf(testTenantId));
        when(mockRequestContext.getLanguage()).thenReturn("zh_CN");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取翻译值的正常场景，验证基本翻译功能
     */
    @Test
    @DisplayName("正常场景 - 获取翻译值成功")
    void testGetTransValue_Success() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {

            // 准备测试数据
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(mockRequestContext);

            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            when(licenseService.checkLicense(testTenantId, "MULTILINGUAL")).thenReturn(true);
            when(i18nSettingProxy.getTransValue(testKeys, testTenantId, "zh_CN")).thenReturn(testTranslations);

            // 执行被测试方法
            Map<String, String> result = i18nSettingService.getTransValue(testKeys, false, false);

            // 验证结果
            assertNotNull(result);
            assertEquals(testTranslations, result);
            verify(licenseService).checkLicense(testTenantId, "MULTILINGUAL");
            verify(i18nSettingProxy).getTransValue(testKeys, testTenantId, "zh_CN");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取翻译值时，请求上下文为空的场景
     */
    @Test
    @DisplayName("正常场景 - 获取翻译值时请求上下文为空返回空Map")
    void testGetTransValue_NoRequestContext() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {
            // 准备测试数据
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(null);

            // 执行被测试方法
            Map<String, String> result = i18nSettingService.getTransValue(testKeys, false, false);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(licenseService, never()).checkLicense(any(), any());
            verify(i18nSettingProxy, never()).getTransValue(any(), any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取翻译值时，跳过许可证验证的场景
     */
    @Test
    @DisplayName("正常场景 - 获取翻译值时跳过许可证验证")
    void testGetTransValue_SkipLicenseValidate() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {

            // 准备测试数据
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(mockRequestContext);

            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            when(i18nSettingProxy.getTransValue(testKeys, testTenantId, "zh_CN")).thenReturn(testTranslations);

            // 执行被测试方法
            Map<String, String> result = i18nSettingService.getTransValue(testKeys, true, false);

            // 验证结果
            assertNotNull(result);
            assertEquals(testTranslations, result);
            verify(licenseService, never()).checkLicense(any(), any());
            verify(i18nSettingProxy).getTransValue(testKeys, testTenantId, "zh_CN");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取翻译值时，许可证检查失败的场景
     */
    @Test
    @DisplayName("正常场景 - 获取翻译值时许可证检查失败返回空Map")
    void testGetTransValue_LicenseCheckFails() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {

            // 准备测试数据
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(mockRequestContext);

            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            when(licenseService.checkLicense(testTenantId, "MULTILINGUAL")).thenReturn(false);

            // 执行被测试方法
            Map<String, String> result = i18nSettingService.getTransValue(testKeys, false, false);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(licenseService).checkLicense(testTenantId, "MULTILINGUAL");
            verify(i18nSettingProxy, never()).getTransValue(any(), any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存翻译的正常场景
     */
    @Test
    @DisplayName("正常场景 - 保存翻译成功")
    void testSaveTranslation_Success() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {
            // 准备测试数据
            I18nTrans i18nTrans = new I18nTrans();
            i18nTrans.setKey("testKey");
            i18nTrans.setTranslations(testTranslations);

            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(mockRequestContext);

            when(licenseService.checkLicense(testTenantId, "MULTILINGUAL")).thenReturn(true);

            // 执行被测试方法
            boolean result = i18nSettingService.saveTranslation(i18nTrans);

            // 验证结果
            assertTrue(result);
            verify(licenseService).checkLicense(testTenantId, "MULTILINGUAL");
            verify(i18nSettingProxy).saveTranslation(i18nTrans, testTenantId);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存翻译时，请求上下文为空的场景
     */
    @Test
    @DisplayName("正常场景 - 保存翻译时请求上下文为空返回false")
    void testSaveTranslation_NoRequestContext() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {
            // 准备测试数据
            I18nTrans i18nTrans = new I18nTrans();
            i18nTrans.setKey("testKey");

            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(null);

            // 执行被测试方法
            boolean result = i18nSettingService.saveTranslation(i18nTrans);

            // 验证结果
            assertFalse(result);
            verify(licenseService, never()).checkLicense(any(), any());
            verify(i18nSettingProxy, never()).saveTranslation(any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存翻译时，许可证检查失败的场景
     */
    @Test
    @DisplayName("正常场景 - 保存翻译时许可证检查失败返回false")
    void testSaveTranslation_LicenseCheckFails() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {
            // 准备测试数据
            I18nTrans i18nTrans = new I18nTrans();
            i18nTrans.setKey("testKey");

            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(mockRequestContext);

            when(licenseService.checkLicense(testTenantId, "MULTILINGUAL")).thenReturn(false);

            // 执行被测试方法
            boolean result = i18nSettingService.saveTranslation(i18nTrans);

            // 验证结果
            assertFalse(result);
            verify(licenseService).checkLicense(testTenantId, "MULTILINGUAL");
            verify(i18nSettingProxy, never()).saveTranslation(any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取支持的语言列表的正常场景
     */
    @Test
    @DisplayName("正常场景 - 获取支持的语言列表成功")
    void testGetSupportedLanguages_Success() {
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // 准备测试数据
            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            Language language1 = new Language();
            language1.setCode("zh_CN");
            language1.setName("简体中文");
            Language language2 = new Language();
            language2.setCode("en_US");
            language2.setName("English");
            List<Language> languages = Lists.newArrayList(language1, language2);

            when(mockI18nClient.getAllLanguage()).thenReturn(languages);

            // 执行被测试方法
            List<Language> result = i18nSettingService.getSupportedLanguages();

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("zh_CN", result.get(0).getCode());
            assertEquals("en_US", result.get(1).getCode());
            verify(mockI18nClient).getAllLanguage();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取支持的语言列表时，I18nClient返回null的场景
     */
    @Test
    @DisplayName("正常场景 - 获取支持的语言列表时I18nClient返回null返回空列表")
    void testGetSupportedLanguages_I18nClientReturnsNull() {
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // 准备测试数据
            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            when(mockI18nClient.getAllLanguage()).thenReturn(null);

            // 执行被测试方法
            List<Language> result = i18nSettingService.getSupportedLanguages();

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(mockI18nClient).getAllLanguage();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量保存多语言翻译的正常场景
     */
    @Test
    @DisplayName("正常场景 - 批量保存多语言翻译成功")
    void testBatchSaveMultiLingualTranslation_Success() throws MetadataServiceException {
        // 准备测试数据
        List<MultiLingualInfo> multiLingualInfos = Lists.newArrayList();
        MultiLingualInfo info1 = new MultiLingualInfo();
        info1.setKey("key1");
        info1.setTranslations(testTranslations);
        multiLingualInfos.add(info1);

        IActionContext mockActionContext = mock(IActionContext.class);

        // 执行被测试方法
        i18nSettingService.batchSaveMultiLingualTranslation(multiLingualInfos, mockActionContext);

        // 验证结果
        verify(multilingualTranslateService).batchSave(multiLingualInfos, mockActionContext);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量保存多语言翻译时，列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 批量保存多语言翻译时列表为空直接返回")
    void testBatchSaveMultiLingualTranslation_EmptyList() throws MetadataServiceException {
        // 准备测试数据
        List<MultiLingualInfo> emptyList = Lists.newArrayList();
        IActionContext mockActionContext = mock(IActionContext.class);

        // 执行被测试方法
        i18nSettingService.batchSaveMultiLingualTranslation(emptyList, mockActionContext);

        // 验证结果
        verify(multilingualTranslateService, never()).batchSave(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取国际化信息的正常场景
     */
    @Test
    @DisplayName("正常场景 - 获取国际化信息成功")
    void testGetI18nInfo_Success() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {

            // 准备测试数据
            String key = "testKey";
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(mockRequestContext);

            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            when(licenseService.checkLicense(testTenantId, "MULTILINGUAL")).thenReturn(true);

            I18nInfo mockI18nInfo = new I18nInfo();
            mockI18nInfo.setKey(key);
            mockI18nInfo.setTranslations(testTranslations);
            when(i18nSettingProxy.getI18nInfo(key, testTenantId)).thenReturn(mockI18nInfo);

            // 执行被测试方法
            I18nInfo result = i18nSettingService.getI18nInfo(key);

            // 验证结果
            assertNotNull(result);
            assertEquals(key, result.getKey());
            assertEquals(testTranslations, result.getTranslations());
            verify(licenseService).checkLicense(testTenantId, "MULTILINGUAL");
            verify(i18nSettingProxy).getI18nInfo(key, testTenantId);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取国际化信息时，许可证检查失败的场景
     */
    @Test
    @DisplayName("正常场景 - 获取国际化信息时许可证检查失败返回null")
    void testGetI18nInfo_LicenseCheckFails() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {

            // 准备测试数据
            String key = "testKey";
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(mockRequestContext);

            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            when(licenseService.checkLicense(testTenantId, "MULTILINGUAL")).thenReturn(false);

            // 执行被测试方法
            I18nInfo result = i18nSettingService.getI18nInfo(key);

            // 验证结果
            assertNull(result);
            verify(licenseService).checkLicense(testTenantId, "MULTILINGUAL");
            verify(i18nSettingProxy, never()).getI18nInfo(any(), any());
        }
    }
}
