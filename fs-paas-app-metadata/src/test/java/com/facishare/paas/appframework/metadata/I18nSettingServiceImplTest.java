package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.dto.I18nTrans;
import com.facishare.paas.appframework.metadata.i18nKeyPlatform.OnTimeTranslate;
import com.facishare.paas.appframework.metadata.i18nKeyPlatform.Save;
import com.facishare.paas.metadata.api.service.IMultipleMultilingualTranslateService;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Language;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class I18nSettingServiceImplTest {

    @Mock
    private I18nSettingProxy i18nSettingProxy;

    @Mock
    private LicenseService licenseService;

    @Mock
    private IMultipleMultilingualTranslateService multilingualTranslateService;

    @InjectMocks
    private I18nSettingServiceImpl i18nSettingService;

    private String testTenantId;
    private String testLanguage;
    private String testKey;
    private String testDefaultValue;
    private List<String> testKeys;
    private RequestContext mockRequestContext;

    @BeforeEach
    void setUp() {
        testTenantId = "74255";
        testLanguage = "zh_CN";
        testKey = "test.key";
        testDefaultValue = "默认值";
        testKeys = Lists.newArrayList("test.key1", "test.key2");

        mockRequestContext = mock(RequestContext.class);
        when(mockRequestContext.getTenantId()).thenReturn(Long.parseLong(testTenantId));
        Language mockLanguage = mock(Language.class);
        when(mockLanguage.getValue()).thenReturn(testLanguage);
        when(mockRequestContext.getLang()).thenReturn(mockLanguage);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取翻译值的正常场景，验证多语言翻译功能
     */
    @Test
    @DisplayName("正常场景 - 获取翻译值成功")
    void testGetTransValue_Success() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {

            // 准备测试数据
            mockedRequestContextManager.when(RequestContextManager::getContext)
                    .thenReturn(mockRequestContext);

            when(licenseService.isSupportMultiLanguage(testTenantId)).thenReturn(true);

            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            Localization mockLocalization = mock(Localization.class);
            when(mockLocalization.get(testLanguage, null)).thenReturn("翻译值");
            Map<String, Localization> localizationMap = Maps.newHashMap();
            localizationMap.put(testKey, mockLocalization);
            when(mockI18nClient.get(anyList(), anyLong())).thenReturn(localizationMap);

            // 执行被测试方法
            Map<String, String> result = i18nSettingService.getTransValue(testKeys, false, false);

            // 验证结果
            assertNotNull(result);
            verify(licenseService).isSupportMultiLanguage(testTenantId);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取翻译值时，请求上下文为null的场景
     */
    @Test
    @DisplayName("正常场景 - 请求上下文为null时返回空Map")
    void testGetTransValue_NullRequestContext() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {
            // 准备测试数据
            mockedRequestContextManager.when(RequestContextManager::getContext)
                    .thenReturn(null);

            // 执行被测试方法
            Map<String, String> result = i18nSettingService.getTransValue(testKeys, false, false);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(licenseService, never()).isSupportMultiLanguage(anyString());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取单个翻译值的正常场景
     */
    @Test
    @DisplayName("正常场景 - 获取单个翻译值成功")
    void testGetSingleTransValue_Success() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {
            // 准备测试数据
            mockedRequestContextManager.when(RequestContextManager::getContext)
                    .thenReturn(mockRequestContext);

            when(licenseService.isSupportMultiLanguage(testTenantId)).thenReturn(true);

            // 执行被测试方法
            String result = i18nSettingService.getTransValue(testKey, testDefaultValue, false);

            // 验证结果
            assertNotNull(result);
            verify(licenseService).isSupportMultiLanguage(testTenantId);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取单个翻译值时，请求上下文为null的场景
     */
    @Test
    @DisplayName("正常场景 - 获取单个翻译值时请求上下文为null返回默认值")
    void testGetSingleTransValue_NullRequestContext() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {
            // 准备测试数据
            mockedRequestContextManager.when(RequestContextManager::getContext)
                    .thenReturn(null);

            // 执行被测试方法
            String result = i18nSettingService.getTransValue(testKey, testDefaultValue, false);

            // 验证结果
            assertEquals(testDefaultValue, result);
            verify(licenseService, never()).isSupportMultiLanguage(anyString());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取本地化信息的正常场景
     */
    @Test
    @DisplayName("正常场景 - 获取本地化信息成功")
    void testGetLocalization_Success() {
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class);
             MockedStatic<RequestUtil> mockedRequestUtil = mockStatic(RequestUtil.class)) {

            // 准备测试数据
            when(licenseService.isSupportMultiLanguage(testTenantId)).thenReturn(true);
            mockedRequestUtil.when(RequestUtil::isCepRequest).thenReturn(false);

            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            Map<String, Localization> expectedResult = Maps.newHashMap();
            when(mockI18nClient.get(testKeys, Long.parseLong(testTenantId))).thenReturn(expectedResult);

            // 执行被测试方法
            Map<String, Localization> result = i18nSettingService.getLocalization(testKeys, testTenantId, false, false);

            // 验证结果
            assertNotNull(result);
            assertEquals(expectedResult, result);
            verify(licenseService).isSupportMultiLanguage(testTenantId);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取本地化信息时，不支持多语言的场景
     */
    @Test
    @DisplayName("正常场景 - 不支持多语言时返回空Map")
    void testGetLocalization_NotSupportMultiLanguage() {
        // 准备测试数据
        when(licenseService.isSupportMultiLanguage(testTenantId)).thenReturn(false);

        // 执行被测试方法
        Map<String, Localization> result = i18nSettingService.getLocalization(testKeys, testTenantId, false, false);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(licenseService).isSupportMultiLanguage(testTenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试自定义对象保存的正常场景
     */
    @Test
    @DisplayName("正常场景 - 自定义对象保存成功")
    void testCustomObjSave_Success() {
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // 准备测试数据
            Save.Arg mockArg = mock(Save.Arg.class);
            Save.TransDataRow mockDataRow = mock(Save.TransDataRow.class);
            when(mockDataRow.getTranslateKey()).thenReturn(testKey);
            when(mockDataRow.getTranslateValue()).thenReturn("翻译值");
            when(mockArg.getDataRows()).thenReturn(Lists.newArrayList(mockDataRow));
            when(mockArg.getLanguage()).thenReturn(testLanguage);

            when(licenseService.isSupportMultiLanguage(testTenantId)).thenReturn(true);

            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            // 执行被测试方法
            i18nSettingService.customObjSave(mockArg, testTenantId);

            // 验证结果
            verify(mockArg).getDataRows();
            verify(mockArg).getLanguage();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试自定义对象保存时，参数为null的场景
     */
    @Test
    @DisplayName("正常场景 - 自定义对象保存时参数为null直接返回")
    void testCustomObjSave_NullArg() {
        // 执行被测试方法
        i18nSettingService.customObjSave(null, testTenantId);

        // 验证结果 - 由于参数为null，不应该调用任何其他方法
        verify(licenseService, never()).isSupportMultiLanguage(anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存翻译值的正常场景
     */
    @Test
    @DisplayName("正常场景 - 保存翻译值成功")
    void testSaveTransValue_Success() {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {

            // 准备测试数据
            Map<String, String> keyToNewName = Maps.newHashMap();
            keyToNewName.put(testKey, "新翻译值");

            mockedRequestContextManager.when(RequestContextManager::getContext)
                    .thenReturn(mockRequestContext);

            when(licenseService.isSupportMultiLanguage(testTenantId)).thenReturn(true);

            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            // 执行被测试方法
            i18nSettingService.saveTransValue(keyToNewName, true);

            // 验证结果
            verify(licenseService).isSupportMultiLanguage(testTenantId);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存翻译值时，键值对为空的场景
     */
    @Test
    @DisplayName("正常场景 - 保存翻译值时键值对为空直接返回")
    void testSaveTransValue_EmptyKeyToNewName() {
        // 准备测试数据
        Map<String, String> emptyKeyToNewName = Maps.newHashMap();

        // 执行被测试方法
        i18nSettingService.saveTransValue(emptyKeyToNewName, true);

        // 验证结果
        verify(licenseService, never()).isSupportMultiLanguage(anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试同步翻译值的正常场景
     */
    @Test
    @DisplayName("正常场景 - 同步翻译值成功")
    void testSyncTransValue_Success() {
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // 准备测试数据
            Map<String, String> keyToNewName = Maps.newHashMap();
            keyToNewName.put(testKey, "新翻译值");

            when(licenseService.isSupportMultiLanguage(testTenantId)).thenReturn(true);

            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            // 执行被测试方法
            i18nSettingService.syncTransValue(keyToNewName, testLanguage, testTenantId);

            // 验证结果
            verify(licenseService).isSupportMultiLanguage(testTenantId);
            verify(mockI18nClient).save4Translate(eq(Long.parseLong(testTenantId)), anyList(), eq(false));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建本地化信息的正常场景
     */
    @Test
    @DisplayName("正常场景 - 构建本地化信息成功")
    void testBuildLocalizations_Success() {
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // 准备测试数据
            Map<String, String> keyToNewName = Maps.newHashMap();
            keyToNewName.put(testKey, "新翻译值");

            when(licenseService.isSupportMultiLanguage(testTenantId)).thenReturn(true);

            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            Localization mockLocalization = mock(Localization.class);
            when(mockLocalization.get(testLanguage, null)).thenReturn("旧翻译值");
            Map<String, Localization> localizationMap = Maps.newHashMap();
            localizationMap.put(testKey, mockLocalization);
            when(mockI18nClient.get(anyList(), anyLong())).thenReturn(localizationMap);

            // 执行被测试方法
            List<Localization> result = i18nSettingService.buildLocalizations(keyToNewName, testLanguage, testTenantId);

            // 验证结果
            assertNotNull(result);
            verify(licenseService).isSupportMultiLanguage(testTenantId);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建本地化信息时，不支持多语言的场景
     */
    @Test
    @DisplayName("正常场景 - 构建本地化信息时不支持多语言返回空列表")
    void testBuildLocalizations_NotSupportMultiLanguage() {
        // 准备测试数据
        Map<String, String> keyToNewName = Maps.newHashMap();
        keyToNewName.put(testKey, "新翻译值");

        when(licenseService.isSupportMultiLanguage(testTenantId)).thenReturn(false);

        // 执行被测试方法
        List<Localization> result = i18nSettingService.buildLocalizations(keyToNewName, testLanguage, testTenantId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(licenseService).isSupportMultiLanguage(testTenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取翻译值（带参数）时，不支持多语言的场景
     */
    @Test
    @DisplayName("正常场景 - 获取翻译值时不支持多语言返回默认值")
    void testGetTransValueWithParams_NotSupportMultiLanguage() {
        // 准备测试数据
        when(licenseService.isSupportMultiLanguage(testTenantId)).thenReturn(false);

        // 执行被测试方法
        String result = i18nSettingService.getTransValue(testTenantId, testKey, testDefaultValue, testLanguage, false);

        // 验证结果
        assertEquals(testDefaultValue, result);
        verify(licenseService).isSupportMultiLanguage(testTenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试同步翻译值包含前缀键的正常场景
     */
    @Test
    @DisplayName("正常场景 - 同步翻译值包含前缀键成功")
    void testSyncTransValueIncludePreKey_Success() {
        try (MockedStatic<I18nClient> mockedI18nClient = mockStatic(I18nClient.class)) {
            // 准备测试数据
            I18nTrans.TransArg mockTransArg = mock(I18nTrans.TransArg.class);
            when(mockTransArg.getCustomKey()).thenReturn(testKey);
            List<I18nTrans.TransArg> transArgList = Lists.newArrayList(mockTransArg);

            when(licenseService.isSupportMultiLanguage(testTenantId)).thenReturn(true);

            I18nClient mockI18nClient = mock(I18nClient.class);
            mockedI18nClient.when(I18nClient::getInstance).thenReturn(mockI18nClient);

            // 执行被测试方法
            i18nSettingService.syncTransValueIncludePreKey(testTenantId, transArgList, testLanguage);

            // 验证结果
            verify(licenseService).isSupportMultiLanguage(testTenantId);
            verify(mockTransArg).getCustomKey();
        }
    }
}
