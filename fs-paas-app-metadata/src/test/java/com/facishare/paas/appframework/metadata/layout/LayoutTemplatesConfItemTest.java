package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * LayoutTemplatesConf.LayoutTemplatesConfItem单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class LayoutTemplatesConfItemTest {

  private ObjectMapper objectMapper;
  private JsonNode testLayoutTemplate;

  @BeforeEach
  void setUp() {
    objectMapper = new ObjectMapper();
    
    // 创建测试用的JsonNode
    ObjectNode layoutTemplate = objectMapper.createObjectNode();
    layoutTemplate.put("templateName", "Test Template");
    layoutTemplate.put("label", "Test Label");
    layoutTemplate.put("card_style", 1);
    
    // 添加unUseScene数组
    layoutTemplate.set("unUseScene", objectMapper.createArrayNode().add("mobile").add("web"));
    
    testLayoutTemplate = layoutTemplate;
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试LayoutTemplatesConfItem构造函数
   */
  @Test
  @DisplayName("正常场景 - 测试LayoutTemplatesConfItem构造函数")
  void testLayoutTemplatesConfItem_Constructor() {
    try (MockedStatic<JacksonUtils> mockedJacksonUtils = mockStatic(JacksonUtils.class)) {
      // 准备测试数据
      Map<String, Object> templateMap = Maps.newHashMap();
      templateMap.put("templateName", "Test Template");
      templateMap.put("label", "Test Label");
      
      List<String> businessList = Lists.newArrayList("mobile", "web");
      
      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(testLayoutTemplate, Map.class))
          .thenReturn(templateMap);
      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(testLayoutTemplate.get("unUseScene"), List.class))
          .thenReturn(businessList);

      // 执行被测试方法 - 通过反射创建内部类实例
      assertDoesNotThrow(() -> {
        Class<?> innerClass = null;
        for (Class<?> clazz : LayoutTemplatesConf.class.getDeclaredClasses()) {
          if (clazz.getSimpleName().equals("LayoutTemplatesConfItem")) {
            innerClass = clazz;
            break;
          }
        }
        assertNotNull(innerClass);
        Constructor<?> constructor = innerClass.getDeclaredConstructor(JsonNode.class);
        constructor.setAccessible(true);
        Object instance = constructor.newInstance(testLayoutTemplate);
        assertNotNull(instance);
      });
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试LayoutTemplatesConfItem构造函数 - 无unUseScene场景
   */
  @Test
  @DisplayName("边界场景 - 测试无unUseScene的构造")
  void testLayoutTemplatesConfItem_Constructor_NoUnUseScene() {
    try (MockedStatic<JacksonUtils> mockedJacksonUtils = mockStatic(JacksonUtils.class)) {
      // 准备测试数据 - 没有unUseScene字段
      ObjectNode layoutTemplateNoScene = objectMapper.createObjectNode();
      layoutTemplateNoScene.put("templateName", "Test Template");
      layoutTemplateNoScene.put("label", "Test Label");
      
      Map<String, Object> templateMap = Maps.newHashMap();
      templateMap.put("templateName", "Test Template");
      templateMap.put("label", "Test Label");
      
      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(layoutTemplateNoScene, Map.class))
          .thenReturn(templateMap);

      // 执行被测试方法
      assertDoesNotThrow(() -> {
        Class<?> innerClass = null;
        for (Class<?> clazz : LayoutTemplatesConf.class.getDeclaredClasses()) {
          if (clazz.getSimpleName().equals("LayoutTemplatesConfItem")) {
            innerClass = clazz;
            break;
          }
        }
        assertNotNull(innerClass);
        Constructor<?> constructor = innerClass.getDeclaredConstructor(JsonNode.class);
        constructor.setAccessible(true);
        Object instance = constructor.newInstance(layoutTemplateNoScene);
        assertNotNull(instance);
      });
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isAllowCardStyle方法
   */
  @Test
  @DisplayName("正常场景 - 测试isAllowCardStyle方法")
  void testIsAllowCardStyle() {
    try (MockedStatic<JacksonUtils> mockedJacksonUtils = mockStatic(JacksonUtils.class)) {
      // 准备测试数据
      Map<String, Object> templateMap = Maps.newHashMap();
      templateMap.put("card_style", 1);

      List<String> businessList = Lists.newArrayList("mobile", "web");

      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(testLayoutTemplate, Map.class))
          .thenReturn(templateMap);
      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(testLayoutTemplate.get("unUseScene"), List.class))
          .thenReturn(businessList);

      // 执行被测试方法
      assertDoesNotThrow(() -> {
        Class<?> innerClass = null;
        for (Class<?> clazz : LayoutTemplatesConf.class.getDeclaredClasses()) {
          if (clazz.getSimpleName().equals("LayoutTemplatesConfItem")) {
            innerClass = clazz;
            break;
          }
        }
        assertNotNull(innerClass);
        Constructor<?> constructor = innerClass.getDeclaredConstructor(JsonNode.class);
        constructor.setAccessible(true);
        Object instance = constructor.newInstance(testLayoutTemplate);

        // 通过反射调用isAllowCardStyle方法
        Method isAllowCardStyleMethod = innerClass.getDeclaredMethod("isAllowCardStyle", Integer.class);
        isAllowCardStyleMethod.setAccessible(true);

        // 测试匹配的cardStyle
        Boolean result1 = (Boolean) isAllowCardStyleMethod.invoke(instance, 1);
        assertTrue(result1);

        // 测试不匹配的cardStyle
        Boolean result2 = (Boolean) isAllowCardStyleMethod.invoke(instance, 2);
        assertFalse(result2);

        // 测试null cardStyle
        Boolean result3 = (Boolean) isAllowCardStyleMethod.invoke(instance, (Integer) null);
        assertTrue(result3);
      });
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isAllowBusiness方法
   */
  @Test
  @DisplayName("正常场景 - 测试isAllowBusiness方法")
  void testIsAllowBusiness() {
    try (MockedStatic<JacksonUtils> mockedJacksonUtils = mockStatic(JacksonUtils.class)) {
      // 准备测试数据
      Map<String, Object> templateMap = Maps.newHashMap();
      templateMap.put("templateName", "Test Template");
      
      List<String> businessList = Lists.newArrayList("mobile", "web");
      
      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(testLayoutTemplate, Map.class))
          .thenReturn(templateMap);
      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(testLayoutTemplate.get("unUseScene"), List.class))
          .thenReturn(businessList);

      // 执行被测试方法
      assertDoesNotThrow(() -> {
        Class<?> innerClass = null;
        for (Class<?> clazz : LayoutTemplatesConf.class.getDeclaredClasses()) {
          if (clazz.getSimpleName().equals("LayoutTemplatesConfItem")) {
            innerClass = clazz;
            break;
          }
        }
        assertNotNull(innerClass);
        Constructor<?> constructor = innerClass.getDeclaredConstructor(JsonNode.class);
        constructor.setAccessible(true);
        Object instance = constructor.newInstance(testLayoutTemplate);

        // 通过反射调用isAllowBusiness方法
        Method isAllowBusinessMethod = innerClass.getDeclaredMethod("isAllowBusiness", String.class);
        isAllowBusinessMethod.setAccessible(true);

        // 测试不在禁用列表中的business
        Boolean result1 = (Boolean) isAllowBusinessMethod.invoke(instance, "desktop");
        assertTrue(result1);

        // 测试在禁用列表中的business
        Boolean result2 = (Boolean) isAllowBusinessMethod.invoke(instance, "mobile");
        assertFalse(result2);

        // 测试null business（应该默认为abstract）
        Boolean result3 = (Boolean) isAllowBusinessMethod.invoke(instance, (String) null);
        assertTrue(result3);

        // 测试空字符串business（应该默认为abstract）
        Boolean result4 = (Boolean) isAllowBusinessMethod.invoke(instance, "");
        assertTrue(result4);
      });
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试isAllow方法
   */
  @Test
  @DisplayName("正常场景 - 测试isAllow方法")
  void testIsAllow() {
    try (MockedStatic<JacksonUtils> mockedJacksonUtils = mockStatic(JacksonUtils.class)) {
      // 准备测试数据
      Map<String, Object> templateMap = Maps.newHashMap();
      templateMap.put("templateName", "Test Template");

      List<String> businessList = Lists.newArrayList("mobile", "web");

      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(testLayoutTemplate, Map.class))
          .thenReturn(templateMap);
      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(testLayoutTemplate.get("unUseScene"), List.class))
          .thenReturn(businessList);

      // 执行被测试方法
      assertDoesNotThrow(() -> {
        Class<?> innerClass = null;
        for (Class<?> clazz : LayoutTemplatesConf.class.getDeclaredClasses()) {
          if (clazz.getSimpleName().equals("LayoutTemplatesConfItem")) {
            innerClass = clazz;
            break;
          }
        }
        assertNotNull(innerClass);
        Constructor<?> constructor = innerClass.getDeclaredConstructor(JsonNode.class);
        constructor.setAccessible(true);
        Object instance = constructor.newInstance(testLayoutTemplate);

        // 通过反射调用isAllow方法
        Method isAllowMethod = innerClass.getDeclaredMethod("isAllow", String.class);
        isAllowMethod.setAccessible(true);

        // 测试任意ei（由于没有grayRule，应该返回true）
        Boolean result = (Boolean) isAllowMethod.invoke(instance, "testEi");
        assertTrue(result);
      });
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getTemplate方法
   */
  @Test
  @DisplayName("正常场景 - 测试getTemplate方法")
  void testGetTemplate() {
    try (MockedStatic<JacksonUtils> mockedJacksonUtils = mockStatic(JacksonUtils.class)) {
      // 准备测试数据
      Map<String, Object> templateMap = Maps.newHashMap();
      templateMap.put("templateName", "Test Template");
      templateMap.put("label", "Test Label");

      List<String> businessList = Lists.newArrayList("mobile", "web");

      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(testLayoutTemplate, Map.class))
          .thenReturn(templateMap);
      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(testLayoutTemplate.get("unUseScene"), List.class))
          .thenReturn(businessList);

      // 执行被测试方法
      assertDoesNotThrow(() -> {
        Class<?> innerClass = null;
        for (Class<?> clazz : LayoutTemplatesConf.class.getDeclaredClasses()) {
          if (clazz.getSimpleName().equals("LayoutTemplatesConfItem")) {
            innerClass = clazz;
            break;
          }
        }
        assertNotNull(innerClass);
        Constructor<?> constructor = innerClass.getDeclaredConstructor(JsonNode.class);
        constructor.setAccessible(true);
        Object instance = constructor.newInstance(testLayoutTemplate);

        // 通过反射调用getTemplate方法
        Method getTemplateMethod = innerClass.getDeclaredMethod("getTemplate");
        getTemplateMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, Object> result = (Map<String, Object>) getTemplateMethod.invoke(instance);
        assertNotNull(result);
        assertEquals("Test Template", result.get("templateName"));
        assertEquals("Test Label", result.get("label"));
      });
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getAllowTemplates静态方法
   */
  @Test
  @DisplayName("正常场景 - 测试getAllowTemplates静态方法")
  void testGetAllowTemplates() {
    try (MockedStatic<JacksonUtils> mockedJacksonUtils = mockStatic(JacksonUtils.class)) {
      // 准备测试数据
      Map<String, Object> templateMap = Maps.newHashMap();
      templateMap.put("templateName", "Test Template");
      templateMap.put("label", "Test Label");

      List<String> businessList = Lists.newArrayList("mobile", "web");

      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(testLayoutTemplate, Map.class))
          .thenReturn(templateMap);
      mockedJacksonUtils.when(() -> JacksonUtils.convertValue(testLayoutTemplate.get("unUseScene"), List.class))
          .thenReturn(businessList);

      // 执行被测试方法
      assertDoesNotThrow(() -> {
        Class<?> innerClass = null;
        for (Class<?> clazz : LayoutTemplatesConf.class.getDeclaredClasses()) {
          if (clazz.getSimpleName().equals("LayoutTemplatesConfItem")) {
            innerClass = clazz;
            break;
          }
        }
        assertNotNull(innerClass);
        Constructor<?> constructor = innerClass.getDeclaredConstructor(JsonNode.class);
        constructor.setAccessible(true);
        Object instance = constructor.newInstance(testLayoutTemplate);

        List<Object> layoutTemplatesConfItems = Lists.newArrayList(instance);

        // 通过反射调用getAllowTemplates静态方法
        Method getAllowTemplatesMethod = innerClass.getDeclaredMethod("getAllowTemplates", String.class, List.class, String.class, Integer.class);
        getAllowTemplatesMethod.setAccessible(true);

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> result = (List<Map<String, Object>>) getAllowTemplatesMethod.invoke(null, "testEi", layoutTemplatesConfItems, "desktop", 1);
        assertNotNull(result);
      });
    }
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试getAllowTemplates静态方法 - 空列表场景
   */
  @Test
  @DisplayName("边界场景 - 测试getAllowTemplates空列表")
  void testGetAllowTemplates_EmptyList() {
    assertDoesNotThrow(() -> {
      Class<?> innerClass = null;
      for (Class<?> clazz : LayoutTemplatesConf.class.getDeclaredClasses()) {
        if (clazz.getSimpleName().equals("LayoutTemplatesConfItem")) {
          innerClass = clazz;
          break;
        }
      }
      assertNotNull(innerClass);

      // 通过反射调用getAllowTemplates静态方法
      Method getAllowTemplatesMethod = innerClass.getDeclaredMethod("getAllowTemplates", String.class, List.class, String.class, Integer.class);
      getAllowTemplatesMethod.setAccessible(true);

      @SuppressWarnings("unchecked")
      List<Map<String, Object>> result = (List<Map<String, Object>>) getAllowTemplatesMethod.invoke(null, "testEi", Lists.newArrayList(), "desktop", 1);
      assertNotNull(result);
      assertTrue(result.isEmpty());
    });
  }
}
