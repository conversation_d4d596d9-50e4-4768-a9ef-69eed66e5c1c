package com.facishare.paas.appframework.metadata.mtresource;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtResource;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MtResourceServiceImplTest {

    @Mock
    private IRepository<MtResource> repository;

    @Mock
    private DescribeLogicService describeLogicService;

    @InjectMocks
    private MtResourceServiceImpl mtResourceService;

    private String testTenantId;
    private String testResourceParentValue;
    private String testResourceType;
    private List<String> testResourceValues;
    private List<MtResource> testMtResources;
    private User testUser;
    private IObjectDescribe mockObjectDescribe;

    @BeforeEach
    void setUp() {
        testTenantId = "74255";
        testResourceParentValue = "TestObj";
        testResourceType = "field";
        testResourceValues = Lists.newArrayList("field1", "field2", "field3");

        testUser = new User();
        testUser.setTenantId(testTenantId);
        testUser.setUserId("testUserId");

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn("TestObj");

        // 创建测试用的MtResource对象
        MtResource mtResource1 = new MtResource();
        mtResource1.setTenantId(testTenantId);
        mtResource1.setResourceParentValue(testResourceParentValue);
        mtResource1.setResourceType(testResourceType);
        mtResource1.setResourceValue("field1");
        mtResource1.setStatus(MtResource.STATUS_TYPE_ENABLE);

        MtResource mtResource2 = new MtResource();
        mtResource2.setTenantId(testTenantId);
        mtResource2.setResourceParentValue(testResourceParentValue);
        mtResource2.setResourceType(testResourceType);
        mtResource2.setResourceValue("field2");
        mtResource2.setStatus(MtResource.STATUS_TYPE_ENABLE);

        testMtResources = Lists.newArrayList(mtResource1, mtResource2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查询资源的正常场景，验证基本查询功能
     */
    @Test
    @DisplayName("正常场景 - 查询资源成功")
    void testQueryResource_Success() {
        // 准备测试数据
        when(repository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(testMtResources);

        // 执行被测试方法
        List<MtResource> result = mtResourceService.queryResource(
                testTenantId, testResourceParentValue, testResourceType, testResourceValues);

        // 验证结果
        assertNotNull(result);
        assertEquals(testMtResources, result);
        verify(repository).findBy(any(User.class), any(Query.class), eq(MtResource.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带源类型和源值的查询资源功能
     */
    @Test
    @DisplayName("正常场景 - 带源类型和源值查询资源成功")
    void testQueryResourceWithSourceTypeAndValue_Success() {
        // 准备测试数据
        String sourceType = "sourceType";
        String sourceValue = "sourceValue";

        when(repository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(testMtResources);

        // 执行被测试方法
        List<MtResource> result = mtResourceService.queryResource(
                testTenantId, testResourceParentValue, testResourceType, testResourceValues, sourceType, sourceValue);

        // 验证结果
        assertNotNull(result);
        assertEquals(testMtResources, result);
        verify(repository).findBy(any(User.class), any(Query.class), eq(MtResource.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据资源类型查询资源的功能
     */
    @Test
    @DisplayName("正常场景 - 根据资源类型查询资源成功")
    void testQueryResourceByResourceTypes_Success() {
        // 准备测试数据
        Collection<String> resourceTypes = Lists.newArrayList("field", "object");
        String resourceValue = "testValue";

        when(repository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(testMtResources);

        // 执行被测试方法
        List<MtResource> result = mtResourceService.queryResourceByResourceTypes(
                testTenantId, testResourceParentValue, resourceTypes, resourceValue);

        // 验证结果
        assertNotNull(result);
        assertEquals(testMtResources, result);
        verify(repository).findBy(any(User.class), any(Query.class), eq(MtResource.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试修改资源时，资源不存在的场景，应该创建新资源
     */
    @Test
    @DisplayName("正常场景 - 修改资源时资源不存在则创建新资源")
    void testModifyResource_ResourceNotExists() {
        // 准备测试数据
        String controlLevel = "controlLevel";
        String sourceType = "sourceType";
        String sourceValue = "sourceValue";
        List<MtResource> emptyList = Lists.newArrayList();
        List<MtResource> createdResources = Lists.newArrayList(testMtResources.get(0));

        when(repository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(emptyList);
        when(repository.bulkCreate(any(User.class), anyList()))
                .thenReturn(createdResources);

        // 执行被测试方法
        List<MtResource> result = mtResourceService.modifyResource(
                testTenantId, testResourceParentValue, testResourceType, testResourceValues,
                controlLevel, sourceType, sourceValue);

        // 验证结果
        assertNotNull(result);
        assertEquals(createdResources, result);
        verify(repository).findBy(any(User.class), any(Query.class), eq(MtResource.class));
        verify(repository).bulkCreate(any(User.class), anyList());
        verify(repository, never()).bulkUpsert(any(User.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试修改资源时，资源已存在的场景，应该更新现有资源
     */
    @Test
    @DisplayName("正常场景 - 修改资源时资源已存在则更新现有资源")
    void testModifyResource_ResourceExists() {
        // 准备测试数据
        String controlLevel = "newControlLevel";
        String sourceType = "sourceType";
        String sourceValue = "sourceValue";
        List<MtResource> updatedResources = Lists.newArrayList(testMtResources.get(0));

        when(repository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(testMtResources);
        when(repository.bulkUpsert(any(User.class), anyList()))
                .thenReturn(updatedResources);

        // 执行被测试方法
        List<MtResource> result = mtResourceService.modifyResource(
                testTenantId, testResourceParentValue, testResourceType, testResourceValues,
                controlLevel, sourceType, sourceValue);

        // 验证结果
        assertNotNull(result);
        assertEquals(updatedResources, result);
        verify(repository).findBy(any(User.class), any(Query.class), eq(MtResource.class));
        verify(repository).bulkUpsert(any(User.class), anyList());
        verify(repository, never()).bulkCreate(any(User.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试修改资源时，资源值列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 修改资源时资源值列表为空返回空列表")
    void testModifyResource_EmptyResourceValues() {
        // 准备测试数据
        List<String> emptyResourceValues = Lists.newArrayList();
        String controlLevel = "controlLevel";
        String sourceType = "sourceType";
        String sourceValue = "sourceValue";

        // 执行被测试方法
        List<MtResource> result = mtResourceService.modifyResource(
                testTenantId, testResourceParentValue, testResourceType, emptyResourceValues,
                controlLevel, sourceType, sourceValue);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(repository, never()).findBy(any(User.class), any(Query.class), eq(MtResource.class));
        verify(repository, never()).bulkCreate(any(User.class), anyList());
        verify(repository, never()).bulkUpsert(any(User.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用资源的正常场景
     */
    @Test
    @DisplayName("正常场景 - 禁用资源成功")
    void testDisableResource_Success() {
        // 准备测试数据
        String sourceType = "sourceType";

        when(repository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(testMtResources);

        // 执行被测试方法
        mtResourceService.disableResource(
                testTenantId, testResourceParentValue, testResourceType, testResourceValues, sourceType);

        // 验证结果
        verify(repository).findBy(any(User.class), any(Query.class), eq(MtResource.class));
        verify(repository).bulkUpsert(any(User.class), eq(testMtResources));
        
        // 验证资源状态被设置为禁用
        for (MtResource mtResource : testMtResources) {
            assertEquals(MtResource.STATUS_TYPE_DISABLE, mtResource.getStatus());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试禁用资源时，资源不存在的场景
     */
    @Test
    @DisplayName("正常场景 - 禁用资源时资源不存在直接返回")
    void testDisableResource_ResourceNotExists() {
        // 准备测试数据
        String sourceType = "sourceType";
        List<MtResource> emptyList = Lists.newArrayList();

        when(repository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(emptyList);

        // 执行被测试方法
        mtResourceService.disableResource(
                testTenantId, testResourceParentValue, testResourceType, testResourceValues, sourceType);

        // 验证结果
        verify(repository).findBy(any(User.class), any(Query.class), eq(MtResource.class));
        verify(repository, never()).bulkUpsert(any(User.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新描述并修改资源的正常场景
     */
    @Test
    @DisplayName("正常场景 - 更新描述并修改资源成功")
    void testUpdateDescribeAndModifyResource_Success() {
        // 准备测试数据
        Collection<String> resourceValues = Lists.newArrayList("field1", "field2");
        String sourceType = "sourceType";
        String sourceValue = "sourceValue";
        String controlLevel = "controlLevel";

        when(repository.findBy(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(Lists.newArrayList());
        when(repository.bulkCreate(any(User.class), anyList()))
                .thenReturn(testMtResources);

        // 执行被测试方法
        mtResourceService.updateDescribeAndModifyResource(
                testTenantId, mockObjectDescribe, resourceValues, sourceType, sourceValue, controlLevel);

        // 验证结果
        verify(describeLogicService).update(mockObjectDescribe);
        verify(repository).findBy(any(User.class), any(Query.class), eq(MtResource.class));
        verify(repository).bulkCreate(any(User.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据资源类型统计数量的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据资源类型统计数量成功")
    void testCountByResourceType_Success() {
        // 准备测试数据
        Integer expectedCount = 5;

        when(repository.findCountOnly(any(User.class), any(Query.class), eq(MtResource.class)))
                .thenReturn(expectedCount);

        // 执行被测试方法
        Integer result = mtResourceService.countByResourceType(
                testUser, testResourceParentValue, testResourceType);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCount, result);
        verify(repository).findCountOnly(any(User.class), any(Query.class), eq(MtResource.class));
    }
}
