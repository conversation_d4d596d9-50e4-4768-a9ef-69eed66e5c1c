package com.facishare.paas.appframework.metadata.metadatahandle;

import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ObjectDisplayNameHandleImplTest {

    @Mock
    private DescribeLogicService describeLogicService;

    @InjectMocks
    private ObjectDisplayNameHandleImpl objectDisplayNameHandle;

    private String testTenantId;
    private String testApiName;
    private IObjectDescribe mockObjectDescribe;

    @BeforeEach
    void setUp() {
        testTenantId = "74255";
        testApiName = "TestObj";

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getDisplayName()).thenReturn("测试对象");
        when(mockObjectDescribe.getApiName()).thenReturn(testApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称的正常场景，验证能正确返回对象的显示名称
     */
    @Test
    @DisplayName("正常场景 - 查找对象显示名称成功")
    void testFindObjectDisplayName_Success() {
        // 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testApiName))
                .thenReturn(mockObjectDescribe);

        // 执行被测试方法
        String result = objectDisplayNameHandle.findObjectDisplayName(testTenantId, testApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals("测试对象", result);
        verify(describeLogicService).findObjectWithoutCopyIfGray(testTenantId, testApiName);
        verify(mockObjectDescribe).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时，验证参数正确传递给DescribeLogicService
     */
    @Test
    @DisplayName("正常场景 - 验证参数正确传递")
    void testFindObjectDisplayName_VerifyParameterPassing() {
        // 准备测试数据
        String customTenantId = "12345";
        String customApiName = "CustomObj";
        
        IObjectDescribe customObjectDescribe = mock(IObjectDescribe.class);
        when(customObjectDescribe.getDisplayName()).thenReturn("自定义对象");
        
        when(describeLogicService.findObjectWithoutCopyIfGray(customTenantId, customApiName))
                .thenReturn(customObjectDescribe);

        // 执行被测试方法
        String result = objectDisplayNameHandle.findObjectDisplayName(customTenantId, customApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals("自定义对象", result);
        verify(describeLogicService).findObjectWithoutCopyIfGray(eq(customTenantId), eq(customApiName));
        verify(customObjectDescribe).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时，对象描述返回null显示名称的场景
     */
    @Test
    @DisplayName("正常场景 - 对象显示名称为null时返回null")
    void testFindObjectDisplayName_NullDisplayName() {
        // 准备测试数据
        IObjectDescribe nullDisplayNameDescribe = mock(IObjectDescribe.class);
        when(nullDisplayNameDescribe.getDisplayName()).thenReturn(null);
        
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testApiName))
                .thenReturn(nullDisplayNameDescribe);

        // 执行被测试方法
        String result = objectDisplayNameHandle.findObjectDisplayName(testTenantId, testApiName);

        // 验证结果
        assertNull(result);
        verify(describeLogicService).findObjectWithoutCopyIfGray(testTenantId, testApiName);
        verify(nullDisplayNameDescribe).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时，对象显示名称为空字符串的场景
     */
    @Test
    @DisplayName("正常场景 - 对象显示名称为空字符串时返回空字符串")
    void testFindObjectDisplayName_EmptyDisplayName() {
        // 准备测试数据
        IObjectDescribe emptyDisplayNameDescribe = mock(IObjectDescribe.class);
        when(emptyDisplayNameDescribe.getDisplayName()).thenReturn("");
        
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testApiName))
                .thenReturn(emptyDisplayNameDescribe);

        // 执行被测试方法
        String result = objectDisplayNameHandle.findObjectDisplayName(testTenantId, testApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals("", result);
        verify(describeLogicService).findObjectWithoutCopyIfGray(testTenantId, testApiName);
        verify(emptyDisplayNameDescribe).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时，DescribeLogicService抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - DescribeLogicService抛出异常时传播异常")
    void testFindObjectDisplayNameThrowsException_ServiceException() {
        // 准备测试数据
        RuntimeException expectedException = new RuntimeException("Service error");
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testApiName))
                .thenThrow(expectedException);

        // 执行并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            objectDisplayNameHandle.findObjectDisplayName(testTenantId, testApiName);
        });

        // 验证异常信息
        assertEquals(expectedException, exception);
        assertEquals("Service error", exception.getMessage());
        verify(describeLogicService).findObjectWithoutCopyIfGray(testTenantId, testApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时，验证方法调用次数
     */
    @Test
    @DisplayName("正常场景 - 验证方法调用次数")
    void testFindObjectDisplayName_VerifyCallCounts() {
        // 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testApiName))
                .thenReturn(mockObjectDescribe);

        // 执行被测试方法
        String result = objectDisplayNameHandle.findObjectDisplayName(testTenantId, testApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals("测试对象", result);
        
        // 验证方法调用次数
        verify(describeLogicService, times(1)).findObjectWithoutCopyIfGray(testTenantId, testApiName);
        verify(mockObjectDescribe, times(1)).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时，验证不同租户ID和API名称的处理
     */
    @Test
    @DisplayName("正常场景 - 处理不同租户ID和API名称")
    void testFindObjectDisplayName_DifferentTenantAndApiName() {
        // 准备测试数据
        String[] tenantIds = {"74255", "12345", "67890"};
        String[] apiNames = {"TestObj", "CustomObj", "AnotherObj"};
        String[] displayNames = {"测试对象", "自定义对象", "另一个对象"};

        for (int i = 0; i < tenantIds.length; i++) {
            IObjectDescribe describe = mock(IObjectDescribe.class);
            when(describe.getDisplayName()).thenReturn(displayNames[i]);
            when(describeLogicService.findObjectWithoutCopyIfGray(tenantIds[i], apiNames[i]))
                    .thenReturn(describe);

            // 执行被测试方法
            String result = objectDisplayNameHandle.findObjectDisplayName(tenantIds[i], apiNames[i]);

            // 验证结果
            assertNotNull(result);
            assertEquals(displayNames[i], result);
        }

        // 验证所有调用都发生了
        for (int i = 0; i < tenantIds.length; i++) {
            verify(describeLogicService).findObjectWithoutCopyIfGray(tenantIds[i], apiNames[i]);
        }
    }
}
