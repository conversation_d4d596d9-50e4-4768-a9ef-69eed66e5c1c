package com.facishare.paas.appframework.metadata.metadatahandle;

import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ObjectDisplayNameHandleImpl的单元测试
 * 测试对象显示名称处理功能
 */
@ExtendWith(MockitoExtension.class)
class ObjectDisplayNameHandleImplTest {

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private IObjectDescribe mockObjectDescribe;

    @InjectMocks
    private ObjectDisplayNameHandleImpl objectDisplayNameHandle;

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找对象显示名称成功")
    void testFindObjectDisplayName_Success() {
        // 准备测试数据
        String tenantId = "tenant123";
        String apiName = "Account";
        String expectedDisplayName = "客户";

        // 配置Mock行为
        when(describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName))
                .thenReturn(mockObjectDescribe);
        when(mockObjectDescribe.getDisplayName()).thenReturn(expectedDisplayName);

        // 执行测试
        String result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName);

        // 验证结果
        assertEquals(expectedDisplayName, result);
        
        // 验证Mock调用
        verify(describeLogicService).findObjectWithoutCopyIfGray(tenantId, apiName);
        verify(mockObjectDescribe).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时，显示名称为空的场景
     */
    @Test
    @DisplayName("边界场景 - 查找对象显示名称时显示名称为空")
    void testFindObjectDisplayName_EmptyDisplayName() {
        // 准备测试数据
        String tenantId = "tenant123";
        String apiName = "Contact";
        String expectedDisplayName = "";

        // 配置Mock行为
        when(describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName))
                .thenReturn(mockObjectDescribe);
        when(mockObjectDescribe.getDisplayName()).thenReturn(expectedDisplayName);

        // 执行测试
        String result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName);

        // 验证结果
        assertEquals(expectedDisplayName, result);
        
        // 验证Mock调用
        verify(describeLogicService).findObjectWithoutCopyIfGray(tenantId, apiName);
        verify(mockObjectDescribe).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时，显示名称为null的场景
     */
    @Test
    @DisplayName("边界场景 - 查找对象显示名称时显示名称为null")
    void testFindObjectDisplayName_NullDisplayName() {
        // 准备测试数据
        String tenantId = "tenant123";
        String apiName = "Opportunity";

        // 配置Mock行为
        when(describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName))
                .thenReturn(mockObjectDescribe);
        when(mockObjectDescribe.getDisplayName()).thenReturn(null);

        // 执行测试
        String result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName);

        // 验证结果
        assertNull(result);
        
        // 验证Mock调用
        verify(describeLogicService).findObjectWithoutCopyIfGray(tenantId, apiName);
        verify(mockObjectDescribe).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时，包含特殊字符的场景
     */
    @Test
    @DisplayName("正常场景 - 查找对象显示名称包含特殊字符")
    void testFindObjectDisplayName_SpecialCharacters() {
        // 准备测试数据
        String tenantId = "tenant123";
        String apiName = "Custom_Object__c";
        String expectedDisplayName = "自定义对象（测试）";

        // 配置Mock行为
        when(describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName))
                .thenReturn(mockObjectDescribe);
        when(mockObjectDescribe.getDisplayName()).thenReturn(expectedDisplayName);

        // 执行测试
        String result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName);

        // 验证结果
        assertEquals(expectedDisplayName, result);
        
        // 验证Mock调用
        verify(describeLogicService).findObjectWithoutCopyIfGray(tenantId, apiName);
        verify(mockObjectDescribe).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时，租户ID包含特殊字符的场景
     */
    @Test
    @DisplayName("正常场景 - 查找对象显示名称时租户ID包含特殊字符")
    void testFindObjectDisplayName_SpecialTenantId() {
        // 准备测试数据
        String tenantId = "tenant-123_test";
        String apiName = "Account";
        String expectedDisplayName = "客户账户";

        // 配置Mock行为
        when(describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName))
                .thenReturn(mockObjectDescribe);
        when(mockObjectDescribe.getDisplayName()).thenReturn(expectedDisplayName);

        // 执行测试
        String result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName);

        // 验证结果
        assertEquals(expectedDisplayName, result);
        
        // 验证Mock调用
        verify(describeLogicService).findObjectWithoutCopyIfGray(tenantId, apiName);
        verify(mockObjectDescribe).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时，API名称包含特殊字符的场景
     */
    @Test
    @DisplayName("正常场景 - 查找对象显示名称时API名称包含特殊字符")
    void testFindObjectDisplayName_SpecialApiName() {
        // 准备测试数据
        String tenantId = "tenant123";
        String apiName = "Test_Object_v2.0";
        String expectedDisplayName = "测试对象v2.0";

        // 配置Mock行为
        when(describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName))
                .thenReturn(mockObjectDescribe);
        when(mockObjectDescribe.getDisplayName()).thenReturn(expectedDisplayName);

        // 执行测试
        String result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName);

        // 验证结果
        assertEquals(expectedDisplayName, result);
        
        // 验证Mock调用
        verify(describeLogicService).findObjectWithoutCopyIfGray(tenantId, apiName);
        verify(mockObjectDescribe).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找对象显示名称时，显示名称包含多语言字符的场景
     */
    @Test
    @DisplayName("正常场景 - 查找对象显示名称包含多语言字符")
    void testFindObjectDisplayName_MultiLanguageDisplayName() {
        // 准备测试数据
        String tenantId = "tenant123";
        String apiName = "GlobalObject";
        String expectedDisplayName = "客户 Customer العميل お客様";

        // 配置Mock行为
        when(describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName))
                .thenReturn(mockObjectDescribe);
        when(mockObjectDescribe.getDisplayName()).thenReturn(expectedDisplayName);

        // 执行测试
        String result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName);

        // 验证结果
        assertEquals(expectedDisplayName, result);
        
        // 验证Mock调用
        verify(describeLogicService).findObjectWithoutCopyIfGray(tenantId, apiName);
        verify(mockObjectDescribe).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试方法的一致性和不可变性
     */
    @Test
    @DisplayName("一致性验证 - 验证相同输入产生相同输出")
    void testFindObjectDisplayName_Consistency() {
        // 准备测试数据
        String tenantId = "tenant123";
        String apiName = "Account";
        String expectedDisplayName = "客户";

        // 配置Mock行为
        when(describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName))
                .thenReturn(mockObjectDescribe);
        when(mockObjectDescribe.getDisplayName()).thenReturn(expectedDisplayName);

        // 多次执行测试
        String result1 = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName);
        String result2 = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName);
        String result3 = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName);

        // 验证结果一致性
        assertEquals(expectedDisplayName, result1);
        assertEquals(expectedDisplayName, result2);
        assertEquals(expectedDisplayName, result3);
        assertEquals(result1, result2);
        assertEquals(result2, result3);
        
        // 验证Mock调用次数
        verify(describeLogicService, times(3)).findObjectWithoutCopyIfGray(tenantId, apiName);
        verify(mockObjectDescribe, times(3)).getDisplayName();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试依赖注入的正确性
     */
    @Test
    @DisplayName("依赖注入验证 - 验证DescribeLogicService正确注入")
    void testDependencyInjection() {
        // 验证依赖注入
        assertNotNull(objectDisplayNameHandle);
        
        // 通过调用方法验证依赖是否正确注入
        String tenantId = "tenant123";
        String apiName = "Account";
        String expectedDisplayName = "客户";

        when(describeLogicService.findObjectWithoutCopyIfGray(tenantId, apiName))
                .thenReturn(mockObjectDescribe);
        when(mockObjectDescribe.getDisplayName()).thenReturn(expectedDisplayName);

        // 执行测试 - 如果依赖注入失败，这里会抛出NullPointerException
        assertDoesNotThrow(() -> {
            String result = objectDisplayNameHandle.findObjectDisplayName(tenantId, apiName);
            assertEquals(expectedDisplayName, result);
        });
    }
}
