package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.StageViewInfo;
import com.facishare.paas.appframework.metadata.layout.TableComponentRender;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class StageViewLogicServiceImplTest {

    @Mock
    private ConfigService configService;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private LayoutLogicService layoutLogicService;

    @Mock
    private FunctionPrivilegeService functionPrivilegeService;

    @InjectMocks
    private StageViewLogicServiceImpl stageViewLogicService;

    private User testUser;
    private String testDescribeApiName;
    private List<StageViewInfo> testStageViewInfoList;
    private IObjectDescribe mockObjectDescribe;
    private String testConfigKey;
    private String testConfigValue;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setTenantId("74255");
        testUser.setUserId("testUserId");

        testDescribeApiName = "TestObj";
        testConfigKey = testDescribeApiName + "_stage_view";

        StageViewInfo mockStageViewInfo = new StageViewInfo();
        mockStageViewInfo.setFieldApiName("testField");
        mockStageViewInfo.setFieldLabel("测试字段");
        testStageViewInfoList = Lists.newArrayList(mockStageViewInfo);

        testConfigValue = JSON.toJSONString(testStageViewInfoList);

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn(testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存阶段视图的正常场景，验证配置创建功能
     */
    @Test
    @DisplayName("正常场景 - 保存阶段视图成功（创建新配置）")
    void testSaveStageView_CreateNewConfig_Success() {
        // 准备测试数据
        when(configService.findUserConfig(testUser, testConfigKey)).thenReturn(null);

        // 执行被测试方法
        boolean result = stageViewLogicService.saveStageView(testDescribeApiName, testStageViewInfoList, testUser);

        // 验证结果
        assertTrue(result);
        verify(configService).findUserConfig(testUser, testConfigKey);
        verify(configService).createUserConfig(testUser, testConfigKey, testConfigValue, ConfigValueType.JSON);
        verify(configService, never()).updateUserConfig(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存阶段视图的正常场景，验证配置更新功能
     */
    @Test
    @DisplayName("正常场景 - 保存阶段视图成功（更新现有配置）")
    void testSaveStageView_UpdateExistingConfig_Success() {
        // 准备测试数据
        String existingConfig = "existing config";
        when(configService.findUserConfig(testUser, testConfigKey)).thenReturn(existingConfig);

        // 执行被测试方法
        boolean result = stageViewLogicService.saveStageView(testDescribeApiName, testStageViewInfoList, testUser);

        // 验证结果
        assertTrue(result);
        verify(configService).findUserConfig(testUser, testConfigKey);
        verify(configService).updateUserConfig(testUser, testConfigKey, testConfigValue, ConfigValueType.JSON);
        verify(configService, never()).createUserConfig(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存阶段视图时，对象API名称为空的场景
     */
    @Test
    @DisplayName("异常场景 - 保存阶段视图时对象API名称为空抛出ValidateException")
    void testSaveStageViewThrowsValidateException_BlankDescribeApiName() {
        try (MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class)) {
            // 准备测试数据
            String blankDescribeApiName = "";
            mockedI18NExt.when(() -> I18NExt.text(I18NKey.PARAM_ERROR))
                    .thenReturn("参数错误");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                stageViewLogicService.saveStageView(blankDescribeApiName, testStageViewInfoList, testUser);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(configService, never()).findUserConfig(any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存阶段视图时，视图信息列表为空的场景
     */
    @Test
    @DisplayName("异常场景 - 保存阶段视图时视图信息列表为空抛出ValidateException")
    void testSaveStageViewThrowsValidateException_EmptyViewInfo() {
        try (MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class)) {
            // 准备测试数据
            List<StageViewInfo> emptyViewInfo = Lists.newArrayList();
            mockedI18NExt.when(() -> I18NExt.text(I18NKey.PARAM_ERROR))
                    .thenReturn("参数错误");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                stageViewLogicService.saveStageView(testDescribeApiName, emptyViewInfo, testUser);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(configService, never()).findUserConfig(any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找阶段视图的正常场景，验证配置存在时的处理
     */
    @Test
    @DisplayName("正常场景 - 查找阶段视图成功（配置存在）")
    void testFindStageView_ConfigExists_Success() {
        try (MockedStatic<StageViewInfo> mockedStageViewInfo = mockStatic(StageViewInfo.class);
             MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<TableComponentRender> mockedTableComponentRender = mockStatic(TableComponentRender.class)) {

            // 准备测试数据
            when(configService.findUserConfig(testUser, testConfigKey)).thenReturn(testConfigValue);
            when(describeLogicService.findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName))
                    .thenReturn(mockObjectDescribe);

            mockedStageViewInfo.when(() -> StageViewInfo.fromJsonArray(testConfigValue))
                    .thenReturn(testStageViewInfoList);

            TableComponent mockTableComponent = mock(TableComponent.class);
            mockedStageViewInfo.when(() -> StageViewInfo.toTableComponent(testStageViewInfoList))
                    .thenReturn(mockTableComponent);
            mockedStageViewInfo.when(() -> StageViewInfo.fromComponent(mockTableComponent))
                    .thenReturn(testStageViewInfoList);

            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe))
                    .thenReturn(mockObjectDescribeExt);

            TableComponentRender.TableComponentRenderBuilder mockBuilder = mock(TableComponentRender.TableComponentRenderBuilder.class);
            TableComponentRender mockRender = mock(TableComponentRender.class);
            mockedTableComponentRender.when(TableComponentRender::builder).thenReturn(mockBuilder);
            when(mockBuilder.functionPrivilegeService(functionPrivilegeService)).thenReturn(mockBuilder);
            when(mockBuilder.user(testUser)).thenReturn(mockBuilder);
            when(mockBuilder.describeExt(mockObjectDescribeExt)).thenReturn(mockBuilder);
            when(mockBuilder.tableComponentExt(any())).thenReturn(mockBuilder);
            when(mockBuilder.build()).thenReturn(mockRender);

            // 执行被测试方法
            List<StageViewInfo> result = stageViewLogicService.findStageView(testDescribeApiName, testUser);

            // 验证结果
            assertNotNull(result);
            assertEquals(testStageViewInfoList, result);
            verify(configService).findUserConfig(testUser, testConfigKey);
            verify(describeLogicService).findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName);
            verify(mockRender).render();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找阶段视图时，配置不存在且为新商机对象的场景
     */
    @Test
    @DisplayName("正常场景 - 查找阶段视图时配置不存在且为新商机对象")
    void testFindStageView_NoConfigNewOpportunity_Success() {
        try (MockedStatic<Utils> mockedUtils = mockStatic(Utils.class);
             MockedStatic<StageViewInfo> mockedStageViewInfo = mockStatic(StageViewInfo.class);
             MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<TableComponentRender> mockedTableComponentRender = mockStatic(TableComponentRender.class)) {

            // 准备测试数据
            String newOpportunityApiName = "NewOpportunity";
            String oldConfigKey = "new_opportuntiy_user_filter";
            String oldConfig = "old config";

            mockedUtils.when(() -> Utils.NEW_OPPORTUNITY_API_NAME).thenReturn(newOpportunityApiName);

            when(configService.findUserConfig(testUser, newOpportunityApiName + "_stage_view")).thenReturn(null);
            when(configService.findUserConfig(testUser, oldConfigKey)).thenReturn(oldConfig);
            when(describeLogicService.findObjectWithoutCopyIfGray(testUser.getTenantId(), newOpportunityApiName))
                    .thenReturn(mockObjectDescribe);

            mockedStageViewInfo.when(() -> StageViewInfo.fromJsonArray(oldConfig))
                    .thenReturn(testStageViewInfoList);

            TableComponent mockTableComponent = mock(TableComponent.class);
            mockedStageViewInfo.when(() -> StageViewInfo.toTableComponent(testStageViewInfoList))
                    .thenReturn(mockTableComponent);
            mockedStageViewInfo.when(() -> StageViewInfo.fromComponent(mockTableComponent))
                    .thenReturn(testStageViewInfoList);

            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe))
                    .thenReturn(mockObjectDescribeExt);

            TableComponentRender.TableComponentRenderBuilder mockBuilder = mock(TableComponentRender.TableComponentRenderBuilder.class);
            TableComponentRender mockRender = mock(TableComponentRender.class);
            mockedTableComponentRender.when(TableComponentRender::builder).thenReturn(mockBuilder);
            when(mockBuilder.functionPrivilegeService(functionPrivilegeService)).thenReturn(mockBuilder);
            when(mockBuilder.user(testUser)).thenReturn(mockBuilder);
            when(mockBuilder.describeExt(mockObjectDescribeExt)).thenReturn(mockBuilder);
            when(mockBuilder.tableComponentExt(any())).thenReturn(mockBuilder);
            when(mockBuilder.build()).thenReturn(mockRender);

            // 执行被测试方法
            List<StageViewInfo> result = stageViewLogicService.findStageView(newOpportunityApiName, testUser);

            // 验证结果
            assertNotNull(result);
            assertEquals(testStageViewInfoList, result);
            verify(configService).findUserConfig(testUser, newOpportunityApiName + "_stage_view");
            verify(configService).findUserConfig(testUser, oldConfigKey);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找阶段视图时，配置不存在且布局为空的场景
     */
    @Test
    @DisplayName("正常场景 - 查找阶段视图时配置不存在且布局为空返回空列表")
    void testFindStageView_NoConfigEmptyLayout_Success() {
        // 准备测试数据
        when(configService.findUserConfig(testUser, testConfigKey)).thenReturn(null);
        when(describeLogicService.findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName))
                .thenReturn(mockObjectDescribe);
        when(layoutLogicService.findMobileListLayout(testUser, mockObjectDescribe, false))
                .thenReturn(Lists.newArrayList());

        // 执行被测试方法
        List<StageViewInfo> result = stageViewLogicService.findStageView(testDescribeApiName, testUser);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(configService).findUserConfig(testUser, testConfigKey);
        verify(layoutLogicService).findMobileListLayout(testUser, mockObjectDescribe, false);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除阶段视图的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除阶段视图成功")
    void testDeleteStageView_Success() {
        // 执行被测试方法
        boolean result = stageViewLogicService.deleteStageView(testDescribeApiName, testUser);

        // 验证结果
        assertTrue(result);
        verify(configService).deleteUserConfig(testUser, testConfigKey);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除阶段视图时，为新商机对象的场景
     */
    @Test
    @DisplayName("正常场景 - 删除阶段视图时为新商机对象同时删除旧配置")
    void testDeleteStageView_NewOpportunity_Success() {
        try (MockedStatic<Utils> mockedUtils = mockStatic(Utils.class)) {
            // 准备测试数据
            String newOpportunityApiName = "NewOpportunity";
            String oldConfigKey = "new_opportuntiy_user_filter";

            mockedUtils.when(() -> Utils.NEW_OPPORTUNITY_API_NAME).thenReturn(newOpportunityApiName);

            // 执行被测试方法
            boolean result = stageViewLogicService.deleteStageView(newOpportunityApiName, testUser);

            // 验证结果
            assertTrue(result);
            verify(configService).deleteUserConfig(testUser, newOpportunityApiName + "_stage_view");
            verify(configService).deleteUserConfig(testUser, oldConfigKey);
        }
    }
}
