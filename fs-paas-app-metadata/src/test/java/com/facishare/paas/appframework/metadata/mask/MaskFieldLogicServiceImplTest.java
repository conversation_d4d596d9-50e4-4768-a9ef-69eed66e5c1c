package com.facishare.paas.appframework.metadata.mask;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MaskFieldLogicServiceImplTest {

    @Mock
    private UserRoleInfoService userRoleInfoService;

    @Mock
    private OrgService orgService;

    @Mock
    private MaskFieldEncryptService maskFieldEncryptService;

    @InjectMocks
    private MaskFieldLogicServiceImpl maskFieldLogicService;

    private User testUser;
    private User adminUser;
    private User superAdminUser;
    private IObjectDescribe mockObjectDescribe;
    private IFieldDescribe mockMaskField;
    private List<IObjectData> testDataList;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setTenantId("74255");
        testUser.setUserId("testUserId");

        adminUser = new User();
        adminUser.setTenantId("74255");
        adminUser.setUserId("adminUserId");

        superAdminUser = new User();
        superAdminUser.setTenantId("74255");
        superAdminUser.setUserId("superAdminUserId");
        superAdminUser.setSupperAdmin(true);

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn("TestObj");
        when(mockObjectDescribe.getDisplayName()).thenReturn("测试对象");

        mockMaskField = mock(IFieldDescribe.class);
        when(mockMaskField.getApiName()).thenReturn("maskField");
        when(mockMaskField.getLabel()).thenReturn("掩码字段");

        IObjectData testData = new ObjectData();
        testData.setId("testDataId");
        testData.set("owner", "ownerId");
        testDataList = Lists.newArrayList(testData);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取掩码字段时，超级管理员应该返回空Map
     */
    @Test
    @DisplayName("正常场景 - 超级管理员获取掩码字段返回空Map")
    void testGetMaskFields_SuperAdminReturnsEmpty() {
        // 准备测试数据
        Collection<IObjectDescribe> describeList = Lists.newArrayList(mockObjectDescribe);
        String ownerId = "ownerId";

        // 执行被测试方法
        Map<String, List<IFieldDescribe>> result = maskFieldLogicService.getMaskFields(
                superAdminUser, describeList, ownerId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(userRoleInfoService, never()).isAdmin(any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取掩码字段时，普通用户的正常场景
     */
    @Test
    @DisplayName("正常场景 - 普通用户获取掩码字段")
    void testGetMaskFields_RegularUser() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            // 准备测试数据
            Collection<IObjectDescribe> describeList = Lists.newArrayList(mockObjectDescribe);
            String ownerId = "ownerId";
            List<IFieldDescribe> maskFields = Lists.newArrayList(mockMaskField);

            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            when(mockObjectDescribeExt.getMaskFields()).thenReturn(maskFields);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe))
                    .thenReturn(mockObjectDescribeExt);

            when(userRoleInfoService.isAdmin(testUser)).thenReturn(false);

            // 执行被测试方法
            Map<String, List<IFieldDescribe>> result = maskFieldLogicService.getMaskFields(
                    testUser, describeList, ownerId);

            // 验证结果
            assertNotNull(result);
            verify(userRoleInfoService).isAdmin(testUser);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取掩码字段时，管理员用户的场景
     */
    @Test
    @DisplayName("正常场景 - 管理员用户获取掩码字段")
    void testGetMaskFields_AdminUser() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {

            // 准备测试数据
            Collection<IObjectDescribe> describeList = Lists.newArrayList(mockObjectDescribe);
            String ownerId = "ownerId";
            List<IFieldDescribe> maskFields = Lists.newArrayList(mockMaskField);

            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            when(mockObjectDescribeExt.getMaskFields()).thenReturn(maskFields);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe))
                    .thenReturn(mockObjectDescribeExt);

            FieldDescribeExt mockFieldDescribeExt = mock(FieldDescribeExt.class);
            when(mockFieldDescribeExt.haveRolesConfig()).thenReturn(true);
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockMaskField))
                    .thenReturn(mockFieldDescribeExt);

            when(userRoleInfoService.isAdmin(adminUser)).thenReturn(true);

            // 执行被测试方法
            Map<String, List<IFieldDescribe>> result = maskFieldLogicService.getMaskFields(
                    adminUser, describeList, ownerId);

            // 验证结果
            assertNotNull(result);
            verify(userRoleInfoService).isAdmin(adminUser);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试填充掩码字段值时，掩码字段API名称为空的场景
     */
    @Test
    @DisplayName("正常场景 - 掩码字段API名称为空时直接返回")
    void testFillMaskFieldValue_EmptyMaskFieldApiNames() {
        // 准备测试数据
        List<String> emptyMaskFieldApiNames = Lists.newArrayList();

        // 执行被测试方法
        maskFieldLogicService.fillMaskFieldValue(testUser, mockObjectDescribe, emptyMaskFieldApiNames, testDataList);

        // 验证结果 - 由于字段名称为空，不应该调用其他方法
        verify(maskFieldEncryptService, never()).encode(any(IObjectData.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试填充掩码字段值时，灰度配置关闭的场景
     */
    @Test
    @DisplayName("正常场景 - 灰度配置关闭时直接返回")
    void testFillMaskFieldValue_GrayConfigDisabled() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            // 准备测试数据
            List<String> maskFieldApiNames = Lists.newArrayList("maskField");

            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.maskFieldEncryptGray(
                    testUser.getTenantId(), mockObjectDescribe.getApiName()))
                    .thenReturn(false);

            // 执行被测试方法
            maskFieldLogicService.fillMaskFieldValue(testUser, mockObjectDescribe, maskFieldApiNames, testDataList);

            // 验证结果
            verify(maskFieldEncryptService, never()).encode(any(IObjectData.class), anyList());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试编码字段描述的默认值的正常场景
     */
    @Test
    @DisplayName("正常场景 - 编码字段描述的默认值成功")
    void testEncodeDefaultValueByFieldDescribe_Success() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class);
             MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {

            // 准备测试数据
            FieldDescribeExt mockFieldDescribeExt = mock(FieldDescribeExt.class);
            when(mockFieldDescribeExt.getMaskFieldEncrypt()).thenReturn(true);
            when(mockFieldDescribeExt.isCalculateField()).thenReturn(false);
            when(mockFieldDescribeExt.getDefaultValue()).thenReturn("defaultValue");

            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockMaskField))
                    .thenReturn(mockFieldDescribeExt);
            mockedObjectDataExt.when(() -> ObjectDataExt.isValueEmpty("defaultValue"))
                    .thenReturn(false);
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.getMaskEncryptFieldName("maskField"))
                    .thenReturn("maskField__s");

            // 执行被测试方法
            maskFieldLogicService.encodeDefaultValueByFieldDescribe(testUser, mockMaskField);

            // 验证结果
            verify(maskFieldEncryptService).encode(any(IObjectData.class), eq(Lists.newArrayList(mockMaskField)));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试编码字段描述的默认值时，掩码字段加密配置关闭的场景
     */
    @Test
    @DisplayName("正常场景 - 掩码字段加密配置关闭时直接返回")
    void testEncodeDefaultValueByFieldDescribe_EncryptConfigDisabled() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 准备测试数据
            FieldDescribeExt mockFieldDescribeExt = mock(FieldDescribeExt.class);
            when(mockFieldDescribeExt.getMaskFieldEncrypt()).thenReturn(false);

            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockMaskField))
                    .thenReturn(mockFieldDescribeExt);

            // 执行被测试方法
            maskFieldLogicService.encodeDefaultValueByFieldDescribe(testUser, mockMaskField);

            // 验证结果
            verify(maskFieldEncryptService, never()).encode(any(IObjectData.class), anyList());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解码掩码字段加密值的正常场景
     */
    @Test
    @DisplayName("正常场景 - 解码掩码字段加密值成功")
    void testDecodeMaskFieldEncryptValue_Success() {
        // 执行被测试方法
        maskFieldLogicService.decodeMaskFieldEncryptValue(testUser, testDataList, mockObjectDescribe);

        // 验证结果
        verify(maskFieldEncryptService).decode(any(IObjectData.class), eq(mockObjectDescribe));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解码掩码字段加密值时，数据列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 数据列表为空时直接返回")
    void testDecodeMaskFieldEncryptValue_EmptyDataList() {
        // 准备测试数据
        List<IObjectData> emptyDataList = Lists.newArrayList();

        // 执行被测试方法
        maskFieldLogicService.decodeMaskFieldEncryptValue(testUser, emptyDataList, mockObjectDescribe);

        // 验证结果
        verify(maskFieldEncryptService, never()).decode(any(IObjectData.class), any(IObjectDescribe.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取掩码字段类型的正常场景
     */
    @Test
    @DisplayName("正常场景 - 获取掩码字段类型成功")
    void testGetMaskFieldTypes_Success() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            // 准备测试数据
            String tenantId = "74255";
            String describeApiName = "TestObj";

            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.maskFieldEncryptGray(tenantId, describeApiName))
                    .thenReturn(true);

            // 执行被测试方法
            List<String> result = maskFieldLogicService.getMaskFieldTypes(tenantId, describeApiName);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.contains(IFieldType.CURRENCY));
            assertTrue(result.contains(IFieldType.EMAIL));
            assertTrue(result.contains(IFieldType.PHONE_NUMBER));
            assertTrue(result.contains(IFieldType.NUMBER));
            assertTrue(result.contains(IFieldType.TEXT));
            assertTrue(result.contains(IFieldType.COUNT));
            assertTrue(result.contains(IFieldType.FORMULA));
            assertTrue(result.contains(IFieldType.QUOTE));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取掩码字段类型时，灰度配置关闭的场景
     */
    @Test
    @DisplayName("正常场景 - 灰度配置关闭时返回基础字段类型")
    void testGetMaskFieldTypes_GrayConfigDisabled() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            // 准备测试数据
            String tenantId = "74255";
            String describeApiName = "TestObj";

            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.maskFieldEncryptGray(tenantId, describeApiName))
                    .thenReturn(false);

            // 执行被测试方法
            List<String> result = maskFieldLogicService.getMaskFieldTypes(tenantId, describeApiName);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.contains(IFieldType.CURRENCY));
            assertTrue(result.contains(IFieldType.EMAIL));
            assertTrue(result.contains(IFieldType.PHONE_NUMBER));
            assertFalse(result.contains(IFieldType.NUMBER));
            assertFalse(result.contains(IFieldType.TEXT));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试掩码字段角色过滤的正常场景
     */
    @Test
    @DisplayName("正常场景 - 掩码字段角色过滤成功")
    void testMaskFieldRoleFilter_Success() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 准备测试数据
            List<IFieldDescribe> maskFields = Lists.newArrayList(mockMaskField);
            List<String> userRoles = Lists.newArrayList("role1", "role2");
            List<String> maskRolesConfig = Lists.newArrayList("role1");

            FieldDescribeExt mockFieldDescribeExt = mock(FieldDescribeExt.class);
            when(mockFieldDescribeExt.haveRolesConfig(testUser)).thenReturn(true);
            when(mockFieldDescribeExt.getMaskRolesConfig(testUser)).thenReturn(maskRolesConfig);

            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockMaskField))
                    .thenReturn(mockFieldDescribeExt);

            when(userRoleInfoService.getUserRole(testUser)).thenReturn(userRoles);

            // 执行被测试方法
            List<IFieldDescribe> result = maskFieldLogicService.maskFieldRoleFilter(testUser, maskFields);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty()); // 用户有role1权限，所以该字段被过滤掉
            verify(userRoleInfoService).getUserRole(testUser);
        }
    }
}
