package com.facishare.paas.appframework.metadata.mask;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MaskFieldLogicServiceImplTest {

    @Mock
    private UserRoleInfoService userRoleInfoService;

    @Mock
    private OrgService orgService;

    @Mock
    private MaskFieldEncryptService maskFieldEncryptService;

    @InjectMocks
    private MaskFieldLogicServiceImpl maskFieldLogicService;

    private User testUser;
    private User superAdminUser;
    private String testOwnerId;
    private IObjectDescribe mockObjectDescribe;
    private IFieldDescribe mockMaskField;
    private List<IObjectDescribe> testDescribeList;
    private List<IObjectData> testObjectDataList;

    @BeforeEach
    void setUp() {
        testUser = new User("74255", "testUserId");
        superAdminUser = new User("74255", "superAdminUserId");
        testOwnerId = "ownerId123";

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn("TestObj");

        mockMaskField = mock(IFieldDescribe.class);
        when(mockMaskField.getApiName()).thenReturn("phone__c");
        when(mockMaskField.getType()).thenReturn(IFieldType.PHONE);

        testDescribeList = Lists.newArrayList(mockObjectDescribe);

        IObjectData mockObjectData = new ObjectData();
        mockObjectData.setId("testDataId");
        mockObjectData.put("phone__c", "13812345678");
        testObjectDataList = Lists.newArrayList(mockObjectData);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取掩码字段的正常场景，验证超级管理员用户的处理
     */
    @Test
    @DisplayName("正常场景 - 超级管理员用户获取掩码字段返回空Map")
    void testGetMaskFields_SuperAdmin_ReturnsEmptyMap() {
        // 准备测试数据
        when(superAdminUser.isSupperAdmin()).thenReturn(true);

        // 执行被测试方法
        Map<String, List<IFieldDescribe>> result = maskFieldLogicService.getMaskFields(
                superAdminUser, testDescribeList, testOwnerId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(userRoleInfoService, never()).findUserRoleInfo(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取掩码字段的正常场景，验证普通用户的处理
     */
    @Test
    @DisplayName("正常场景 - 普通用户获取掩码字段成功")
    void testGetMaskFields_RegularUser_Success() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            // 准备测试数据
            when(testUser.isSupperAdmin()).thenReturn(false);

            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe))
                    .thenReturn(mockObjectDescribeExt);
            when(mockObjectDescribeExt.getMaskFields()).thenReturn(Lists.newArrayList(mockMaskField));

            // 执行被测试方法
            Map<String, List<IFieldDescribe>> result = maskFieldLogicService.getMaskFields(
                    testUser, testDescribeList, testOwnerId);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertTrue(result.containsKey("TestObj"));
            assertEquals(1, result.get("TestObj").size());
            assertEquals(mockMaskField, result.get("TestObj").get(0));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取掩码字段时，对象描述列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 获取掩码字段时对象描述列表为空返回空Map")
    void testGetMaskFields_EmptyDescribeList() {
        // 准备测试数据
        when(testUser.isSupperAdmin()).thenReturn(false);
        Collection<IObjectDescribe> emptyDescribeList = Lists.newArrayList();

        // 执行被测试方法
        Map<String, List<IFieldDescribe>> result = maskFieldLogicService.getMaskFields(
                testUser, emptyDescribeList, testOwnerId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解码掩码字段加密值的正常场景
     */
    @Test
    @DisplayName("正常场景 - 解码掩码字段加密值成功")
    void testDecodeMaskFieldEncryptValue_Success() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class);
             MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {

            // 准备测试数据
            mockedAppFrameworkConfig.when(AppFrameworkConfig::isOpenMaskFieldEncrypt)
                    .thenReturn(true);

            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            mockedObjectDataExt.when(() -> ObjectDataExt.of(any(IObjectData.class)))
                    .thenReturn(mockObjectDataExt);
            when(mockObjectDataExt.getMaskFieldEncryptValue()).thenReturn(Maps.newHashMap());

            when(maskFieldEncryptService.decode(eq(testUser), anyMap(), eq(mockObjectDescribe)))
                    .thenReturn(testObjectDataList);

            // 执行被测试方法
            List<IObjectData> result = maskFieldLogicService.decodeMaskFieldEncryptValue(
                    testUser, testObjectDataList, mockObjectDescribe);

            // 验证结果
            assertNotNull(result);
            assertEquals(testObjectDataList, result);
            verify(maskFieldEncryptService).decode(eq(testUser), anyMap(), eq(mockObjectDescribe));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解码掩码字段加密值时，掩码字段加密功能关闭的场景
     */
    @Test
    @DisplayName("正常场景 - 解码掩码字段加密值时功能关闭直接返回原数据")
    void testDecodeMaskFieldEncryptValue_FeatureDisabled() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            // 准备测试数据
            mockedAppFrameworkConfig.when(AppFrameworkConfig::isOpenMaskFieldEncrypt)
                    .thenReturn(false);

            // 执行被测试方法
            List<IObjectData> result = maskFieldLogicService.decodeMaskFieldEncryptValue(
                    testUser, testObjectDataList, mockObjectDescribe);

            // 验证结果
            assertNotNull(result);
            assertEquals(testObjectDataList, result);
            verify(maskFieldEncryptService, never()).decode(any(), any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解码掩码字段加密值时，对象数据列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 解码掩码字段加密值时对象数据列表为空直接返回")
    void testDecodeMaskFieldEncryptValue_EmptyObjectDataList() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            // 准备测试数据
            mockedAppFrameworkConfig.when(AppFrameworkConfig::isOpenMaskFieldEncrypt)
                    .thenReturn(true);
            List<IObjectData> emptyObjectDataList = Lists.newArrayList();

            // 执行被测试方法
            List<IObjectData> result = maskFieldLogicService.decodeMaskFieldEncryptValue(
                    testUser, emptyObjectDataList, mockObjectDescribe);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(maskFieldEncryptService, never()).decode(any(), any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试编码掩码字段加密值的正常场景
     */
    @Test
    @DisplayName("正常场景 - 编码掩码字段加密值成功")
    void testEncodeMaskFieldEncryptValue_Success() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class);
             MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {

            // 准备测试数据
            mockedAppFrameworkConfig.when(AppFrameworkConfig::isOpenMaskFieldEncrypt)
                    .thenReturn(true);

            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe))
                    .thenReturn(mockObjectDescribeExt);
            when(mockObjectDescribeExt.getMaskFields()).thenReturn(Lists.newArrayList(mockMaskField));

            when(maskFieldEncryptService.encode(eq(testUser), eq(testObjectDataList), eq(mockObjectDescribe)))
                    .thenReturn(testObjectDataList);

            // 执行被测试方法
            List<IObjectData> result = maskFieldLogicService.encodeMaskFieldEncryptValue(
                    testUser, testObjectDataList, mockObjectDescribe);

            // 验证结果
            assertNotNull(result);
            assertEquals(testObjectDataList, result);
            verify(maskFieldEncryptService).encode(eq(testUser), eq(testObjectDataList), eq(mockObjectDescribe));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试编码掩码字段加密值时，掩码字段加密功能关闭的场景
     */
    @Test
    @DisplayName("正常场景 - 编码掩码字段加密值时功能关闭直接返回原数据")
    void testEncodeMaskFieldEncryptValue_FeatureDisabled() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            // 准备测试数据
            mockedAppFrameworkConfig.when(AppFrameworkConfig::isOpenMaskFieldEncrypt)
                    .thenReturn(false);

            // 执行被测试方法
            List<IObjectData> result = maskFieldLogicService.encodeMaskFieldEncryptValue(
                    testUser, testObjectDataList, mockObjectDescribe);

            // 验证结果
            assertNotNull(result);
            assertEquals(testObjectDataList, result);
            verify(maskFieldEncryptService, never()).encode(any(), any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试编码掩码字段加密值时，没有掩码字段的场景
     */
    @Test
    @DisplayName("正常场景 - 编码掩码字段加密值时没有掩码字段直接返回原数据")
    void testEncodeMaskFieldEncryptValue_NoMaskFields() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class);
             MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {

            // 准备测试数据
            mockedAppFrameworkConfig.when(AppFrameworkConfig::isOpenMaskFieldEncrypt)
                    .thenReturn(true);

            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe))
                    .thenReturn(mockObjectDescribeExt);
            when(mockObjectDescribeExt.getMaskFields()).thenReturn(Lists.newArrayList()); // 没有掩码字段

            // 执行被测试方法
            List<IObjectData> result = maskFieldLogicService.encodeMaskFieldEncryptValue(
                    testUser, testObjectDataList, mockObjectDescribe);

            // 验证结果
            assertNotNull(result);
            assertEquals(testObjectDataList, result);
            verify(maskFieldEncryptService, never()).encode(any(), any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否需要掩码的正常场景
     */
    @Test
    @DisplayName("正常场景 - 检查是否需要掩码成功")
    void testCheckNeedMask_Success() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            // 准备测试数据
            when(testUser.isSupperAdmin()).thenReturn(false);

            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe))
                    .thenReturn(mockObjectDescribeExt);
            when(mockObjectDescribeExt.getMaskFields()).thenReturn(Lists.newArrayList(mockMaskField));

            // 执行被测试方法
            boolean result = maskFieldLogicService.checkNeedMask(testUser, mockObjectDescribe, testOwnerId);

            // 验证结果
            assertTrue(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否需要掩码时，用户是超级管理员的场景
     */
    @Test
    @DisplayName("正常场景 - 检查是否需要掩码时用户是超级管理员返回false")
    void testCheckNeedMask_SuperAdmin_ReturnsFalse() {
        // 准备测试数据
        when(superAdminUser.isSupperAdmin()).thenReturn(true);

        // 执行被测试方法
        boolean result = maskFieldLogicService.checkNeedMask(superAdminUser, mockObjectDescribe, testOwnerId);

        // 验证结果
        assertFalse(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否需要掩码时，没有掩码字段的场景
     */
    @Test
    @DisplayName("正常场景 - 检查是否需要掩码时没有掩码字段返回false")
    void testCheckNeedMask_NoMaskFields_ReturnsFalse() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {
            // 准备测试数据
            when(testUser.isSupperAdmin()).thenReturn(false);

            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe))
                    .thenReturn(mockObjectDescribeExt);
            when(mockObjectDescribeExt.getMaskFields()).thenReturn(Lists.newArrayList()); // 没有掩码字段

            // 执行被测试方法
            boolean result = maskFieldLogicService.checkNeedMask(testUser, mockObjectDescribe, testOwnerId);

            // 验证结果
            assertFalse(result);
        }
    }
}
