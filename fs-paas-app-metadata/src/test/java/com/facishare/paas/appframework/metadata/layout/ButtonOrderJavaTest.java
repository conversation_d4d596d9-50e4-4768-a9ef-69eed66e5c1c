package com.facishare.paas.appframework.metadata.layout;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.ButtonUsePageType;
import com.facishare.paas.appframework.metadata.LayoutButtonExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ButtonOrder 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ButtonOrder 测试")
class ButtonOrderJavaTest {

  @Mock
  private LayoutExt layoutExt;

  @Mock
  private ObjectDescribeExt describeExt;

  @Mock
  private IUdefButton udefButton;

  private List<IButton> targetButtons;
  private List<IButton> templateButtons;

  @BeforeEach
  void setUp() {
    targetButtons = Lists.newArrayList();
    templateButtons = Lists.newArrayList();
  }

  @Test
  @DisplayName("测试按钮排序 - 正常场景")
  void testOrderingByTemplate_Success() {
    // 准备测试数据
    IButton targetButton1 = createButton("button1", "Button 1");
    IButton targetButton2 = createButton("button2", "Button 2");
    IButton targetButton3 = createButton("button3", "Button 3");
    targetButtons.add(targetButton1);
    targetButtons.add(targetButton2);
    targetButtons.add(targetButton3);

    IButton templateButton1 = createButton("button2", "Template Button 2");
    IButton templateButton2 = createButton("button1", "Template Button 1");
    templateButtons.add(templateButton1);
    templateButtons.add(templateButton2);

    // 执行测试
    List<IButton> result = ButtonOrder.orderingByTemplate(targetButtons, templateButtons);

    // 验证结果
    assertNotNull(result);
    assertEquals(3, result.size());
    assertEquals("button2", result.get(0).getName());
    assertEquals("button1", result.get(1).getName());
    assertEquals("button3", result.get(2).getName());
  }

  @Test
  @DisplayName("测试按钮排序 - 空模板列表")
  void testOrderingByTemplate_EmptyTemplate() {
    // 准备测试数据
    IButton targetButton = createButton("button1", "Button 1");
    targetButtons.add(targetButton);

    // 执行测试
    List<IButton> result = ButtonOrder.orderingByTemplate(targetButtons, templateButtons);

    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("button1", result.get(0).getName());
  }

  @Test
  @DisplayName("测试过滤自定义按钮 - 正常场景")
  void testFilteringUdefButton_Success() {
    // 准备测试数据
    when(udefButton.isDeleted()).thenReturn(false);
    when(udefButton.getApiName()).thenReturn("custom_button__c");
    when(udefButton.getLabel()).thenReturn("Custom Button");
    when(udefButton.isActive()).thenReturn(true);

    List<IUdefButton> udefButtons = Lists.newArrayList(udefButton);

    // 执行测试
    List<IButton> result = ButtonOrder.filteringUdefButton(udefButtons);

    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("custom_button__c", result.get(0).getName());
    assertEquals("Custom Button", result.get(0).getLabel());
    assertEquals(IButton.ACTION_TYPE_CUSTOM, result.get(0).getActionType());
  }

  @Test
  @DisplayName("测试过滤自定义按钮 - 过滤已删除按钮")
  void testFilteringUdefButton_FilterDeleted() {
    // 准备测试数据
    when(udefButton.isDeleted()).thenReturn(true);

    List<IUdefButton> udefButtons = Lists.newArrayList(udefButton);

    // 执行测试
    List<IButton> result = ButtonOrder.filteringUdefButton(udefButtons);

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  @DisplayName("测试过滤自定义按钮 - 过滤非自定义按钮")
  void testFilteringUdefButton_FilterNonCustom() {
    // 准备测试数据
    when(udefButton.isDeleted()).thenReturn(false);
    when(udefButton.getApiName()).thenReturn("standard_button"); // 不以__c结尾

    List<IUdefButton> udefButtons = Lists.newArrayList(udefButton);

    // 执行测试
    List<IButton> result = ButtonOrder.filteringUdefButton(udefButtons);

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  @DisplayName("测试更新布局中的自定义按钮 - 正常场景")
  void testUpdateCustomButtonWithLayout_Success() {
    // 准备测试数据
    IButton customButton = createButton("custom_button__c", "Custom Button");
    customButton.set("isActive", true);
    List<IButton> customButtons = Lists.newArrayList(customButton);

    IButton layoutButton = createButton("custom_button__c", "Layout Button");
    layoutButton.set("isActive", false);
    List<IButton> buttonOrder = Lists.newArrayList(layoutButton);

    when(layoutExt.getButtonOrder()).thenReturn(buttonOrder);
    when(layoutExt.getHiddenButtons()).thenReturn(Lists.newArrayList());

    // 执行测试
    ButtonOrder.updateCustomButtonWithLayout(customButtons, layoutExt);

    // 验证结果
    verify(layoutExt).setButtonOrder(any(List.class));
    assertEquals(true, layoutButton.get("isActive", Boolean.class));
    assertEquals("Custom Button", layoutButton.getLabel());
  }

  @Test
  @DisplayName("测试更新布局中的自定义按钮 - 空自定义按钮列表")
  void testUpdateCustomButtonWithLayout_EmptyCustomButtons() {
    // 准备测试数据
    IButton systemButton = createButton("system_button", "System Button");
    IButton customButton = createButton("custom_button__c", "Custom Button");
    List<IButton> buttonOrder = Lists.newArrayList(systemButton, customButton);

    when(layoutExt.getButtonOrder()).thenReturn(buttonOrder);

    try (MockedStatic<LayoutButtonExt> layoutButtonExtMock = mockStatic(LayoutButtonExt.class)) {
      LayoutButtonExt mockLayoutButtonExt = mock(LayoutButtonExt.class);
      layoutButtonExtMock.when(() -> LayoutButtonExt.of(any(IButton.class))).thenReturn(mockLayoutButtonExt);
      when(mockLayoutButtonExt.isSystemButton()).thenReturn(true, false);

      // 执行测试
      ButtonOrder.updateCustomButtonWithLayout(Lists.newArrayList(), layoutExt);

      // 验证结果
      verify(layoutExt).setButtonOrder(any(List.class));
    }
  }

  @Test
  @DisplayName("测试追加新的自定义按钮")
  void testBackhanderNewCustomButtons_Success() {
    // 准备测试数据
    IButton existingButton = createButton("existing_button__c", "Existing Button");
    IButton newButton = createButton("new_button__c", "New Button");

    List<IButton> buttonOrder = Lists.newArrayList(existingButton);
    List<IButton> customButtons = Lists.newArrayList(existingButton, newButton);

    when(layoutExt.getButtonOrder()).thenReturn(buttonOrder);

    try (MockedStatic<LayoutButtonExt> layoutButtonExtMock = mockStatic(LayoutButtonExt.class)) {
      LayoutButtonExt mockLayoutButtonExt = mock(LayoutButtonExt.class);
      layoutButtonExtMock.when(() -> LayoutButtonExt.of(any(IButton.class))).thenReturn(mockLayoutButtonExt);
      when(mockLayoutButtonExt.isAIAgent()).thenReturn(false);

      // 执行测试
      ButtonOrder.backhanderNewCustomButtons(layoutExt, customButtons);

      // 验证结果
      verify(layoutExt).setButtonOrder(any(List.class));
    }
  }

  @Test
  @DisplayName("测试获取排序按钮列表")
  void testGetOrderedButtonList_Success() {
    // 准备测试数据
    IButton customButton = createButton("custom_button__c", "Custom Button");
    List<IButton> customButtons = Lists.newArrayList(customButton);

    try (MockedStatic<LayoutButtons> layoutButtonsMock = mockStatic(LayoutButtons.class)) {
      LayoutButtons mockLayoutButtons = mock(LayoutButtons.class);
      layoutButtonsMock.when(() -> LayoutButtons.getInstance(any())).thenReturn(mockLayoutButtons);
      when(mockLayoutButtons.getActionButtons()).thenReturn(Lists.newArrayList());
      when(mockLayoutButtons.getTailButtons()).thenReturn(Lists.newArrayList());

      // 执行测试
      List<IButton> result = ButtonOrder.getOrderedButtonList(customButtons, describeExt);

      // 验证结果
      assertNotNull(result);
    }
  }

  @Test
  @DisplayName("测试按使用页面类型获取排序按钮列表 - Detail页面")
  void testGetOrderButtonListByUsePage_Detail() {
    // 准备测试数据
    List<IButton> customButtons = Lists.newArrayList();

    try (MockedStatic<LayoutButtons> layoutButtonsMock = mockStatic(LayoutButtons.class)) {
      LayoutButtons mockLayoutButtons = mock(LayoutButtons.class);
      layoutButtonsMock.when(() -> LayoutButtons.getInstance(any())).thenReturn(mockLayoutButtons);
      when(mockLayoutButtons.getActionButtons()).thenReturn(Lists.newArrayList());
      when(mockLayoutButtons.getTailButtons()).thenReturn(Lists.newArrayList());

      // 执行测试
      List<IButton> result = ButtonOrder.getOrderButtonListByUsePage(customButtons, describeExt, ButtonUsePageType.Detail);

      // 验证结果
      assertNotNull(result);
    }
  }

  @Test
  @DisplayName("测试转换Deal按钮为Transfer按钮")
  void testConvertDealToTransfer_Success() {
    // 准备测试数据
    IButton dealButton = createButton(ObjectAction.DEAL.getButtonApiName(), "Deal");
    dealButton.setAction(ObjectAction.DEAL.getActionCode());
    List<IButton> buttons = Lists.newArrayList(dealButton);

    // 由于Utils.LEADS_API_NAME是静态final字段，我们直接使用其值进行测试

    // 执行测试
    ButtonOrder.convertDealToTransfer(buttons, Utils.LEADS_API_NAME);

    // 验证结果
    assertEquals(ObjectAction.TRANSFER.getButtonApiName(), dealButton.getName());
    assertEquals(ObjectAction.TRANSFER.getActionCode(), dealButton.getAction());
    assertEquals(ObjectAction.TRANSFER.getActionLabel(), dealButton.getLabel());
  }

  /**
   * 创建测试用的按钮
   */
  private IButton createButton(String name, String label) {
    Button button = new Button();
    button.setName(name);
    button.setLabel(label);
    button.setActionType("default");
    return button;
  }
}
