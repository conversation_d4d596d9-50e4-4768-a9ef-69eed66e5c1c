package com.facishare.paas.appframework.metadata.menu;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataActionService;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.menu.model.MenuItemConfigObject;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MenuCommonServiceImplTest {

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private MetaDataFindService metaDataFindService;

    @Mock
    private MetaDataActionService metaDataActionService;

    @InjectMocks
    private MenuCommonServiceImpl menuCommonService;

    private User testUser;
    private IObjectDescribe mockMenuItemDescribe;
    private IObjectData mockSystemCrmMenu;
    private IObjectData mockMenuItem;
    private QueryResult<IObjectData> mockQueryResult;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setTenantId("74255");
        testUser.setUserId("testUserId");

        mockMenuItemDescribe = mock(IObjectDescribe.class);
        when(mockMenuItemDescribe.getId()).thenReturn("menuItemDescribeId");
        when(mockMenuItemDescribe.getApiName()).thenReturn(MenuConstants.MENU_ITEM_API_NAME);

        mockSystemCrmMenu = new ObjectData();
        mockSystemCrmMenu.setId("systemCrmMenuId");

        mockMenuItem = new ObjectData();
        mockMenuItem.setId("menuItemId");
        mockMenuItem.set(MenuConstants.MenuItemField.NUMBER.getApiName(), 10);

        mockQueryResult = mock(QueryResult.class);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建菜单项的正常场景，当菜单项不存在时应该创建新的菜单项
     */
    @Test
    @DisplayName("正常场景 - 创建菜单项成功")
    void testCreateMenuItem_Success() {
        // 准备测试数据
        String apiName = "TestObj";
        
        when(describeLogicService.findObject(testUser.getTenantId(), MenuConstants.MENU_ITEM_API_NAME))
                .thenReturn(mockMenuItemDescribe);
        when(mockQueryResult.getData()).thenReturn(Lists.newArrayList(mockSystemCrmMenu));
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);
        
        // 模拟菜单项不存在
        QueryResult<IObjectData> emptyResult = mock(QueryResult.class);
        when(emptyResult.getData()).thenReturn(Lists.newArrayList());
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_ITEM_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(emptyResult)
                .thenReturn(mockQueryResult);
        
        when(mockQueryResult.getData()).thenReturn(Lists.newArrayList(mockMenuItem));
        when(metaDataActionService.saveObjectData(eq(testUser), any(IObjectData.class))).thenReturn(mockMenuItem);

        // 执行被测试方法
        IObjectData result = menuCommonService.createMenuItem(testUser, apiName);

        // 验证结果
        assertNotNull(result);
        verify(describeLogicService).findObject(testUser.getTenantId(), MenuConstants.MENU_ITEM_API_NAME);
        verify(metaDataActionService).saveObjectData(eq(testUser), any(IObjectData.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建菜单项时，如果菜单项已存在，应该返回现有菜单项而不创建新的
     */
    @Test
    @DisplayName("正常场景 - 菜单项已存在时返回现有菜单项")
    void testCreateMenuItem_MenuItemExists() {
        // 准备测试数据
        String apiName = "TestObj";
        List<IObjectData> existingMenuItems = Lists.newArrayList(mockMenuItem);
        
        when(describeLogicService.findObject(testUser.getTenantId(), MenuConstants.MENU_ITEM_API_NAME))
                .thenReturn(mockMenuItemDescribe);
        when(mockQueryResult.getData()).thenReturn(Lists.newArrayList(mockSystemCrmMenu));
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);
        
        // 模拟菜单项已存在
        QueryResult<IObjectData> existingResult = mock(QueryResult.class);
        when(existingResult.getData()).thenReturn(existingMenuItems);
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_ITEM_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(existingResult);

        // 执行被测试方法
        IObjectData result = menuCommonService.createMenuItem(testUser, apiName);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockMenuItem, result);
        verify(metaDataActionService, never()).saveObjectData(any(User.class), any(IObjectData.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新带排序的菜单项的正常场景
     */
    @Test
    @DisplayName("正常场景 - 创建或更新带排序的菜单项成功")
    void testCreateOrUpdateMenuItemWithOrder_Success() {
        // 准备测试数据
        String apiName = "TestObj";
        Integer order = 15;
        
        when(describeLogicService.findObject(testUser.getTenantId(), MenuConstants.MENU_ITEM_API_NAME))
                .thenReturn(mockMenuItemDescribe);
        when(mockQueryResult.getData()).thenReturn(Lists.newArrayList(mockSystemCrmMenu));
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);
        
        // 模拟菜单项不存在
        QueryResult<IObjectData> emptyResult = mock(QueryResult.class);
        when(emptyResult.getData()).thenReturn(Lists.newArrayList());
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_ITEM_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(emptyResult);
        
        when(metaDataActionService.saveObjectData(eq(testUser), any(IObjectData.class))).thenReturn(mockMenuItem);

        // 执行被测试方法
        IObjectData result = menuCommonService.createOrUpdateMenuItemWithOrder(testUser, apiName, order);

        // 验证结果
        assertNotNull(result);
        verify(metaDataActionService).saveObjectData(eq(testUser), any(IObjectData.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新带排序的菜单项时，order参数为null应该抛出异常
     */
    @Test
    @DisplayName("异常场景 - order参数为null时抛出ValidateException")
    void testCreateOrUpdateMenuItemWithOrderThrowsValidateException_NullOrder() {
        // 准备测试数据
        String apiName = "TestObj";
        Integer order = null;

        // 执行并验证异常
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            menuCommonService.createOrUpdateMenuItemWithOrder(testUser, apiName, order);
        });

        // 验证异常信息
        assertNotNull(exception);
        verify(describeLogicService, never()).findObject(anyString(), anyString());
        verify(metaDataActionService, never()).saveObjectData(any(User.class), any(IObjectData.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找默认CRM菜单的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找默认CRM菜单成功")
    void testFindDefaultCrmMenu_Success() {
        // 准备测试数据
        when(mockQueryResult.getData()).thenReturn(Lists.newArrayList(mockSystemCrmMenu));
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // 执行被测试方法
        IObjectData result = menuCommonService.findDefaultCrmMenu(testUser);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockSystemCrmMenu, result);
        verify(metaDataFindService).findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找默认CRM菜单时，如果没有找到应该返回null
     */
    @Test
    @DisplayName("正常场景 - 未找到默认CRM菜单时返回null")
    void testFindDefaultCrmMenu_NotFound() {
        // 准备测试数据
        when(mockQueryResult.getData()).thenReturn(Lists.newArrayList());
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // 执行被测试方法
        IObjectData result = menuCommonService.findDefaultCrmMenu(testUser);

        // 验证结果
        assertNull(result);
        verify(metaDataFindService).findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据API名称查找菜单项的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据API名称查找菜单项成功")
    void testFindMenuItemByApiName_Success() {
        // 准备测试数据
        String menuId = "testMenuId";
        List<String> apiNames = Lists.newArrayList("TestObj1", "TestObj2");
        List<IObjectData> expectedMenuItems = Lists.newArrayList(mockMenuItem);
        
        when(mockQueryResult.getData()).thenReturn(expectedMenuItems);
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_ITEM_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // 执行被测试方法
        List<IObjectData> result = menuCommonService.findMenuItemByApiName(testUser, menuId, apiNames);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedMenuItems, result);
        verify(metaDataFindService).findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_ITEM_API_NAME), any(SearchTemplateQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找最后的CRM菜单项的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找最后的CRM菜单项成功")
    void testFindLastCrmMenuItem_Success() {
        // 准备测试数据
        String menuId = "testMenuId";
        
        when(mockQueryResult.getData()).thenReturn(Lists.newArrayList(mockMenuItem));
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_ITEM_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(mockQueryResult);

        // 执行被测试方法
        IObjectData result = menuCommonService.findLastCrmMenuItem(testUser, menuId);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockMenuItem, result);
        verify(metaDataFindService).findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_ITEM_API_NAME), any(SearchTemplateQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试构建菜单项的正常场景
     */
    @Test
    @DisplayName("正常场景 - 构建菜单项成功")
    void testBuildMenuItem_Success() {
        // 准备测试数据
        String menuId = "testMenuId";
        String objectApiName = "TestObj";

        // 执行被测试方法
        IObjectData result = menuCommonService.buildMenuItem(testUser, menuId, mockMenuItemDescribe, objectApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals(testUser.getTenantId(), result.getTenantId());
        assertEquals(mockMenuItemDescribe.getId(), result.getDescribeId());
        assertEquals(mockMenuItemDescribe.getApiName(), result.getDescribeApiName());
        assertEquals(menuId, result.get(MenuConstants.MenuItemField.MENUID.getApiName()));
        assertEquals(objectApiName, result.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName()));
        assertEquals(MenuConstants.MenuItemField.DEFINETYPE_STYSTEM.getApiName(), 
                result.get(MenuConstants.MenuItemField.DEFINETYPE.getApiName()));
        assertEquals(MenuConstants.MenuItemField.TYPE_MENU.getApiName(), 
                result.get(MenuConstants.MenuItemField.TYPE.getApiName()));
        assertEquals(Boolean.FALSE, result.get(MenuConstants.MenuItemField.ISHIDDEN.getApiName()));
    }
}
