package com.facishare.paas.appframework.metadata.menu;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataActionService;
import com.facishare.paas.appframework.metadata.MetaDataFindService;

import com.facishare.paas.appframework.metadata.menu.model.MenuItemConfigObject;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MenuCommonServiceImplTest {

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private MetaDataFindService metaDataFindService;

    @Mock
    private MetaDataActionService metaDataActionService;

    @InjectMocks
    private MenuCommonServiceImpl menuCommonService;

    private User testUser;
    private String testApiName;
    private IObjectDescribe mockMenuItemDescribe;
    private IObjectData mockSystemCrmMenu;
    private IObjectData mockMenuItem;
    private List<IObjectData> testMenuItemList;

    @BeforeEach
    void setUp() {
        testUser = new User("74255", "testUserId");
        testApiName = "TestObj";

        mockMenuItemDescribe = mock(IObjectDescribe.class);
        when(mockMenuItemDescribe.getApiName()).thenReturn(MenuConstants.MENU_ITEM_API_NAME);

        mockSystemCrmMenu = new ObjectData();
        mockSystemCrmMenu.setId("systemCrmMenuId");

        mockMenuItem = new ObjectData();
        mockMenuItem.setId("menuItemId");
        testMenuItemList = Lists.newArrayList(mockMenuItem);
    }

    /**
     * GenerateByAI 测试内容描述：测试创建菜单项的正常场景，当菜单项不存在时创建新的菜单项
     */
    @Test
    @DisplayName("正常场景 - 创建菜单项成功（菜单项不存在）")
    void testCreateMenuItem_Success_MenuItemNotExists() {
        // 准备测试数据
        when(describeLogicService.findObject(testUser.getTenantId(), MenuConstants.MENU_ITEM_API_NAME))
                .thenReturn(mockMenuItemDescribe);
        QueryResult<IObjectData> menuQueryResult = mock(QueryResult.class);
        when(menuQueryResult.getData()).thenReturn(Lists.newArrayList(mockSystemCrmMenu));
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(menuQueryResult);

        QueryResult<IObjectData> menuItemQueryResult = mock(QueryResult.class);
        when(menuItemQueryResult.getData()).thenReturn(Lists.newArrayList()); // 返回空列表，表示菜单项不存在
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_ITEM_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(menuItemQueryResult);

        IObjectData newMenuItem = new ObjectData();
        newMenuItem.setId("newMenuItemId");
        when(metaDataActionService.saveObjectData(eq(testUser), any(IObjectData.class)))
                .thenReturn(newMenuItem);

        // 执行被测试方法
        IObjectData result = menuCommonService.createMenuItem(testUser, testApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals("newMenuItemId", result.getId());
        verify(describeLogicService).findObject(testUser.getTenantId(), MenuConstants.MENU_ITEM_API_NAME);
        verify(metaDataActionService).saveObjectData(eq(testUser), any(IObjectData.class));
    }

    /**
     * GenerateByAI 测试内容描述：测试创建菜单项时，菜单项已存在的场景
     */
    @Test
    @DisplayName("正常场景 - 创建菜单项时菜单项已存在返回现有菜单项")
    void testCreateMenuItem_MenuItemAlreadyExists() {
        // 准备测试数据
        when(describeLogicService.findObject(testUser.getTenantId(), MenuConstants.MENU_ITEM_API_NAME))
                .thenReturn(mockMenuItemDescribe);
        QueryResult<IObjectData> menuQueryResult2 = mock(QueryResult.class);
        when(menuQueryResult2.getData()).thenReturn(Lists.newArrayList(mockSystemCrmMenu));
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(menuQueryResult2);

        QueryResult<IObjectData> menuItemQueryResult2 = mock(QueryResult.class);
        when(menuItemQueryResult2.getData()).thenReturn(testMenuItemList); // 返回已存在的菜单项
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq(MenuConstants.MENU_ITEM_API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(menuItemQueryResult2);

        // 执行被测试方法
        IObjectData result = menuCommonService.createMenuItem(testUser, testApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockMenuItem.getId(), result.getId());
        verify(describeLogicService).findObject(testUser.getTenantId(), MenuConstants.MENU_ITEM_API_NAME);
        verify(metaDataActionService, never()).saveObjectData(any(), any()); // 不应该创建新的菜单项
    }

    /**
     * GenerateByAI 测试内容描述：测试查找默认CRM菜单的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找默认CRM菜单成功")
    void testFindDefaultCrmMenu_Success() {
        try (MockedStatic<SearchTemplateQueryExt> mockedSearchTemplateQueryExt = mockStatic(SearchTemplateQueryExt.class)) {
            // 准备测试数据
            SearchTemplateQuery mockQuery = mock(SearchTemplateQuery.class);
            mockedSearchTemplateQueryExt.when(() -> SearchTemplateQueryExt.builder())
                    .thenReturn(mock(SearchTemplateQueryExt.SearchTemplateQueryExtBuilder.class));

            when(metaDataFindService.findObjectDataByApiName(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class)))
                    .thenReturn(Lists.newArrayList(mockSystemCrmMenu));

            // 执行被测试方法
            IObjectData result = menuCommonService.findDefaultCrmMenu(testUser);

            // 验证结果
            assertNotNull(result);
            assertEquals(mockSystemCrmMenu.getId(), result.getId());
            verify(metaDataFindService).findObjectDataByApiName(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class));
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试查找默认CRM菜单时，菜单不存在的场景
     */
    @Test
    @DisplayName("异常场景 - 查找默认CRM菜单时菜单不存在抛出ValidateException")
    void testFindDefaultCrmMenuThrowsValidateException_MenuNotExists() {
        try (MockedStatic<SearchTemplateQueryExt> mockedSearchTemplateQueryExt = mockStatic(SearchTemplateQueryExt.class)) {
            // 准备测试数据
            mockedSearchTemplateQueryExt.when(() -> SearchTemplateQueryExt.builder())
                    .thenReturn(mock(SearchTemplateQueryExt.SearchTemplateQueryExtBuilder.class));

            when(metaDataFindService.findObjectDataByApiName(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class)))
                    .thenReturn(Lists.newArrayList()); // 返回空列表

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                menuCommonService.findDefaultCrmMenu(testUser);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(metaDataFindService).findObjectDataByApiName(eq(testUser), eq(MenuConstants.MENU_API_NAME), any(SearchTemplateQuery.class));
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试根据API名称查找菜单项的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据API名称查找菜单项成功")
    void testFindMenuItemByApiName_Success() {
        try (MockedStatic<SearchTemplateQueryExt> mockedSearchTemplateQueryExt = mockStatic(SearchTemplateQueryExt.class)) {
            // 准备测试数据
            String menuId = "testMenuId";
            List<String> apiNames = Lists.newArrayList(testApiName);

            mockedSearchTemplateQueryExt.when(() -> SearchTemplateQueryExt.builder())
                    .thenReturn(mock(SearchTemplateQueryExt.SearchTemplateQueryExtBuilder.class));

            when(metaDataFindService.findObjectDataByApiName(eq(testUser), eq(MenuConstants.MENU_ITEM_API_NAME), any(SearchTemplateQuery.class)))
                    .thenReturn(testMenuItemList);

            // 执行被测试方法
            List<IObjectData> result = menuCommonService.findMenuItemByApiName(testUser, menuId, apiNames);

            // 验证结果
            assertNotNull(result);
            assertEquals(testMenuItemList, result);
            verify(metaDataFindService).findObjectDataByApiName(eq(testUser), eq(MenuConstants.MENU_ITEM_API_NAME), any(SearchTemplateQuery.class));
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试根据API名称查找菜单项时，API名称列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 根据API名称查找菜单项时API名称列表为空返回空列表")
    void testFindMenuItemByApiName_EmptyApiNames() {
        // 准备测试数据
        String menuId = "testMenuId";
        List<String> emptyApiNames = Lists.newArrayList();

        // 执行被测试方法
        List<IObjectData> result = menuCommonService.findMenuItemByApiName(testUser, menuId, emptyApiNames);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(metaDataFindService, never()).findObjectDataByApiName(any(), any(), any());
    }

    /**
     * GenerateByAI 测试内容描述：测试查找菜单项配置对象的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找菜单项配置对象成功")
    void testFindMenuItemConfigObject_Success() {
        try (MockedStatic<SearchTemplateQueryExt> mockedSearchTemplateQueryExt = mockStatic(SearchTemplateQueryExt.class)) {
            // 准备测试数据
            String menuItemId = "testMenuItemId";

            mockedSearchTemplateQueryExt.when(() -> SearchTemplateQueryExt.builder())
                    .thenReturn(mock(SearchTemplateQueryExt.SearchTemplateQueryExtBuilder.class));

            IObjectData mockConfigObject = new ObjectData();
            mockConfigObject.setId("configObjectId");
            when(metaDataFindService.findObjectDataByApiName(eq(testUser), eq(MenuConstants.MENU_ITEM_CONFIG_OBJECT_API_NAME), any(SearchTemplateQuery.class)))
                    .thenReturn(Lists.newArrayList(mockConfigObject));

            // 执行被测试方法
            MenuItemConfigObject result = menuCommonService.findMenuItemConfigObject(testUser, menuItemId);

            // 验证结果
            assertNotNull(result);
            verify(metaDataFindService).findObjectDataByApiName(eq(testUser), eq(MenuConstants.MENU_ITEM_CONFIG_OBJECT_API_NAME), any(SearchTemplateQuery.class));
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试查找菜单项配置对象时，配置对象不存在的场景
     */
    @Test
    @DisplayName("正常场景 - 查找菜单项配置对象时配置对象不存在返回null")
    void testFindMenuItemConfigObject_ConfigObjectNotExists() {
        try (MockedStatic<SearchTemplateQueryExt> mockedSearchTemplateQueryExt = mockStatic(SearchTemplateQueryExt.class)) {
            // 准备测试数据
            String menuItemId = "testMenuItemId";

            mockedSearchTemplateQueryExt.when(() -> SearchTemplateQueryExt.builder())
                    .thenReturn(mock(SearchTemplateQueryExt.SearchTemplateQueryExtBuilder.class));

            when(metaDataFindService.findObjectDataByApiName(eq(testUser), eq(MenuConstants.MENU_ITEM_CONFIG_OBJECT_API_NAME), any(SearchTemplateQuery.class)))
                    .thenReturn(Lists.newArrayList()); // 返回空列表

            // 执行被测试方法
            MenuItemConfigObject result = menuCommonService.findMenuItemConfigObject(testUser, menuItemId);

            // 验证结果
            assertNull(result);
            verify(metaDataFindService).findObjectDataByApiName(eq(testUser), eq(MenuConstants.MENU_ITEM_CONFIG_OBJECT_API_NAME), any(SearchTemplateQuery.class));
        }
    }
}
