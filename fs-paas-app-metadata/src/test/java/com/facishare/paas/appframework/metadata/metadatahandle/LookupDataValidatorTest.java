package com.facishare.paas.appframework.metadata.metadatahandle;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ProductCategoryService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LookupDataValidatorTest {

    @Mock
    private User mockUser;

    @Mock
    private IObjectData mockObjectData;

    @Mock
    private IObjectReferenceField mockReferenceField;

    @Mock
    private ProductCategoryService mockProductCategoryService;

    @Mock
    private MetaDataFindService mockMetaDataFindService;

    @Mock
    private DescribeLogicService mockDescribeLogicService;

    @Mock
    private QueryResult<IObjectData> mockQueryResult;

    @Mock
    private IObjectDescribe mockObjectDescribe;

    private LookupDataValidator lookupDataValidator;

    @BeforeEach
    void setUp() {
        when(mockUser.getTenantId()).thenReturn("74255");
        when(mockUser.getUserId()).thenReturn("testUserId");

        when(mockReferenceField.getApiName()).thenReturn("testField");
        when(mockReferenceField.getTargetApiName()).thenReturn("TargetObj");
        when(mockReferenceField.getLabel()).thenReturn("测试字段");
        when(mockReferenceField.getDescribeApiName()).thenReturn("TestObj");
        when(mockReferenceField.getHelpText()).thenReturn("帮助文本");

        when(mockObjectDescribe.getDisplayName()).thenReturn("测试对象");

        lookupDataValidator = LookupDataValidator.builder()
                .user(mockUser)
                .objectData(mockObjectData)
                .referenceField(mockReferenceField)
                .productCategoryService(mockProductCategoryService)
                .metaDataFindService(mockMetaDataFindService)
                .describeLogicService(mockDescribeLogicService)
                .isIgnorePolygonal(false)
                .throwException(false)
                .build();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法时，跳过验证的灰度租户应该返回null
     */
    @Test
    @DisplayName("正常场景 - 跳过验证的灰度租户返回null")
    void testValidate_SkipValidateGrayTenant() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isSkipValidateLookupGrayTenant("74255"))
                    .thenReturn(true);

            // 执行被测试方法
            String result = lookupDataValidator.validate();

            // 验证结果
            assertNull(result);
            verify(mockObjectData, never()).get(anyString());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法时，引用字段的wheres为空应该返回null
     */
    @Test
    @DisplayName("正常场景 - 引用字段wheres为空时返回null")
    void testValidate_EmptyWheres() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isSkipValidateLookupGrayTenant("74255"))
                    .thenReturn(false);

            when(mockReferenceField.getWheres()).thenReturn(Lists.newArrayList());
            when(mockObjectData.get("testField")).thenReturn("testValue");

            // 执行被测试方法
            String result = lookupDataValidator.validate();

            // 验证结果
            assertNull(result);
            verify(mockObjectData).get("testField");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法时，对象数据字段值为null应该返回null
     */
    @Test
    @DisplayName("正常场景 - 对象数据字段值为null时返回null")
    void testValidate_NullFieldValue() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isSkipValidateLookupGrayTenant("74255"))
                    .thenReturn(false);

            Wheres mockWheres = mock(Wheres.class);
            when(mockReferenceField.getWheres()).thenReturn(Lists.newArrayList(mockWheres));
            when(mockObjectData.get("testField")).thenReturn(null);

            // 执行被测试方法
            String result = lookupDataValidator.validate();

            // 验证结果
            assertNull(result);
            verify(mockObjectData).get("testField");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法时，对象数据字段值为空字符串应该返回null
     */
    @Test
    @DisplayName("正常场景 - 对象数据字段值为空字符串时返回null")
    void testValidate_EmptyFieldValue() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class)) {
            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isSkipValidateLookupGrayTenant("74255"))
                    .thenReturn(false);

            Wheres mockWheres = mock(Wheres.class);
            when(mockReferenceField.getWheres()).thenReturn(Lists.newArrayList(mockWheres));
            when(mockObjectData.get("testField")).thenReturn("");

            // 执行被测试方法
            String result = lookupDataValidator.validate();

            // 验证结果
            assertNull(result);
            verify(mockObjectData).get("testField");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法时，查询到数据应该返回null（验证通过）
     */
    @Test
    @DisplayName("正常场景 - 查询到数据时验证通过返回null")
    void testValidate_DataFound() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class);
             MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {

            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isSkipValidateLookupGrayTenant("74255"))
                    .thenReturn(false);
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), eq("74255")))
                    .thenReturn(false);

            // 准备测试数据
            Wheres mockWheres = mock(Wheres.class);
            when(mockReferenceField.getWheres()).thenReturn(Lists.newArrayList(mockWheres));
            when(mockObjectData.get("testField")).thenReturn("testValue");

            IObjectData foundData = new ObjectData();
            foundData.setId("foundDataId");
            List<IObjectData> dataList = Lists.newArrayList(foundData);

            when(mockQueryResult.getData()).thenReturn(dataList);
            when(mockMetaDataFindService.findBySearchQuery(eq(mockUser), eq("TargetObj"), any(SearchTemplateQuery.class)))
                    .thenReturn(mockQueryResult);

            // 执行被测试方法
            String result = lookupDataValidator.validate();

            // 验证结果
            assertNull(result);
            verify(mockMetaDataFindService).findBySearchQuery(eq(mockUser), eq("TargetObj"), any(SearchTemplateQuery.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法时，未查询到数据且不抛异常应该返回字段名
     */
    @Test
    @DisplayName("正常场景 - 未查询到数据且不抛异常时返回字段名")
    void testValidate_DataNotFoundNoException() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class);
             MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {

            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isSkipValidateLookupGrayTenant("74255"))
                    .thenReturn(false);
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), eq("74255")))
                    .thenReturn(false);

            // 准备测试数据
            Wheres mockWheres = mock(Wheres.class);
            when(mockReferenceField.getWheres()).thenReturn(Lists.newArrayList(mockWheres));
            when(mockObjectData.get("testField")).thenReturn("testValue");

            List<IObjectData> emptyDataList = Lists.newArrayList();
            when(mockQueryResult.getData()).thenReturn(emptyDataList);
            when(mockMetaDataFindService.findBySearchQuery(eq(mockUser), eq("TargetObj"), any(SearchTemplateQuery.class)))
                    .thenReturn(mockQueryResult);

            // 执行被测试方法
            String result = lookupDataValidator.validate();

            // 验证结果
            assertEquals("testField", result);
            verify(mockMetaDataFindService).findBySearchQuery(eq(mockUser), eq("TargetObj"), any(SearchTemplateQuery.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法时，未查询到数据且抛异常应该抛出ValidateException
     */
    @Test
    @DisplayName("异常场景 - 未查询到数据且抛异常时抛出ValidateException")
    void testValidateThrowsValidateException_DataNotFoundWithException() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class);
             MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {

            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isSkipValidateLookupGrayTenant("74255"))
                    .thenReturn(false);
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), eq("74255")))
                    .thenReturn(false);

            // 准备测试数据 - 设置抛异常
            lookupDataValidator = LookupDataValidator.builder()
                    .user(mockUser)
                    .objectData(mockObjectData)
                    .referenceField(mockReferenceField)
                    .productCategoryService(mockProductCategoryService)
                    .metaDataFindService(mockMetaDataFindService)
                    .describeLogicService(mockDescribeLogicService)
                    .isIgnorePolygonal(false)
                    .throwException(true)
                    .build();

            Wheres mockWheres = mock(Wheres.class);
            when(mockReferenceField.getWheres()).thenReturn(Lists.newArrayList(mockWheres));
            when(mockObjectData.get("testField")).thenReturn("testValue");

            List<IObjectData> emptyDataList = Lists.newArrayList();
            when(mockQueryResult.getData()).thenReturn(emptyDataList);
            when(mockMetaDataFindService.findBySearchQuery(eq(mockUser), eq("TargetObj"), any(SearchTemplateQuery.class)))
                    .thenReturn(mockQueryResult);
            when(mockDescribeLogicService.findObjectWithoutCopyIfGray("74255", "TestObj"))
                    .thenReturn(mockObjectDescribe);

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                lookupDataValidator.validate();
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(mockMetaDataFindService).findBySearchQuery(eq(mockUser), eq("TargetObj"), any(SearchTemplateQuery.class));
            verify(mockDescribeLogicService).findObjectWithoutCopyIfGray("74255", "TestObj");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证方法时，忽略多角关系的场景
     */
    @Test
    @DisplayName("正常场景 - 忽略多角关系时正确处理")
    void testValidate_IgnorePolygonal() {
        try (MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class);
             MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {

            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isSkipValidateLookupGrayTenant("74255"))
                    .thenReturn(false);
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), eq("74255")))
                    .thenReturn(false);

            // 准备测试数据 - 设置忽略多角关系
            lookupDataValidator = LookupDataValidator.builder()
                    .user(mockUser)
                    .objectData(mockObjectData)
                    .referenceField(mockReferenceField)
                    .productCategoryService(mockProductCategoryService)
                    .metaDataFindService(mockMetaDataFindService)
                    .describeLogicService(mockDescribeLogicService)
                    .isIgnorePolygonal(true)
                    .throwException(false)
                    .build();

            Wheres mockWheres = mock(Wheres.class);
            when(mockReferenceField.getWheres()).thenReturn(Lists.newArrayList(mockWheres));
            when(mockObjectData.get("testField")).thenReturn("testValue");

            IObjectData foundData = new ObjectData();
            foundData.setId("foundDataId");
            List<IObjectData> dataList = Lists.newArrayList(foundData);

            when(mockQueryResult.getData()).thenReturn(dataList);
            when(mockMetaDataFindService.findBySearchQuery(eq(mockUser), eq("TargetObj"), any(SearchTemplateQuery.class)))
                    .thenReturn(mockQueryResult);

            // 执行被测试方法
            String result = lookupDataValidator.validate();

            // 验证结果
            assertNull(result);
            verify(mockMetaDataFindService).findBySearchQuery(eq(mockUser), eq("TargetObj"), any(SearchTemplateQuery.class));
        }
    }
}
