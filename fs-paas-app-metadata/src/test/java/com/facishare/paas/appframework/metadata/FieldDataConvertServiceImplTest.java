package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dataconvert.DataConvertContext;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverter;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverterManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.multiRegion.MultiRegionContextHolder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FieldDataConvertServiceImplTest {

    @Mock
    private FieldDataConverterManager fieldDataConverterManager;

    @InjectMocks
    private FieldDataConvertServiceImpl fieldDataConvertService;

    private User testUser;
    private IObjectData testObjectData;
    private IFieldDescribe mockFieldDescribe;
    private FieldDataConverter mockFieldDataConverter;
    private String testFieldRenderType;
    private String testConvertedData;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setTenantId("74255");
        testUser.setUserId("testUserId");

        testObjectData = new ObjectData();
        testObjectData.setId("testDataId");

        mockFieldDescribe = mock(IFieldDescribe.class);
        when(mockFieldDescribe.getApiName()).thenReturn("testField");
        when(mockFieldDescribe.getType()).thenReturn("TEXT");

        mockFieldDataConverter = mock(FieldDataConverter.class);
        testFieldRenderType = "TEXT";
        testConvertedData = "转换后的数据";

        when(fieldDataConverterManager.getFieldDataConverter(testFieldRenderType))
                .thenReturn(mockFieldDataConverter);
        when(mockFieldDataConverter.convertFieldData(eq(testObjectData), eq(mockFieldDescribe), any(DataConvertContext.class)))
                .thenReturn(testConvertedData);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字段数据转换的正常场景，验证基本的数据转换功能
     */
    @Test
    @DisplayName("正常场景 - 字段数据转换成功")
    void testConvertData_Success() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<DataConvertContext> mockedDataConvertContext = mockStatic(DataConvertContext.class)) {

            // 准备测试数据
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.getFieldRenderType(mockFieldDescribe))
                    .thenReturn(testFieldRenderType);

            DataConvertContext mockDataConvertContext = mock(DataConvertContext.class);
            mockedDataConvertContext.when(() -> DataConvertContext.of(testUser))
                    .thenReturn(mockDataConvertContext);

            // 执行被测试方法
            String result = fieldDataConvertService.convertData(testObjectData, mockFieldDescribe, testUser);

            // 验证结果
            assertNotNull(result);
            assertEquals(testConvertedData, result);
            verify(fieldDataConverterManager).getFieldDataConverter(testFieldRenderType);
            verify(mockFieldDataConverter).convertFieldData(testObjectData, mockFieldDescribe, mockDataConvertContext);
            mockedObjectDescribeExt.verify(() -> ObjectDescribeExt.getFieldRenderType(mockFieldDescribe));
            mockedDataConvertContext.verify(() -> DataConvertContext.of(testUser));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带多区域参数的字段数据转换的正常场景
     */
    @Test
    @DisplayName("正常场景 - 带多区域参数的字段数据转换成功")
    void testConvertDataWithMultiRegion_Success() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<DataConvertContext> mockedDataConvertContext = mockStatic(DataConvertContext.class);
             MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class);
             MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class);
             MockedStatic<MultiRegionContextHolder> mockedMultiRegionContextHolder = mockStatic(MultiRegionContextHolder.class)) {

            // 准备测试数据
            boolean isUseMultiRegion = true;
            String testRegion = "testRegion";
            String testFieldType = "TEXT";

            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.getFieldRenderType(mockFieldDescribe))
                    .thenReturn(testFieldRenderType);

            DataConvertContext mockDataConvertContext = mock(DataConvertContext.class);
            mockedDataConvertContext.when(() -> DataConvertContext.of(testUser))
                    .thenReturn(mockDataConvertContext);

            FieldDescribeExt mockFieldDescribeExt = mock(FieldDescribeExt.class);
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockFieldDescribe))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.getTypeOrReturnType()).thenReturn(testFieldType);

            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isUseMultiRegionByFieldType(testFieldType))
                    .thenReturn(true);

            mockedMultiRegionContextHolder.when(MultiRegionContextHolder::getUserRegion)
                    .thenReturn(testRegion);

            // 执行被测试方法
            String result = fieldDataConvertService.convertData(testObjectData, mockFieldDescribe, testUser, isUseMultiRegion);

            // 验证结果
            assertNotNull(result);
            assertEquals(testConvertedData, result);
            verify(fieldDataConverterManager).getFieldDataConverter(testFieldRenderType);
            verify(mockFieldDataConverter).convertFieldData(testObjectData, mockFieldDescribe, mockDataConvertContext);
            verify(mockDataConvertContext).setRegion(testRegion);
            mockedAppFrameworkConfig.verify(() -> AppFrameworkConfig.isUseMultiRegionByFieldType(testFieldType));
            mockedMultiRegionContextHolder.verify(MultiRegionContextHolder::getUserRegion);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带多区域参数但不使用多区域的字段数据转换场景
     */
    @Test
    @DisplayName("正常场景 - 带多区域参数但不使用多区域的字段数据转换成功")
    void testConvertDataWithMultiRegionFalse_Success() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<DataConvertContext> mockedDataConvertContext = mockStatic(DataConvertContext.class);
             MockedStatic<AppFrameworkConfig> mockedAppFrameworkConfig = mockStatic(AppFrameworkConfig.class);
             MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {

            // 准备测试数据
            boolean isUseMultiRegion = true;
            String testFieldType = "TEXT";

            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.getFieldRenderType(mockFieldDescribe))
                    .thenReturn(testFieldRenderType);

            DataConvertContext mockDataConvertContext = mock(DataConvertContext.class);
            mockedDataConvertContext.when(() -> DataConvertContext.of(testUser))
                    .thenReturn(mockDataConvertContext);

            FieldDescribeExt mockFieldDescribeExt = mock(FieldDescribeExt.class);
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.of(mockFieldDescribe))
                    .thenReturn(mockFieldDescribeExt);
            when(mockFieldDescribeExt.getTypeOrReturnType()).thenReturn(testFieldType);

            mockedAppFrameworkConfig.when(() -> AppFrameworkConfig.isUseMultiRegionByFieldType(testFieldType))
                    .thenReturn(false);

            // 执行被测试方法
            String result = fieldDataConvertService.convertData(testObjectData, mockFieldDescribe, testUser, isUseMultiRegion);

            // 验证结果
            assertNotNull(result);
            assertEquals(testConvertedData, result);
            verify(fieldDataConverterManager).getFieldDataConverter(testFieldRenderType);
            verify(mockFieldDataConverter).convertFieldData(testObjectData, mockFieldDescribe, mockDataConvertContext);
            verify(mockDataConvertContext, never()).setRegion(anyString());
            mockedAppFrameworkConfig.verify(() -> AppFrameworkConfig.isUseMultiRegionByFieldType(testFieldType));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试不使用多区域参数的字段数据转换场景
     */
    @Test
    @DisplayName("正常场景 - 不使用多区域参数的字段数据转换成功")
    void testConvertDataWithoutMultiRegion_Success() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<DataConvertContext> mockedDataConvertContext = mockStatic(DataConvertContext.class)) {

            // 准备测试数据
            boolean isUseMultiRegion = false;

            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.getFieldRenderType(mockFieldDescribe))
                    .thenReturn(testFieldRenderType);

            DataConvertContext mockDataConvertContext = mock(DataConvertContext.class);
            mockedDataConvertContext.when(() -> DataConvertContext.of(testUser))
                    .thenReturn(mockDataConvertContext);

            // 执行被测试方法
            String result = fieldDataConvertService.convertData(testObjectData, mockFieldDescribe, testUser, isUseMultiRegion);

            // 验证结果
            assertNotNull(result);
            assertEquals(testConvertedData, result);
            verify(fieldDataConverterManager).getFieldDataConverter(testFieldRenderType);
            verify(mockFieldDataConverter).convertFieldData(testObjectData, mockFieldDescribe, mockDataConvertContext);
            verify(mockDataConvertContext, never()).setRegion(anyString());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字段数据转换时，转换器返回null的场景
     */
    @Test
    @DisplayName("正常场景 - 字段数据转换时转换器返回null")
    void testConvertData_ConverterReturnsNull() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<DataConvertContext> mockedDataConvertContext = mockStatic(DataConvertContext.class)) {

            // 准备测试数据
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.getFieldRenderType(mockFieldDescribe))
                    .thenReturn(testFieldRenderType);

            DataConvertContext mockDataConvertContext = mock(DataConvertContext.class);
            mockedDataConvertContext.when(() -> DataConvertContext.of(testUser))
                    .thenReturn(mockDataConvertContext);

            when(mockFieldDataConverter.convertFieldData(eq(testObjectData), eq(mockFieldDescribe), any(DataConvertContext.class)))
                    .thenReturn(null);

            // 执行被测试方法
            String result = fieldDataConvertService.convertData(testObjectData, mockFieldDescribe, testUser);

            // 验证结果
            assertNull(result);
            verify(fieldDataConverterManager).getFieldDataConverter(testFieldRenderType);
            verify(mockFieldDataConverter).convertFieldData(testObjectData, mockFieldDescribe, mockDataConvertContext);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字段数据转换时，转换器返回空字符串的场景
     */
    @Test
    @DisplayName("正常场景 - 字段数据转换时转换器返回空字符串")
    void testConvertData_ConverterReturnsEmptyString() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<DataConvertContext> mockedDataConvertContext = mockStatic(DataConvertContext.class)) {

            // 准备测试数据
            String emptyResult = "";

            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.getFieldRenderType(mockFieldDescribe))
                    .thenReturn(testFieldRenderType);

            DataConvertContext mockDataConvertContext = mock(DataConvertContext.class);
            mockedDataConvertContext.when(() -> DataConvertContext.of(testUser))
                    .thenReturn(mockDataConvertContext);

            when(mockFieldDataConverter.convertFieldData(eq(testObjectData), eq(mockFieldDescribe), any(DataConvertContext.class)))
                    .thenReturn(emptyResult);

            // 执行被测试方法
            String result = fieldDataConvertService.convertData(testObjectData, mockFieldDescribe, testUser);

            // 验证结果
            assertNotNull(result);
            assertEquals(emptyResult, result);
            assertTrue(result.isEmpty());
            verify(fieldDataConverterManager).getFieldDataConverter(testFieldRenderType);
            verify(mockFieldDataConverter).convertFieldData(testObjectData, mockFieldDescribe, mockDataConvertContext);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试重载方法调用的正常场景，验证方法委托
     */
    @Test
    @DisplayName("正常场景 - 重载方法调用成功")
    void testConvertDataOverload_Success() {
        try (MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class);
             MockedStatic<DataConvertContext> mockedDataConvertContext = mockStatic(DataConvertContext.class)) {

            // 准备测试数据
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.getFieldRenderType(mockFieldDescribe))
                    .thenReturn(testFieldRenderType);

            DataConvertContext mockDataConvertContext = mock(DataConvertContext.class);
            mockedDataConvertContext.when(() -> DataConvertContext.of(testUser))
                    .thenReturn(mockDataConvertContext);

            // 执行被测试方法（调用重载方法）
            String result = fieldDataConvertService.convertData(testObjectData, mockFieldDescribe, testUser);

            // 验证结果
            assertNotNull(result);
            assertEquals(testConvertedData, result);
            verify(fieldDataConverterManager).getFieldDataConverter(testFieldRenderType);
            verify(mockFieldDataConverter).convertFieldData(testObjectData, mockFieldDescribe, mockDataConvertContext);
        }
    }
}
