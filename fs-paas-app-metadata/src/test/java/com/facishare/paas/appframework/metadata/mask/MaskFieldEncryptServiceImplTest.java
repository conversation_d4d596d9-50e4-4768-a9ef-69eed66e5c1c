package com.facishare.paas.appframework.metadata.mask;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * MaskFieldEncryptServiceImpl的单元测试
 * 测试字段加密和解密功能
 */
@ExtendWith(MockitoExtension.class)
class MaskFieldEncryptServiceImplTest {

    @Mock
    private IFieldDescribe mockFieldDescribe;

    @Mock
    private IObjectDescribe mockObjectDescribe;

    @InjectMocks
    private MaskFieldEncryptServiceImpl maskFieldEncryptService;

    @BeforeEach
    void setUp() {
        // 配置基本的Mock行为
        when(mockFieldDescribe.getApiName()).thenReturn("testField");
        when(mockFieldDescribe.getType()).thenReturn("TEXT");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字符串编码的正常场景
     */
    @Test
    @DisplayName("正常场景 - 字符串编码成功")
    void testEncode_Success() {
        // 准备测试数据
        String originalValue = "testValue";

        // 执行测试
        String result = maskFieldEncryptService.encode(originalValue);

        // 验证结果
        assertNotNull(result);
        assertNotEquals(originalValue, result);
        assertTrue(result.length() > 0);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字符串编码时输入为空的场景
     */
    @Test
    @DisplayName("边界场景 - 字符串编码时输入为空")
    void testEncode_EmptyString() {
        // 准备测试数据
        String originalValue = "";

        // 执行测试
        String result = maskFieldEncryptService.encode(originalValue);

        // 验证结果
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字符串编码时输入为null的场景
     */
    @Test
    @DisplayName("边界场景 - 字符串编码时输入为null")
    void testEncode_NullString() {
        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            maskFieldEncryptService.encode(null);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字符串解码的正常场景
     */
    @Test
    @DisplayName("正常场景 - 字符串解码成功")
    void testDecode_Success() {
        // 准备测试数据 - 先编码再解码
        String originalValue = "testValue";
        String encodedValue = maskFieldEncryptService.encode(originalValue);

        // 执行测试
        String result = maskFieldEncryptService.decode(encodedValue);

        // 验证结果
        assertEquals(originalValue, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字符串解码时输入无效编码的场景
     */
    @Test
    @DisplayName("异常场景 - 字符串解码时输入无效编码")
    void testDecode_InvalidEncoding() {
        // 准备测试数据
        String invalidEncodedValue = "invalidEncodedValue";

        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            maskFieldEncryptService.decode(invalidEncodedValue);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据编码的正常场景
     */
    @Test
    @DisplayName("正常场景 - 对象数据编码成功")
    void testEncodeObjectData_Success() {
        // 准备测试数据
        IObjectData objectData = new ObjectData();
        objectData.set("testField", "testValue");
        
        List<IFieldDescribe> fieldDescribes = new ArrayList<>();
        fieldDescribes.add(mockFieldDescribe);

        // 执行测试
        assertDoesNotThrow(() -> {
            maskFieldEncryptService.encode(objectData, fieldDescribes);
        });

        // 验证结果 - 应该添加了加密字段
        assertTrue(objectData.containsField("testField__encrypt"));
        assertNotNull(objectData.get("testField__encrypt"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据编码时字段列表为空的场景
     */
    @Test
    @DisplayName("边界场景 - 对象数据编码时字段列表为空")
    void testEncodeObjectData_EmptyFields() {
        // 准备测试数据
        IObjectData objectData = new ObjectData();
        objectData.set("testField", "testValue");
        
        List<IFieldDescribe> emptyFieldDescribes = Collections.emptyList();

        // 执行测试
        assertDoesNotThrow(() -> {
            maskFieldEncryptService.encode(objectData, emptyFieldDescribes);
        });

        // 验证结果 - 不应该添加加密字段
        assertFalse(objectData.containsField("testField__encrypt"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据编码时字段列表为null的场景
     */
    @Test
    @DisplayName("边界场景 - 对象数据编码时字段列表为null")
    void testEncodeObjectData_NullFields() {
        // 准备测试数据
        IObjectData objectData = new ObjectData();
        objectData.set("testField", "testValue");

        // 执行测试
        assertDoesNotThrow(() -> {
            maskFieldEncryptService.encode(objectData, null);
        });

        // 验证结果 - 不应该添加加密字段
        assertFalse(objectData.containsField("testField__encrypt"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据编码时字段值为空的场景
     */
    @Test
    @DisplayName("边界场景 - 对象数据编码时字段值为空")
    void testEncodeObjectData_EmptyValue() {
        // 准备测试数据
        IObjectData objectData = new ObjectData();
        objectData.set("testField", "");
        
        List<IFieldDescribe> fieldDescribes = new ArrayList<>();
        fieldDescribes.add(mockFieldDescribe);

        // 执行测试
        assertDoesNotThrow(() -> {
            maskFieldEncryptService.encode(objectData, fieldDescribes);
        });

        // 验证结果 - 应该设置空的加密字段
        assertTrue(objectData.containsField("testField__encrypt"));
        assertEquals("", objectData.get("testField__encrypt"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据编码时字段值为null的场景
     */
    @Test
    @DisplayName("边界场景 - 对象数据编码时字段值为null")
    void testEncodeObjectData_NullValue() {
        // 准备测试数据
        IObjectData objectData = new ObjectData();
        objectData.set("testField", null);
        
        List<IFieldDescribe> fieldDescribes = new ArrayList<>();
        fieldDescribes.add(mockFieldDescribe);

        // 执行测试
        assertDoesNotThrow(() -> {
            maskFieldEncryptService.encode(objectData, fieldDescribes);
        });

        // 验证结果 - 应该设置空的加密字段
        assertTrue(objectData.containsField("testField__encrypt"));
        assertEquals("", objectData.get("testField__encrypt"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据编码时对象不包含指定字段的场景
     */
    @Test
    @DisplayName("边界场景 - 对象数据编码时对象不包含指定字段")
    void testEncodeObjectData_FieldNotExists() {
        // 准备测试数据
        IObjectData objectData = new ObjectData();
        // 注意：不设置testField
        
        List<IFieldDescribe> fieldDescribes = new ArrayList<>();
        fieldDescribes.add(mockFieldDescribe);

        // 执行测试
        assertDoesNotThrow(() -> {
            maskFieldEncryptService.encode(objectData, fieldDescribes);
        });

        // 验证结果 - 不应该添加加密字段
        assertFalse(objectData.containsField("testField__encrypt"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据解码的正常场景
     */
    @Test
    @DisplayName("正常场景 - 对象数据解码成功")
    void testDecodeObjectData_Success() {
        // 准备测试数据 - 先编码
        IObjectData objectData = new ObjectData();
        objectData.set("testField", "testValue");
        
        List<IFieldDescribe> fieldDescribes = new ArrayList<>();
        fieldDescribes.add(mockFieldDescribe);
        
        // 先编码
        maskFieldEncryptService.encode(objectData, fieldDescribes);
        String encryptedValue = (String) objectData.get("testField__encrypt");
        
        // 清除原始字段，只保留加密字段
        objectData.remove("testField");

        // 执行解码测试
        assertDoesNotThrow(() -> {
            maskFieldEncryptService.decode(objectData, fieldDescribes);
        });

        // 验证结果
        assertTrue(objectData.containsField("testField"));
        assertEquals("testValue", objectData.get("testField"));
        assertFalse(objectData.containsField("testField__encrypt"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据解码时字段列表为空的场景
     */
    @Test
    @DisplayName("边界场景 - 对象数据解码时字段列表为空")
    void testDecodeObjectData_EmptyFields() {
        // 准备测试数据
        IObjectData objectData = new ObjectData();
        objectData.set("testField__encrypt", "someEncryptedValue");
        
        List<IFieldDescribe> emptyFieldDescribes = Collections.emptyList();

        // 执行测试
        assertDoesNotThrow(() -> {
            maskFieldEncryptService.decode(objectData, emptyFieldDescribes);
        });

        // 验证结果 - 加密字段应该保持不变
        assertTrue(objectData.containsField("testField__encrypt"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据解码时加密字段值为空的场景
     */
    @Test
    @DisplayName("边界场景 - 对象数据解码时加密字段值为空")
    void testDecodeObjectData_EmptyEncryptedValue() {
        // 准备测试数据
        IObjectData objectData = new ObjectData();
        objectData.set("testField__encrypt", "");
        
        List<IFieldDescribe> fieldDescribes = new ArrayList<>();
        fieldDescribes.add(mockFieldDescribe);

        // 执行测试
        assertDoesNotThrow(() -> {
            maskFieldEncryptService.decode(objectData, fieldDescribes);
        });

        // 验证结果
        assertTrue(objectData.containsField("testField"));
        assertNull(objectData.get("testField"));
        assertFalse(objectData.containsField("testField__encrypt"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试编码解码的往返一致性
     */
    @Test
    @DisplayName("一致性验证 - 编码解码往返一致性")
    void testEncodeDecodeRoundTrip() {
        // 准备测试数据
        String originalValue = "roundTripTestValue";
        IObjectData objectData = new ObjectData();
        objectData.set("testField", originalValue);
        
        List<IFieldDescribe> fieldDescribes = new ArrayList<>();
        fieldDescribes.add(mockFieldDescribe);

        // 执行编码
        maskFieldEncryptService.encode(objectData, fieldDescribes);
        
        // 清除原始字段
        objectData.remove("testField");
        
        // 执行解码
        maskFieldEncryptService.decode(objectData, fieldDescribes);

        // 验证往返一致性
        assertEquals(originalValue, objectData.get("testField"));
        assertFalse(objectData.containsField("testField__encrypt"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多个字段的编码解码
     */
    @Test
    @DisplayName("复杂场景 - 多个字段的编码解码")
    void testMultipleFields() {
        // 准备测试数据
        IObjectData objectData = new ObjectData();
        objectData.set("testField", "value1");
        objectData.set("anotherField", "value2");
        
        IFieldDescribe mockFieldDescribe2 = mock(IFieldDescribe.class);
        when(mockFieldDescribe2.getApiName()).thenReturn("anotherField");
        when(mockFieldDescribe2.getType()).thenReturn("TEXT");
        
        List<IFieldDescribe> fieldDescribes = new ArrayList<>();
        fieldDescribes.add(mockFieldDescribe);
        fieldDescribes.add(mockFieldDescribe2);

        // 执行编码
        maskFieldEncryptService.encode(objectData, fieldDescribes);

        // 验证编码结果
        assertTrue(objectData.containsField("testField__encrypt"));
        assertTrue(objectData.containsField("anotherField__encrypt"));
        
        // 清除原始字段
        objectData.remove("testField");
        objectData.remove("anotherField");
        
        // 执行解码
        maskFieldEncryptService.decode(objectData, fieldDescribes);

        // 验证解码结果
        assertEquals("value1", objectData.get("testField"));
        assertEquals("value2", objectData.get("anotherField"));
        assertFalse(objectData.containsField("testField__encrypt"));
        assertFalse(objectData.containsField("anotherField__encrypt"));
    }
}
