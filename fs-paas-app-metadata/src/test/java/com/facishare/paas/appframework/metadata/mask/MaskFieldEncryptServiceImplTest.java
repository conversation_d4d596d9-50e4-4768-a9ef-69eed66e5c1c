package com.facishare.paas.appframework.metadata.mask;

import com.facishare.paas.appframework.common.util.EncryptUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MaskFieldEncryptServiceImplTest {

    private MaskFieldEncryptServiceImpl maskFieldEncryptService;
    private IObjectData testObjectData;
    private IFieldDescribe mockFieldDescribe;
    private IObjectDescribe mockObjectDescribe;
    private Collection<IFieldDescribe> testFieldDescribes;

    @BeforeEach
    void setUp() {
        maskFieldEncryptService = new MaskFieldEncryptServiceImpl();

        testObjectData = new ObjectData();
        testObjectData.setId("testDataId");
        testObjectData.set("testField", "testValue");

        mockFieldDescribe = mock(IFieldDescribe.class);
        when(mockFieldDescribe.getApiName()).thenReturn("testField");
        when(mockFieldDescribe.getType()).thenReturn(IFieldType.TEXT);

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn("TestObj");

        testFieldDescribes = Lists.newArrayList(mockFieldDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字符串编码的正常场景，验证能正确调用EncryptUtil.encode
     */
    @Test
    @DisplayName("正常场景 - 字符串编码成功")
    void testEncodeString_Success() {
        try (MockedStatic<EncryptUtil> mockedEncryptUtil = mockStatic(EncryptUtil.class)) {
            // 准备测试数据
            String testValue = "testValue";
            String expectedEncodedValue = "encodedValue";

            mockedEncryptUtil.when(() -> EncryptUtil.encode(testValue))
                    .thenReturn(expectedEncodedValue);

            // 执行被测试方法
            String result = maskFieldEncryptService.encode(testValue);

            // 验证结果
            assertEquals(expectedEncodedValue, result);
            mockedEncryptUtil.verify(() -> EncryptUtil.encode(testValue));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据编码时，字段描述集合为空的场景
     */
    @Test
    @DisplayName("正常场景 - 字段描述集合为空时直接返回")
    void testEncodeObjectData_EmptyFieldDescribes() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class)) {
            // 准备测试数据
            Collection<IFieldDescribe> emptyFieldDescribes = Lists.newArrayList();

            // 执行被测试方法
            maskFieldEncryptService.encode(testObjectData, emptyFieldDescribes);

            // 验证结果 - 由于字段描述为空，不应该调用任何静态方法
            mockedFieldDescribeExt.verifyNoInteractions();
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据编码时，对象数据不包含指定字段的场景
     */
    @Test
    @DisplayName("正常场景 - 对象数据不包含指定字段时跳过处理")
    void testEncodeObjectData_FieldNotContained() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class);
             MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {

            // 准备测试数据 - 创建不包含testField的对象数据
            IObjectData emptyObjectData = new ObjectData();
            emptyObjectData.setId("emptyDataId");

            // 执行被测试方法
            maskFieldEncryptService.encode(emptyObjectData, testFieldDescribes);

            // 验证结果 - 由于对象数据不包含字段，不应该调用编码相关方法
            mockedFieldDescribeExt.verify(() -> FieldDescribeExt.getMaskEncryptFieldName(anyString()), never());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据编码时，字段值为空的场景
     */
    @Test
    @DisplayName("正常场景 - 字段值为空时设置空字符串")
    void testEncodeObjectData_EmptyFieldValue() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class);
             MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {

            // 准备测试数据
            IObjectData objectDataWithNullValue = new ObjectData();
            objectDataWithNullValue.setId("testDataId");
            objectDataWithNullValue.set("testField", null);

            mockedObjectDataExt.when(() -> ObjectDataExt.isValueEmpty(null))
                    .thenReturn(true);
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.getMaskEncryptFieldName("testField"))
                    .thenReturn("testField__s");

            // 执行被测试方法
            maskFieldEncryptService.encode(objectDataWithNullValue, testFieldDescribes);

            // 验证结果
            assertEquals("", objectDataWithNullValue.get("testField__s"));
            mockedFieldDescribeExt.verify(() -> FieldDescribeExt.getMaskEncryptFieldName("testField"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据编码时，字段值不为空的正常场景
     */
    @Test
    @DisplayName("正常场景 - 字段值不为空时正确编码")
    void testEncodeObjectData_NonEmptyFieldValue() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class);
             MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class);
             MockedStatic<EncryptUtil> mockedEncryptUtil = mockStatic(EncryptUtil.class)) {

            // 准备测试数据
            String testValue = "testValue";
            String expectedEncodedValue = "encodedValue";

            mockedObjectDataExt.when(() -> ObjectDataExt.isValueEmpty(testValue))
                    .thenReturn(false);
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.getMaskEncryptFieldName("testField"))
                    .thenReturn("testField__s");
            mockedEncryptUtil.when(() -> EncryptUtil.encode(anyString()))
                    .thenReturn(expectedEncodedValue);

            // 执行被测试方法
            maskFieldEncryptService.encode(testObjectData, testFieldDescribes);

            // 验证结果
            assertEquals(expectedEncodedValue, testObjectData.get("testField__s"));
            mockedFieldDescribeExt.verify(() -> FieldDescribeExt.getMaskEncryptFieldName("testField"));
            mockedEncryptUtil.verify(() -> EncryptUtil.encode(anyString()));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字符串解码的正常场景，验证能正确调用EncryptUtil.decode
     */
    @Test
    @DisplayName("正常场景 - 字符串解码成功")
    void testDecodeString_Success() {
        try (MockedStatic<EncryptUtil> mockedEncryptUtil = mockStatic(EncryptUtil.class)) {
            // 准备测试数据
            String encodedValue = "encodedValue";
            String expectedDecodedValue = "decodedValue";

            mockedEncryptUtil.when(() -> EncryptUtil.decode(encodedValue))
                    .thenReturn(expectedDecodedValue);

            // 执行被测试方法
            String result = maskFieldEncryptService.decode(encodedValue);

            // 验证结果
            assertEquals(expectedDecodedValue, result);
            mockedEncryptUtil.verify(() -> EncryptUtil.decode(encodedValue));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据解码时，字段描述集合为空的场景
     */
    @Test
    @DisplayName("正常场景 - 字段描述集合为空时直接返回")
    void testDecodeObjectData_EmptyFieldDescribes() {
        try (MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {
            // 准备测试数据
            Collection<IFieldDescribe> emptyFieldDescribes = Lists.newArrayList();

            // 执行被测试方法
            maskFieldEncryptService.decode(testObjectData, emptyFieldDescribes);

            // 验证结果 - 由于字段描述为空，不应该调用ObjectDataExt.of
            mockedObjectDataExt.verify(() -> ObjectDataExt.of(any(IObjectData.class)), never());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象数据解码时，加密字段值为空的场景
     */
    @Test
    @DisplayName("正常场景 - 加密字段值为空时正确处理")
    void testDecodeObjectData_EmptyEncryptedValue() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class);
             MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class)) {

            // 准备测试数据
            IObjectData objectDataWithEncryptedField = new ObjectData();
            objectDataWithEncryptedField.setId("testDataId");
            objectDataWithEncryptedField.set("testField__s", "");

            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            mockedObjectDataExt.when(() -> ObjectDataExt.of(objectDataWithEncryptedField))
                    .thenReturn(mockObjectDataExt);
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.getMaskEncryptFieldName("testField"))
                    .thenReturn("testField__s");
            mockedObjectDataExt.when(() -> ObjectDataExt.isValueEmpty(""))
                    .thenReturn(true);

            // 执行被测试方法
            maskFieldEncryptService.decode(objectDataWithEncryptedField, testFieldDescribes);

            // 验证结果
            verify(mockObjectDataExt).remove("testField__s");
            mockedFieldDescribeExt.verify(() -> FieldDescribeExt.getMaskEncryptFieldName("testField"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据对象描述解码的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据对象描述解码成功")
    void testDecodeByObjectDescribe_Success() {
        try (MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class);
             MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class);
             MockedStatic<ObjectDescribeExt> mockedObjectDescribeExt = mockStatic(ObjectDescribeExt.class)) {

            // 准备测试数据
            IObjectData objectDataWithEncryptedFields = new ObjectData();
            objectDataWithEncryptedFields.setId("testDataId");
            objectDataWithEncryptedFields.set("testField__s", "encryptedValue");

            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("testField__s", "encryptedValue");

            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            when(mockObjectDataExt.toMap()).thenReturn(dataMap);
            mockedObjectDataExt.when(() -> ObjectDataExt.of(objectDataWithEncryptedFields))
                    .thenReturn(mockObjectDataExt);

            mockedFieldDescribeExt.when(() -> FieldDescribeExt.isMaskEncryptField("testField__s"))
                    .thenReturn(true);
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.getFieldNameFromMaskEncryptFieldName("testField__s"))
                    .thenReturn("testField");

            ObjectDescribeExt mockObjectDescribeExt = mock(ObjectDescribeExt.class);
            when(mockObjectDescribeExt.getFieldByApiNames(Lists.newArrayList("testField")))
                    .thenReturn(testFieldDescribes);
            mockedObjectDescribeExt.when(() -> ObjectDescribeExt.of(mockObjectDescribe))
                    .thenReturn(mockObjectDescribeExt);

            // 执行被测试方法
            maskFieldEncryptService.decode(objectDataWithEncryptedFields, mockObjectDescribe);

            // 验证结果
            mockedFieldDescribeExt.verify(() -> FieldDescribeExt.isMaskEncryptField("testField__s"));
            mockedFieldDescribeExt.verify(() -> FieldDescribeExt.getFieldNameFromMaskEncryptFieldName("testField__s"));
            verify(mockObjectDescribeExt).getFieldByApiNames(Lists.newArrayList("testField"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解码时反序列化失败的异常场景
     */
    @Test
    @DisplayName("异常场景 - 解码时反序列化失败抛出ValidateException")
    void testDecodeThrowsValidateException_DeserializationFailure() {
        try (MockedStatic<FieldDescribeExt> mockedFieldDescribeExt = mockStatic(FieldDescribeExt.class);
             MockedStatic<ObjectDataExt> mockedObjectDataExt = mockStatic(ObjectDataExt.class);
             MockedStatic<EncryptUtil> mockedEncryptUtil = mockStatic(EncryptUtil.class)) {

            // 准备测试数据
            IObjectData objectDataWithInvalidEncryption = new ObjectData();
            objectDataWithInvalidEncryption.setId("testDataId");
            objectDataWithInvalidEncryption.set("testField__s", "invalidEncryptedValue");

            ObjectDataExt mockObjectDataExt = mock(ObjectDataExt.class);
            mockedObjectDataExt.when(() -> ObjectDataExt.of(objectDataWithInvalidEncryption))
                    .thenReturn(mockObjectDataExt);
            mockedFieldDescribeExt.when(() -> FieldDescribeExt.getMaskEncryptFieldName("testField"))
                    .thenReturn("testField__s");
            mockedObjectDataExt.when(() -> ObjectDataExt.isValueEmpty("invalidEncryptedValue"))
                    .thenReturn(false);
            mockedEncryptUtil.when(() -> EncryptUtil.decode("invalidEncryptedValue"))
                    .thenReturn("invalidSerializedData");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                maskFieldEncryptService.decode(objectDataWithInvalidEncryption, testFieldDescribes);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(mockObjectDataExt).remove("testField__s");
        }
    }
}
