package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.layout.component.IViewComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.ViewComponentInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ListComponentFactoryTest {

  @Mock
  private ViewComponentFactory viewComponentFactory;

  @Mock
  private User user;

  @Mock
  private IObjectDescribe describe;

  @InjectMocks
  private ListComponentFactory listComponentFactory;

  private List<IViewComponentInfo> viewComponentInfos;

  @BeforeEach
  void setUp() {
    // 使用真实的ViewComponentInfo对象而不是mock，避免类型转换问题
    ViewComponentInfo realViewInfo = new ViewComponentInfo();
    realViewInfo.setName("list_view");
    realViewInfo.setIsDefault(true);

    viewComponentInfos = Lists.newArrayList(realViewInfo);

    // 设置基本的mock行为
    when(viewComponentFactory.create(user, describe)).thenReturn(viewComponentInfos);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试createDefaultComponent方法正常场景
   */
  @Test
  @DisplayName("正常场景 - 创建默认列表组件")
  void testCreateDefaultComponent_Success() {
    // 执行被测试方法
    ListComponentExt result = listComponentFactory.createDefaultComponent(user, describe);

    // 验证结果
    assertNotNull(result);
    assertNotNull(result.getComponent());
    assertEquals(ListComponentExt.COMPONENT_TYPE_LIST, result.getComponent().getType());
    assertEquals(ListComponentExt.LIST_COMPONENT, result.getName());
    assertNotNull(result.getHeader());
    
    // 验证视图组件信息被设置
    verify(viewComponentFactory).create(user, describe);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试supportComponentType方法
   */
  @Test
  @DisplayName("正常场景 - 获取支持的组件类型")
  void testSupportComponentType_Success() {
    // 执行被测试方法
    String result = listComponentFactory.supportComponentType();

    // 验证结果
    assertNotNull(result);
    assertEquals(ListComponentExt.COMPONENT_TYPE_LIST, result);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试createDefaultComponent(Context)方法正常场景
   */
  @Test
  @DisplayName("正常场景 - 通过Context创建默认组件")
  void testCreateDefaultComponentWithContext_Success() {
    // 准备测试数据
    ILayoutFactory.Context context = ILayoutFactory.Context.of(user, describe);

    // 执行被测试方法
    IComponent result = listComponentFactory.createDefaultComponent(context, describe);

    // 验证结果
    assertNotNull(result);
    assertEquals(ListComponentExt.COMPONENT_TYPE_LIST, result.getType());
    
    // 验证视图组件信息被设置
    verify(viewComponentFactory).create(user, describe);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试createDefaultComponent方法空视图组件列表
   */
  @Test
  @DisplayName("边界场景 - 空视图组件列表")
  void testCreateDefaultComponent_EmptyViewComponents() {
    // 准备测试数据
    when(viewComponentFactory.create(user, describe)).thenReturn(Lists.newArrayList());

    // 执行被测试方法
    ListComponentExt result = listComponentFactory.createDefaultComponent(user, describe);

    // 验证结果
    assertNotNull(result);
    assertNotNull(result.getComponent());
    assertEquals(ListComponentExt.COMPONENT_TYPE_LIST, result.getComponent().getType());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试createDefaultComponent方法null参数
   */
  @Test
  @DisplayName("边界场景 - null参数处理")
  void testCreateDefaultComponent_NullParameters() {
    // 准备测试数据
    when(viewComponentFactory.create(null, null)).thenReturn(Lists.newArrayList());

    // 执行被测试方法
    assertDoesNotThrow(() -> {
      ListComponentExt result = listComponentFactory.createDefaultComponent((User) null, (IObjectDescribe) null);
      assertNotNull(result);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试组件的基本属性设置
   */
  @Test
  @DisplayName("正常场景 - 验证组件基本属性")
  void testCreateDefaultComponent_ComponentProperties() {
    // 执行被测试方法
    ListComponentExt result = listComponentFactory.createDefaultComponent(user, describe);

    // 验证结果
    assertNotNull(result);
    assertNotNull(result.getName());
    assertNotNull(result.getHeader());
    assertNotNull(result.getNameI18nKey());
    
    // 验证组件类型
    assertEquals(ListComponentExt.COMPONENT_TYPE_LIST, result.getComponent().getType());
    assertEquals(ListComponentExt.LIST_COMPONENT, result.getName());
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试基本功能验证
   */
  @Test
  @DisplayName("正常场景 - 验证基本功能")
  void testListComponentFactory_BasicFunctionality() {
    // 验证基本功能
    assertNotNull(listComponentFactory);
    assertDoesNotThrow(() -> {
      listComponentFactory.toString();
    });
    assertDoesNotThrow(() -> {
      listComponentFactory.hashCode();
    });
    assertDoesNotThrow(() -> {
      listComponentFactory.equals(listComponentFactory);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试多次调用的一致性
   */
  @Test
  @DisplayName("正常场景 - 测试多次调用的一致性")
  void testCreateDefaultComponent_MultipleCallsConsistency() {
    // 执行被测试方法
    ListComponentExt result1 = listComponentFactory.createDefaultComponent(user, describe);
    ListComponentExt result2 = listComponentFactory.createDefaultComponent(user, describe);

    // 验证结果
    assertNotNull(result1);
    assertNotNull(result2);
    assertNotSame(result1, result2); // 应该是不同的实例
    
    // 但基本属性应该相同
    assertEquals(result1.getName(), result2.getName());
    assertEquals(result1.getComponent().getType(), result2.getComponent().getType());
    
    // 验证viewComponentFactory被调用了两次
    verify(viewComponentFactory, times(2)).create(user, describe);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试异常处理能力
   */
  @Test
  @DisplayName("异常场景 - 测试异常处理")
  void testCreateDefaultComponent_ExceptionHandling() {
    // 准备测试数据 - viewComponentFactory抛出异常
    when(viewComponentFactory.create(user, describe)).thenThrow(new RuntimeException("Test exception"));

    // 执行被测试方法并验证异常传播
    assertThrows(RuntimeException.class, () -> {
      listComponentFactory.createDefaultComponent(user, describe);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Context为null的场景
   */
  @Test
  @DisplayName("边界场景 - Context为null")
  void testCreateDefaultComponentWithContext_NullContext() {
    // 执行被测试方法并验证异常
    assertThrows(NullPointerException.class, () -> {
      listComponentFactory.createDefaultComponent((ILayoutFactory.Context) null, describe);
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试组件扩展功能
   */
  @Test
  @DisplayName("正常场景 - 验证组件扩展功能")
  void testCreateDefaultComponent_ExtendedFeatures() {
    // 执行被测试方法
    ListComponentExt result = listComponentFactory.createDefaultComponent(user, describe);

    // 验证扩展功能
    assertNotNull(result);
    assertDoesNotThrow(() -> {
      // 验证可以调用扩展方法
      result.resetViewInfos(Lists.newArrayList());
      result.resetFiltersInfos(Lists.newArrayList());
      result.resetButtonInfos(Lists.newArrayList());
    });
  }
}
