package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.TagRestServiceProxy;
import com.facishare.paas.appframework.common.service.dto.FindAllTags;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.dto.tag.TagGroupTag;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.ISubTagDescribe;
import com.facishare.paas.metadata.api.describe.ITagDescribe;
import com.facishare.paas.metadata.api.service.ISubTagDescribeService;
import com.facishare.paas.metadata.api.service.ITagDataRelationService;
import com.facishare.paas.metadata.api.service.ITagDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.support.GDSHandler;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TagLogicServiceImplTest {

    @Mock
    private ISubTagDescribeService subTagDescribeService;

    @Mock
    private ITagDescribeService tagDescribeService;

    @Mock
    private TagRestServiceProxy tagRestServiceProxy;

    @Mock
    private GDSHandler gdsHandler;

    @Mock
    private LicenseService licenseService;

    @Mock
    private I18nSettingService i18nSettingService;

    @Mock
    private ITagDataRelationService tagDataRelationService;

    @InjectMocks
    private TagLogicServiceImpl tagLogicService;

    private String testObjectApiName;
    private String testTenantId;
    private String testUserId;
    private String testKeyword;
    private IActionContext mockActionContext;
    private List<ISubTagDescribe> testSubTagDescribes;
    private List<ITagDescribe> testTagDescribes;

    @BeforeEach
    void setUp() {
        testObjectApiName = "TestObj";
        testTenantId = "74255";
        testUserId = "testUserId";
        testKeyword = "test";

        mockActionContext = mock(IActionContext.class);
        when(mockActionContext.getEnterpriseId()).thenReturn(testTenantId);

        ISubTagDescribe mockSubTag = mock(ISubTagDescribe.class);
        when(mockSubTag.getId()).thenReturn("subTagId");
        when(mockSubTag.getName()).thenReturn("测试子标签");
        testSubTagDescribes = Lists.newArrayList(mockSubTag);

        ITagDescribe mockTag = mock(ITagDescribe.class);
        when(mockTag.getId()).thenReturn("tagId");
        when(mockTag.getName()).thenReturn("测试标签组");
        testTagDescribes = Lists.newArrayList(mockTag);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据角色过滤查找所有标签的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据角色过滤查找所有标签成功")
    void testFindAllTagsFilterByRole_Success() throws MetadataServiceException {
        // 准备测试数据
        when(subTagDescribeService.findSubTagListByNameAndDescribe(testObjectApiName, testKeyword, true, mockActionContext))
                .thenReturn(testSubTagDescribes);

        // 执行被测试方法
        List<ISubTagDescribe> result = tagLogicService.findAllTagsFilterByRole(
                testObjectApiName, testKeyword, true, mockActionContext);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSubTagDescribes.size(), result.size());
        verify(subTagDescribeService).findSubTagListByNameAndDescribe(testObjectApiName, testKeyword, true, mockActionContext);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据角色过滤查找所有标签时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 根据角色过滤查找所有标签时抛出MetaDataBusinessException")
    void testFindAllTagsFilterByRoleThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        MetadataServiceException serviceException = new MetadataServiceException("Service error");
        when(subTagDescribeService.findSubTagListByNameAndDescribe(testObjectApiName, testKeyword, true, mockActionContext))
                .thenThrow(serviceException);

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            tagLogicService.findAllTagsFilterByRole(testObjectApiName, testKeyword, true, mockActionContext);
        });

        // 验证异常信息
        assertNotNull(exception);
        assertEquals(serviceException, exception.getCause());
        verify(subTagDescribeService).findSubTagListByNameAndDescribe(testObjectApiName, testKeyword, true, mockActionContext);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找所有标签的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找所有标签成功")
    void testFindAllTags_Success() throws MetadataServiceException {
        // 准备测试数据
        when(subTagDescribeService.findSubTagListByNameAndDescribe(testTenantId, testObjectApiName, testKeyword, true))
                .thenReturn(testSubTagDescribes);

        // 执行被测试方法
        List<ISubTagDescribe> result = tagLogicService.findAllTags(testObjectApiName, testTenantId, testKeyword, true);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSubTagDescribes.size(), result.size());
        verify(subTagDescribeService).findSubTagListByNameAndDescribe(testTenantId, testObjectApiName, testKeyword, true);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找所有标签组的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找所有标签组成功")
    void testFindAllTagGroups_Success() throws MetadataServiceException {
        // 准备测试数据
        int tagCount = 5;
        when(tagDescribeService.findTagCount(testTenantId, testObjectApiName)).thenReturn(tagCount);
        when(tagDescribeService.findAllTag(testTenantId, testObjectApiName, 1, tagCount))
                .thenReturn(testTagDescribes);

        // 执行被测试方法
        List<ITagDescribe> result = tagLogicService.findAllTagGroups(testObjectApiName, testTenantId);

        // 验证结果
        assertNotNull(result);
        verify(tagDescribeService).findTagCount(testTenantId, testObjectApiName);
        verify(tagDescribeService).findAllTag(testTenantId, testObjectApiName, 1, tagCount);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找所有标签组时，标签数量为0的场景
     */
    @Test
    @DisplayName("正常场景 - 标签数量为0时返回空列表")
    void testFindAllTagGroups_ZeroCount() throws MetadataServiceException {
        // 准备测试数据
        when(tagDescribeService.findTagCount(testTenantId, testObjectApiName)).thenReturn(0);

        // 执行被测试方法
        List<ITagDescribe> result = tagLogicService.findAllTagGroups(testObjectApiName, testTenantId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(tagDescribeService).findTagCount(testTenantId, testObjectApiName);
        verify(tagDescribeService, never()).findAllTag(anyString(), anyString(), anyInt(), anyInt());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据名称查找标签的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据名称查找标签成功")
    void testFindTagsByName_Success() throws MetadataServiceException {
        // 准备测试数据
        String tagName = "测试标签";
        int subTagCount = 3;
        QueryResult<ISubTagDescribe> mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(testSubTagDescribes);

        when(subTagDescribeService.findSubTagCountByDescribeApiName(testTenantId, testObjectApiName))
                .thenReturn(subTagCount);
        when(subTagDescribeService.findSubTagByTagId(testTenantId, null, testObjectApiName, tagName, 1, subTagCount))
                .thenReturn(mockQueryResult);

        // 执行被测试方法
        List<ISubTagDescribe> result = tagLogicService.findTagsByName(testObjectApiName, testTenantId, tagName);

        // 验证结果
        assertNotNull(result);
        assertEquals(testSubTagDescribes, result);
        verify(subTagDescribeService).findSubTagCountByDescribeApiName(testTenantId, testObjectApiName);
        verify(subTagDescribeService).findSubTagByTagId(testTenantId, null, testObjectApiName, tagName, 1, subTagCount);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找标签组的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据ID查找标签组成功")
    void testFindTagGroupsByIds_Success() throws MetadataServiceException {
        // 准备测试数据
        Collection<String> groupIds = Lists.newArrayList("groupId1", "groupId2");
        
        when(tagDescribeService.findTagsByIds(eq(testTenantId), eq(testObjectApiName), anyList()))
                .thenReturn(testTagDescribes);

        // 执行被测试方法
        List<ITagDescribe> result = tagLogicService.findTagGroupsByIds(testObjectApiName, testTenantId, groupIds);

        // 验证结果
        assertNotNull(result);
        assertEquals(testTagDescribes.size(), result.size());
        verify(tagDescribeService).findTagsByIds(eq(testTenantId), eq(testObjectApiName), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找标签组（不带对象API名称）的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据ID查找标签组（不带对象API名称）成功")
    void testFindTagGroupsByIdsWithoutObjectApiName_Success() throws MetadataServiceException {
        // 准备测试数据
        Collection<String> groupIds = Lists.newArrayList("groupId1", "groupId2");
        
        when(tagDescribeService.findTagsByIds(eq(testTenantId), anyList()))
                .thenReturn(testTagDescribes);

        // 执行被测试方法
        List<ITagDescribe> result = tagLogicService.findTagGroupsByIds(testTenantId, groupIds);

        // 验证结果
        assertNotNull(result);
        assertEquals(testTagDescribes.size(), result.size());
        verify(tagDescribeService).findTagsByIds(eq(testTenantId), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找标签组（带实时翻译）的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据ID查找标签组（带实时翻译）成功")
    void testFindTagGroupsByIdsWithOnTime_Success() throws MetadataServiceException {
        // 准备测试数据
        Collection<String> groupIds = Lists.newArrayList("groupId1", "groupId2");
        boolean onTime = true;
        
        when(tagDescribeService.findTagsByIds(eq(testTenantId), anyList()))
                .thenReturn(testTagDescribes);

        // 执行被测试方法
        List<ITagDescribe> result = tagLogicService.findTagGroupsByIds(testTenantId, groupIds, onTime);

        // 验证结果
        assertNotNull(result);
        assertEquals(testTagDescribes.size(), result.size());
        verify(tagDescribeService).findTagsByIds(eq(testTenantId), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试通过REST接口查找标签的正常场景
     */
    @Test
    @DisplayName("正常场景 - 通过REST接口查找标签成功")
    void testFindTagsByRest_Success() {
        // 准备测试数据
        String tagName = "测试标签";
        String ea = "testEA";
        
        FindAllTags.Result mockResult = mock(FindAllTags.Result.class);
        FindAllTags.TagGroup mockTagGroup = mock(FindAllTags.TagGroup.class);
        when(mockTagGroup.getId()).thenReturn("groupId");
        when(mockTagGroup.getName()).thenReturn("标签组");
        
        FindAllTags.Tag mockTag = mock(FindAllTags.Tag.class);
        when(mockTag.getId()).thenReturn("tagId");
        when(mockTag.getName()).thenReturn("标签");
        when(mockTagGroup.getTags()).thenReturn(Lists.newArrayList(mockTag));
        
        when(mockResult.getData()).thenReturn(Lists.newArrayList(mockTagGroup));
        
        when(gdsHandler.getEAByEI(testTenantId)).thenReturn(ea);
        when(tagRestServiceProxy.findAllTags(anyMap(), anyMap())).thenReturn(mockResult);

        // 执行被测试方法
        List<TagGroupTag> result = tagLogicService.findTagsByRest(tagName, testTenantId, testUserId);

        // 验证结果
        assertNotNull(result);
        verify(gdsHandler).getEAByEI(testTenantId);
        verify(tagRestServiceProxy).findAllTags(anyMap(), anyMap());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试通过REST接口查找标签时，结果为空的场景
     */
    @Test
    @DisplayName("正常场景 - 通过REST接口查找标签结果为空时返回空列表")
    void testFindTagsByRest_EmptyResult() {
        // 准备测试数据
        String tagName = "测试标签";
        String ea = "testEA";
        
        FindAllTags.Result mockResult = mock(FindAllTags.Result.class);
        when(mockResult.getData()).thenReturn(Lists.newArrayList());
        
        when(gdsHandler.getEAByEI(testTenantId)).thenReturn(ea);
        when(tagRestServiceProxy.findAllTags(anyMap(), anyMap())).thenReturn(mockResult);

        // 执行被测试方法
        List<TagGroupTag> result = tagLogicService.findTagsByRest(tagName, testTenantId, testUserId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(gdsHandler).getEAByEI(testTenantId);
        verify(tagRestServiceProxy).findAllTags(anyMap(), anyMap());
    }
}
