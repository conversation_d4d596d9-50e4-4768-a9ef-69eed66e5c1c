package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TagLogicServiceImplTest {

    @Mock
    private MetaDataFindService metaDataFindService;

    @Mock
    private MetaDataActionService metaDataActionService;

    @Mock
    private DescribeLogicService describeLogicService;

    @InjectMocks
    private TagLogicServiceImpl tagLogicService;

    private User testUser;
    private String testTenantId;
    private String testObjectApiName;
    private String testDataId;
    private List<String> testTagNames;
    private IObjectDescribe mockObjectDescribe;
    private IObjectData mockTagData;
    private List<IObjectData> testTagDataList;

    @BeforeEach
    void setUp() {
        testUser = new User("74255", "testUserId");
        testTenantId = "74255";
        testObjectApiName = "TestObj";
        testDataId = "testDataId";
        testTagNames = Lists.newArrayList("tag1", "tag2");

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn(testObjectApiName);

        mockTagData = new ObjectData();
        mockTagData.setId("tagId1");
        mockTagData.put("name__c", "tag1");
        testTagDataList = Lists.newArrayList(mockTagData);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建标签的正常场景，验证标签创建功能
     */
    @Test
    @DisplayName("正常场景 - 创建标签成功")
    void testCreateTag_Success() {
        // 准备测试数据
        String tagName = "newTag";
        IObjectDescribe tagDescribe = mock(IObjectDescribe.class);
        when(describeLogicService.findObject(testTenantId, "TagObj")).thenReturn(tagDescribe);

        IObjectData newTag = new ObjectData();
        newTag.setId("newTagId");
        when(metaDataActionService.saveObjectData(eq(testUser), any(IObjectData.class)))
                .thenReturn(newTag);

        // 执行被测试方法
        IObjectData result = tagLogicService.createTag(testUser, tagName, testObjectApiName, testDataId);

        // 验证结果
        assertNotNull(result);
        assertEquals("newTagId", result.getId());
        verify(describeLogicService).findObject(testTenantId, "TagObj");
        verify(metaDataActionService).saveObjectData(eq(testUser), any(IObjectData.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找标签的正常场景，验证标签查询功能
     */
    @Test
    @DisplayName("正常场景 - 查找标签成功")
    void testFindTags_Success() {
        // 准备测试数据
        QueryResult<IObjectData> queryResult = mock(QueryResult.class);
        when(queryResult.getData()).thenReturn(testTagDataList);
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq("TagObj"), any(SearchTemplateQuery.class)))
                .thenReturn(queryResult);

        // 执行被测试方法
        List<IObjectData> result = tagLogicService.findTags(testUser, testObjectApiName, testDataId);

        // 验证结果
        assertNotNull(result);
        assertEquals(testTagDataList, result);
        verify(metaDataFindService).findBySearchQuery(eq(testUser), eq("TagObj"), any(SearchTemplateQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找标签时，没有找到标签的场景
     */
    @Test
    @DisplayName("正常场景 - 查找标签时没有找到标签返回空列表")
    void testFindTags_NoTagsFound() {
        // 准备测试数据
        QueryResult<IObjectData> queryResult = mock(QueryResult.class);
        when(queryResult.getData()).thenReturn(Lists.newArrayList());
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq("TagObj"), any(SearchTemplateQuery.class)))
                .thenReturn(queryResult);

        // 执行被测试方法
        List<IObjectData> result = tagLogicService.findTags(testUser, testObjectApiName, testDataId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(metaDataFindService).findBySearchQuery(eq(testUser), eq("TagObj"), any(SearchTemplateQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除标签的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除标签成功")
    void testDeleteTag_Success() {
        // 准备测试数据
        String tagId = "tagToDelete";
        IObjectData tagToDelete = new ObjectData();
        tagToDelete.setId(tagId);

        when(metaDataFindService.findObjectData(testUser, tagId, mockObjectDescribe))
                .thenReturn(tagToDelete);

        // 执行被测试方法
        tagLogicService.deleteTag(testUser, tagId);

        // 验证结果
        verify(metaDataFindService).findObjectData(testUser, tagId, mockObjectDescribe);
        verify(metaDataActionService).deleteObjectData(testUser, tagToDelete);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除标签时，标签不存在的场景
     */
    @Test
    @DisplayName("异常场景 - 删除标签时标签不存在抛出异常")
    void testDeleteTagThrowsException_TagNotExists() {
        // 准备测试数据
        String tagId = "nonExistentTag";

        when(metaDataFindService.findObjectData(testUser, tagId, mockObjectDescribe))
                .thenReturn(null);

        // 执行并验证异常
        assertThrows(MetaDataBusinessException.class, () -> {
            tagLogicService.deleteTag(testUser, tagId);
        });

        verify(metaDataFindService).findObjectData(testUser, tagId, mockObjectDescribe);
        verify(metaDataActionService, never()).deleteObjectData(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量创建标签的正常场景
     */
    @Test
    @DisplayName("正常场景 - 批量创建标签成功")
    void testBatchCreateTags_Success() {
        // 准备测试数据
        IObjectDescribe tagDescribe = mock(IObjectDescribe.class);
        when(describeLogicService.findObject(testTenantId, "TagObj")).thenReturn(tagDescribe);

        IObjectData newTag1 = new ObjectData();
        newTag1.setId("newTagId1");
        IObjectData newTag2 = new ObjectData();
        newTag2.setId("newTagId2");

        when(metaDataActionService.saveObjectData(eq(testUser), any(IObjectData.class)))
                .thenReturn(newTag1, newTag2);

        // 执行被测试方法
        List<IObjectData> result = tagLogicService.batchCreateTags(testUser, testTagNames, testObjectApiName, testDataId);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(describeLogicService).findObject(testTenantId, "TagObj");
        verify(metaDataActionService, times(2)).saveObjectData(eq(testUser), any(IObjectData.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量创建标签时，标签名称列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 批量创建标签时标签名称列表为空返回空列表")
    void testBatchCreateTags_EmptyTagNames() {
        // 准备测试数据
        List<String> emptyTagNames = Lists.newArrayList();

        // 执行被测试方法
        List<IObjectData> result = tagLogicService.batchCreateTags(testUser, emptyTagNames, testObjectApiName, testDataId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(describeLogicService, never()).findObject(any(), any());
        verify(metaDataActionService, never()).saveObjectData(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新标签的正常场景
     */
    @Test
    @DisplayName("正常场景 - 更新标签成功")
    void testUpdateTag_Success() {
        // 准备测试数据
        String tagId = "tagToUpdate";
        String newTagName = "updatedTagName";
        
        IObjectData existingTag = new ObjectData();
        existingTag.setId(tagId);
        existingTag.put("name__c", "oldTagName");

        when(metaDataFindService.findObjectData(testUser, tagId, mockObjectDescribe))
                .thenReturn(existingTag);

        IObjectData updatedTag = new ObjectData();
        updatedTag.setId(tagId);
        updatedTag.put("name__c", newTagName);
        when(metaDataActionService.updateObjectData(eq(testUser), any(IObjectData.class)))
                .thenReturn(updatedTag);

        // 执行被测试方法
        IObjectData result = tagLogicService.updateTag(testUser, tagId, newTagName);

        // 验证结果
        assertNotNull(result);
        assertEquals(tagId, result.getId());
        assertEquals(newTagName, result.get("name__c"));
        verify(metaDataFindService).findObjectData(testUser, tagId, mockObjectDescribe);
        verify(metaDataActionService).updateObjectData(eq(testUser), any(IObjectData.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新标签时，标签不存在的场景
     */
    @Test
    @DisplayName("异常场景 - 更新标签时标签不存在抛出异常")
    void testUpdateTagThrowsException_TagNotExists() {
        // 准备测试数据
        String tagId = "nonExistentTag";
        String newTagName = "updatedTagName";

        when(metaDataFindService.findObjectData(testUser, tagId, mockObjectDescribe))
                .thenReturn(null);

        // 执行并验证异常
        assertThrows(MetaDataBusinessException.class, () -> {
            tagLogicService.updateTag(testUser, tagId, newTagName);
        });

        verify(metaDataFindService).findObjectData(testUser, tagId, mockObjectDescribe);
        verify(metaDataActionService, never()).updateObjectData(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据标签名称查找标签的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据标签名称查找标签成功")
    void testFindTagByName_Success() {
        // 准备测试数据
        String tagName = "searchTag";
        QueryResult<IObjectData> queryResult = mock(QueryResult.class);
        when(queryResult.getData()).thenReturn(testTagDataList);
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq("TagObj"), any(SearchTemplateQuery.class)))
                .thenReturn(queryResult);

        // 执行被测试方法
        IObjectData result = tagLogicService.findTagByName(testUser, tagName, testObjectApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockTagData, result);
        verify(metaDataFindService).findBySearchQuery(eq(testUser), eq("TagObj"), any(SearchTemplateQuery.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据标签名称查找标签时，标签不存在的场景
     */
    @Test
    @DisplayName("正常场景 - 根据标签名称查找标签时标签不存在返回null")
    void testFindTagByName_TagNotExists() {
        // 准备测试数据
        String tagName = "nonExistentTag";
        QueryResult<IObjectData> queryResult = mock(QueryResult.class);
        when(queryResult.getData()).thenReturn(Lists.newArrayList());
        when(metaDataFindService.findBySearchQuery(eq(testUser), eq("TagObj"), any(SearchTemplateQuery.class)))
                .thenReturn(queryResult);

        // 执行被测试方法
        IObjectData result = tagLogicService.findTagByName(testUser, tagName, testObjectApiName);

        // 验证结果
        assertNull(result);
        verify(metaDataFindService).findBySearchQuery(eq(testUser), eq("TagObj"), any(SearchTemplateQuery.class));
    }
}
