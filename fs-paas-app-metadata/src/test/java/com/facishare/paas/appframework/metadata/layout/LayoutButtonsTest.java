package com.facishare.paas.appframework.metadata.layout;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * LayoutButtons 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("LayoutButtons 测试")
class LayoutButtonsTest {

  @Mock
  private ObjectDescribeExt objectDescribeExt;

  @BeforeEach
  void setUp() {
    // 测试前准备
  }

  @Test
  @DisplayName("测试获取实例 - 已知对象")
  void testGetInstance_KnownObject() {
    // 准备测试数据
    when(objectDescribeExt.getApiName()).thenReturn("AccountObj");

    // 执行测试
    LayoutButtons result = LayoutButtons.getInstance(objectDescribeExt);

    // 验证结果
    assertNotNull(result);
    assertEquals(LayoutButtons.AccountObj, result);
  }

  @Test
  @DisplayName("测试获取实例 - 未知对象")
  void testGetInstance_UnknownObject() {
    // 准备测试数据
    when(objectDescribeExt.getApiName()).thenReturn("UnknownObj");

    // 执行测试
    LayoutButtons result = LayoutButtons.getInstance(objectDescribeExt);

    // 验证结果
    assertNotNull(result);
    assertEquals(LayoutButtons.UserDefinedObj, result);
  }

  @Test
  @DisplayName("测试获取动作按钮 - AccountObj")
  void testGetActionButtons_AccountObj() {
    // 执行测试
    List<IButton> result = LayoutButtons.AccountObj.getActionButtons();

    // 验证结果
    assertNotNull(result);
    assertFalse(result.isEmpty());

    // 验证包含预期的按钮（基于ObjectAction的actionCode）
    assertTrue(result.stream().anyMatch(button -> "Edit".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "SaleRecord".equals(button.getAction())));
  }

  @Test
  @DisplayName("测试获取动作按钮 - ContactObj")
  void testGetActionButtons_ContactObj() {
    // 执行测试
    List<IButton> result = LayoutButtons.ContactObj.getActionButtons();

    // 验证结果
    assertNotNull(result);
    assertFalse(result.isEmpty());

    // 验证包含预期的按钮
    assertTrue(result.stream().anyMatch(button -> "Edit".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Dial".equals(button.getAction())));
  }

  @Test
  @DisplayName("测试获取动作按钮 - OpportunityObj")
  void testGetActionButtons_OpportunityObj() {
    // 执行测试
    List<IButton> result = LayoutButtons.OpportunityObj.getActionButtons();

    // 验证结果
    assertNotNull(result);
    assertFalse(result.isEmpty());

    // 验证包含预期的按钮
    assertTrue(result.stream().anyMatch(button -> "Edit".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "ChangeSaleAction".equals(button.getAction())));
  }

  @Test
  @DisplayName("测试获取动作按钮 - LeadsObj")
  void testGetActionButtons_LeadsObj() {
    // 执行测试
    List<IButton> result = LayoutButtons.LeadsObj.getActionButtons();

    // 验证结果
    assertNotNull(result);
    assertFalse(result.isEmpty());

    // 验证包含预期的按钮
    assertTrue(result.stream().anyMatch(button -> "Edit".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Transfer".equals(button.getAction())));
  }

  @Test
  @DisplayName("测试获取动作按钮 - UserDefinedObj")
  void testGetActionButtons_UserDefinedObj() {
    // 执行测试
    List<IButton> result = LayoutButtons.UserDefinedObj.getActionButtons();

    // 验证结果
    assertNotNull(result);
    assertFalse(result.isEmpty());

    // 验证包含预期的按钮
    assertTrue(result.stream().anyMatch(button -> "Edit".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "StartStagePropellor".equals(button.getAction())));
  }

  @Test
  @DisplayName("测试获取尾部按钮 - 普通对象")
  void testGetTailButtons_NormalObject() {
    // 执行测试
    List<IButton> result = LayoutButtons.AccountObj.getTailButtons();

    // 验证结果
    assertNotNull(result);
    assertFalse(result.isEmpty());
    
    // 验证包含预期的尾部按钮
    assertTrue(result.stream().anyMatch(button -> "SendMail".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Discuss".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Remind".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Schedule".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Print".equals(button.getAction())));
  }

  @Test
  @DisplayName("测试获取尾部按钮 - 销售订单产品")
  void testGetTailButtons_SalesOrderProduct() {
    // 执行测试
    List<IButton> result = LayoutButtons.SalesOrderProductObj.getTailButtons();

    // 验证结果
    assertNotNull(result);
    assertFalse(result.isEmpty());

    // 验证不包含打印按钮
    assertFalse(result.stream().anyMatch(button -> "Print".equals(button.getAction())));
    // 验证包含其他按钮
    assertTrue(result.stream().anyMatch(button -> "SendMail".equals(button.getAction())));
  }

  @Test
  @DisplayName("测试获取尾部按钮 - 退货单产品")
  void testGetTailButtons_ReturnedGoodsInvoiceProduct() {
    // 执行测试
    List<IButton> result = LayoutButtons.ReturnedGoodsInvoiceProductObj.getTailButtons();

    // 验证结果
    assertNotNull(result);
    assertFalse(result.isEmpty());

    // 验证不包含打印按钮
    assertFalse(result.stream().anyMatch(button -> "Print".equals(button.getAction())));
    // 验证包含其他按钮
    assertTrue(result.stream().anyMatch(button -> "SendMail".equals(button.getAction())));
  }

  @Test
  @DisplayName("测试获取动作按钮 - CommonTail")
  void testGetActionButtons_CommonTail() {
    // 执行测试
    List<IButton> result = LayoutButtons.CommonTail.getActionButtons();

    // 验证结果
    assertNotNull(result);
    assertEquals(5, result.size());
    
    // 验证包含预期的按钮
    assertTrue(result.stream().anyMatch(button -> "SendMail".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Discuss".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Remind".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Schedule".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Print".equals(button.getAction())));
  }

  @Test
  @DisplayName("测试获取动作按钮 - Supplements")
  void testGetActionButtons_Supplements() {
    // 执行测试
    List<IButton> result = LayoutButtons.Supplements.getActionButtons();

    // 验证结果
    assertNotNull(result);
    assertEquals(4, result.size());
    
    // 验证包含预期的按钮
    assertTrue(result.stream().anyMatch(button -> "SendMail".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Discuss".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Remind".equals(button.getAction())));
    assertTrue(result.stream().anyMatch(button -> "Schedule".equals(button.getAction())));
    // 验证不包含打印按钮
    assertFalse(result.stream().anyMatch(button -> "Print".equals(button.getAction())));
  }

  @Test
  @DisplayName("测试获取动作按钮 - WebOnlyButtons")
  void testGetActionButtons_WebOnlyButtons() {
    // 执行测试
    List<IButton> result = LayoutButtons.WebOnlyButtons.getActionButtons();

    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    
    // 验证包含预期的按钮
    assertTrue(result.stream().anyMatch(button -> "AddSpec".equals(button.getAction())));
  }

  @Test
  @DisplayName("测试按钮属性 - 验证按钮基本属性")
  void testButtonProperties() {
    // 执行测试
    List<IButton> buttons = LayoutButtons.AccountObj.getActionButtons();

    // 验证结果
    assertNotNull(buttons);
    for (IButton button : buttons) {
      assertNotNull(button.getName());
      assertNotNull(button.getAction());
      assertNotNull(button.getLabel());
      assertEquals("default", button.getActionType());
    }
  }

  @Test
  @DisplayName("测试枚举值完整性")
  void testEnumCompleteness() {
    // 验证所有枚举值都能正常工作
    for (LayoutButtons layoutButton : LayoutButtons.values()) {
      assertDoesNotThrow(() -> {
        assertNotNull(layoutButton.getActionButtons());
        assertNotNull(layoutButton.getTailButtons());
      });
    }
  }

  @Test
  @DisplayName("测试特殊对象按钮 - ElectronicSignerObj")
  void testGetActionButtons_ElectronicSignerObj() {
    // 执行测试
    List<IButton> result = LayoutButtons.ElectronicSignerObj.getActionButtons();

    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    
    // 验证包含预期的按钮
    assertTrue(result.stream().anyMatch(button -> "SignFile".equals(button.getAction())));
  }
}
