package com.facishare.paas.appframework.metadata.mtresource;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.mtresource.model.ConfigurationPackageResource;
import com.facishare.paas.appframework.metadata.mtresource.model.FieldControlInfoHelper;
import com.facishare.paas.appframework.metadata.mtresource.model.ResourceQuery;
import com.facishare.paas.appframework.metadata.repository.model.MtResource;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ConfigurationPackageResourceLogicServiceImplTest {

    @Mock
    private IMtResourceService mtResourceService;

    @InjectMocks
    private ConfigurationPackageResourceLogicServiceImpl configurationPackageResourceLogicService;

    private User testUser;
    private String testResourceParentValue;
    private String testResourceType;
    private String testSourceType;
    private String testSourceValue;
    private ResourceQuery testResourceQuery;
    private List<MtResource> testMtResources;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setTenantId("74255");
        testUser.setUserId("testUserId");

        testResourceParentValue = "TestObj";
        testResourceType = "field";
        testSourceType = "sourceType";
        testSourceValue = "sourceValue";

        // 创建测试用的ResourceQuery
        testResourceQuery = mock(ResourceQuery.class);
        Map<String, List<ConfigurationPackageResource.ResourceValue>> resourceValues = Maps.newHashMap();
        resourceValues.put("field1", Lists.newArrayList());
        resourceValues.put("field2", Lists.newArrayList());
        when(testResourceQuery.mackResourceValues()).thenReturn(resourceValues);
        when(testResourceQuery.getKeys()).thenReturn(null);

        // 创建测试用的MtResource对象
        MtResource mtResource1 = new MtResource();
        mtResource1.setTenantId("74255");
        mtResource1.setResourceParentValue(testResourceParentValue);
        mtResource1.setResourceType(testResourceType);
        mtResource1.setResourceValue("field1");
        mtResource1.setControlLevel("controlLevel");
        mtResource1.setSourceType(testSourceType);
        mtResource1.setSourceValue(testSourceValue);

        MtResource mtResource2 = new MtResource();
        mtResource2.setTenantId("74255");
        mtResource2.setResourceParentValue(testResourceParentValue);
        mtResource2.setResourceType(testResourceType);
        mtResource2.setResourceValue("field2");
        mtResource2.setControlLevel("controlLevel");
        mtResource2.setSourceType(testSourceType);
        mtResource2.setSourceValue(testSourceValue);

        testMtResources = Lists.newArrayList(mtResource1, mtResource2);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找配置包资源的正常场景，验证能正确返回配置包资源列表
     */
    @Test
    @DisplayName("正常场景 - 查找配置包资源成功")
    void testFind_Success() {
        // 准备测试数据
        when(mtResourceService.queryResource(eq("74255"), eq(testResourceParentValue), eq(testResourceType), 
                anyList(), eq(testSourceType), eq(testSourceValue)))
                .thenReturn(testMtResources);

        // 执行被测试方法
        List<ConfigurationPackageResource> result = configurationPackageResourceLogicService.find(
                testUser, testResourceParentValue, testResourceType, testResourceQuery, testSourceType, testSourceValue);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(mtResourceService).queryResource(eq("74255"), eq(testResourceParentValue), eq(testResourceType), 
                anyList(), eq(testSourceType), eq(testSourceValue));
        verify(testResourceQuery).mackResourceValues();
        verify(testResourceQuery).getKeys();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找配置包资源时，资源父值为空的场景
     */
    @Test
    @DisplayName("正常场景 - 资源父值为空时使用默认值")
    void testFind_EmptyResourceParentValue() {
        // 准备测试数据
        String emptyResourceParentValue = "";
        
        when(mtResourceService.queryResource(eq("74255"), eq(ConfigurationPackageResource.EMPTY), eq(testResourceType), 
                anyList(), eq(testSourceType), eq(testSourceValue)))
                .thenReturn(testMtResources);

        // 执行被测试方法
        List<ConfigurationPackageResource> result = configurationPackageResourceLogicService.find(
                testUser, emptyResourceParentValue, testResourceType, testResourceQuery, testSourceType, testSourceValue);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(mtResourceService).queryResource(eq("74255"), eq(ConfigurationPackageResource.EMPTY), eq(testResourceType), 
                anyList(), eq(testSourceType), eq(testSourceValue));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据资源类型查找配置包资源的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据资源类型查找配置包资源成功")
    void testFindByResourceTypes_Success() {
        // 准备测试数据
        Collection<String> resourceTypes = Lists.newArrayList("field", "object");
        String describeApiName = "TestObj";

        when(mtResourceService.queryResourceByResourceTypes(eq("74255"), eq(testResourceParentValue), 
                eq(resourceTypes), eq(describeApiName)))
                .thenReturn(testMtResources);

        // 执行被测试方法
        List<ConfigurationPackageResource> result = configurationPackageResourceLogicService.findByResourceTypes(
                testUser, testResourceParentValue, resourceTypes, describeApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(mtResourceService).queryResourceByResourceTypes(eq("74255"), eq(testResourceParentValue), 
                eq(resourceTypes), eq(describeApiName));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试修改资源的正常场景，验证能正确处理资源修改
     */
    @Test
    @DisplayName("正常场景 - 修改资源成功")
    void testModifyResource_Success() {
        // 准备测试数据
        ConfigurationPackageResource resource1 = mock(ConfigurationPackageResource.class);
        when(resource1.groupByKey()).thenReturn("group1");
        when(resource1.getResourceValue()).thenReturn("field1");
        when(resource1.getResourceParentValue()).thenReturn(testResourceParentValue);
        when(resource1.getResourceType()).thenReturn(testResourceType);
        when(resource1.getControlLevel()).thenReturn("controlLevel");
        when(resource1.getSourceType()).thenReturn(testSourceType);
        when(resource1.getSourceValue()).thenReturn(testSourceValue);

        ConfigurationPackageResource resource2 = mock(ConfigurationPackageResource.class);
        when(resource2.groupByKey()).thenReturn("group1");
        when(resource2.getResourceValue()).thenReturn("field2");

        Collection<ConfigurationPackageResource> resources = Lists.newArrayList(resource1, resource2);

        when(mtResourceService.modifyResource(eq("74255"), eq(testResourceParentValue), eq(testResourceType), 
                anyList(), eq("controlLevel"), eq(testSourceType), eq(testSourceValue)))
                .thenReturn(Lists.newArrayList());

        // 执行被测试方法
        configurationPackageResourceLogicService.modifyResource(testUser, resources);

        // 验证结果
        verify(mtResourceService).modifyResource(eq("74255"), eq(testResourceParentValue), eq(testResourceType), 
                anyList(), eq("controlLevel"), eq(testSourceType), eq(testSourceValue));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据资源类型统计数量的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据资源类型统计数量成功")
    void testCountByResourceType_Success() {
        // 准备测试数据
        Integer expectedCount = 5;

        when(mtResourceService.countByResourceType(testUser, testResourceParentValue, testResourceType))
                .thenReturn(expectedCount);

        // 执行被测试方法
        Integer result = configurationPackageResourceLogicService.countByResourceType(
                testUser, testResourceParentValue, testResourceType);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCount, result);
        verify(mtResourceService).countByResourceType(testUser, testResourceParentValue, testResourceType);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找字段控制配置的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找字段控制配置成功")
    void testFindFieldControlConfigs_Success() {
        // 准备测试数据
        String describeApiName = "TestObj";

        when(mtResourceService.queryResource(eq("74255"), eq(describeApiName), 
                eq(MtResource.RESOURCE_TYPE_FILED), isNull(), eq(MtResource.SOURCE_TYPE_FIELD_CONTROL), isNull()))
                .thenReturn(testMtResources);

        // 执行被测试方法
        List<MtResource> result = configurationPackageResourceLogicService.findFieldControlConfigs(
                testUser, describeApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals(testMtResources, result);
        verify(mtResourceService).queryResource(eq("74255"), eq(describeApiName), 
                eq(MtResource.RESOURCE_TYPE_FILED), isNull(), eq(MtResource.SOURCE_TYPE_FIELD_CONTROL), isNull());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新字段控制配置的正常场景
     */
    @Test
    @DisplayName("正常场景 - 更新字段控制配置成功")
    void testUpdateFieldControlConfigs_Success() {
        // 准备测试数据
        String describeApiName = "TestObj";
        FieldControlInfoHelper fieldControlInfoHelper = mock(FieldControlInfoHelper.class);
        
        FieldControlInfoHelper.FieldControlInfoHelperItem strictControl = mock(FieldControlInfoHelper.FieldControlInfoHelperItem.class);
        FieldControlInfoHelper.FieldControlInfoHelperItem weakControl = mock(FieldControlInfoHelper.FieldControlInfoHelperItem.class);
        FieldControlInfoHelper.FieldControlInfoHelperItem deleteControl = mock(FieldControlInfoHelper.FieldControlInfoHelperItem.class);

        when(fieldControlInfoHelper.getStrictControl()).thenReturn(strictControl);
        when(fieldControlInfoHelper.getWeakControl()).thenReturn(weakControl);
        when(fieldControlInfoHelper.getDeleteControl()).thenReturn(deleteControl);

        when(strictControl.isEmpty()).thenReturn(false);
        when(weakControl.isEmpty()).thenReturn(false);
        when(deleteControl.isEmpty()).thenReturn(false);

        when(strictControl.getFieldNames()).thenReturn(Lists.newArrayList("field1"));
        when(strictControl.getControlType()).thenReturn("strict");
        when(weakControl.getFieldNames()).thenReturn(Lists.newArrayList("field2"));
        when(weakControl.getControlType()).thenReturn("weak");
        when(deleteControl.getFieldNames()).thenReturn(Lists.newArrayList("field3"));

        when(mtResourceService.modifyResource(anyString(), anyString(), anyString(), anyList(), anyString(), anyString(), anyString()))
                .thenReturn(Lists.newArrayList());

        // 执行被测试方法
        configurationPackageResourceLogicService.updateFieldControlConfigs(testUser, describeApiName, fieldControlInfoHelper);

        // 验证结果
        verify(mtResourceService, times(2)).modifyResource(anyString(), anyString(), anyString(), anyList(), anyString(), anyString(), anyString());
        verify(mtResourceService).disableResource(anyString(), anyString(), anyString(), anyList(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新字段控制配置时，控制项为空的场景
     */
    @Test
    @DisplayName("正常场景 - 更新字段控制配置时控制项为空不执行操作")
    void testUpdateFieldControlConfigs_EmptyControlItems() {
        // 准备测试数据
        String describeApiName = "TestObj";
        FieldControlInfoHelper fieldControlInfoHelper = mock(FieldControlInfoHelper.class);
        
        FieldControlInfoHelper.FieldControlInfoHelperItem strictControl = mock(FieldControlInfoHelper.FieldControlInfoHelperItem.class);
        FieldControlInfoHelper.FieldControlInfoHelperItem weakControl = mock(FieldControlInfoHelper.FieldControlInfoHelperItem.class);
        FieldControlInfoHelper.FieldControlInfoHelperItem deleteControl = mock(FieldControlInfoHelper.FieldControlInfoHelperItem.class);

        when(fieldControlInfoHelper.getStrictControl()).thenReturn(strictControl);
        when(fieldControlInfoHelper.getWeakControl()).thenReturn(weakControl);
        when(fieldControlInfoHelper.getDeleteControl()).thenReturn(deleteControl);

        // 所有控制项都为空
        when(strictControl.isEmpty()).thenReturn(true);
        when(weakControl.isEmpty()).thenReturn(true);
        when(deleteControl.isEmpty()).thenReturn(true);

        // 执行被测试方法
        configurationPackageResourceLogicService.updateFieldControlConfigs(testUser, describeApiName, fieldControlInfoHelper);

        // 验证结果 - 由于所有控制项都为空，不应该调用任何服务方法
        verify(mtResourceService, never()).modifyResource(anyString(), anyString(), anyString(), anyList(), anyString(), anyString(), anyString());
        verify(mtResourceService, never()).disableResource(anyString(), anyString(), anyString(), anyList(), anyString());
    }
}
