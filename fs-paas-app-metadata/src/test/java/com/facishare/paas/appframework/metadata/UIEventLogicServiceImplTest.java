package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IUIEvent;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.UIEvent;
import com.facishare.paas.metadata.service.impl.UIEventService;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UIEventLogicServiceImplTest {

    @Mock
    private UIEventService eventService;

    @InjectMocks
    private UIEventLogicServiceImpl uiEventLogicService;

    private User testUser;
    private String testTenantId;
    private String testDescribeApiName;
    private List<IUIEvent> testUIEvents;
    private List<String> testEventIds;
    private IActionContext mockActionContext;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setTenantId("74255");
        testUser.setUserId("testUserId");

        testTenantId = "74255";
        testDescribeApiName = "TestObj";

        IUIEvent mockUIEvent = new UIEvent();
        mockUIEvent.setId("eventId1");
        mockUIEvent.setDescribeApiName(testDescribeApiName);
        testUIEvents = Lists.newArrayList(mockUIEvent);

        testEventIds = Lists.newArrayList("eventId1", "eventId2");

        mockActionContext = mock(IActionContext.class);
        when(mockActionContext.getEnterpriseId()).thenReturn(testTenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建UI事件的正常场景，验证批量创建功能
     */
    @Test
    @DisplayName("正常场景 - 创建UI事件成功")
    void testCreateEvents_Success() throws MetadataServiceException {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<ActionContextExt> mockedActionContextExt = mockStatic(ActionContextExt.class)) {

            // 准备测试数据
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(null);

            ActionContextExt mockActionContextExt = mock(ActionContextExt.class);
            mockedActionContextExt.when(() -> ActionContextExt.of(testUser)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(mockActionContext);

            when(eventService.batchInsert(testUIEvents, mockActionContext)).thenReturn(testUIEvents);

            // 执行被测试方法
            List<IUIEvent> result = uiEventLogicService.createEvents(testUIEvents, testUser);

            // 验证结果
            assertNotNull(result);
            assertEquals(testUIEvents, result);
            verify(eventService).batchInsert(testUIEvents, mockActionContext);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建UI事件时，事件列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 创建UI事件时事件列表为空返回空列表")
    void testCreateEvents_EmptyEvents() {
        // 准备测试数据
        List<IUIEvent> emptyEvents = Lists.newArrayList();

        // 执行被测试方法
        List<IUIEvent> result = uiEventLogicService.createEvents(emptyEvents, testUser);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(eventService, never()).batchInsert(anyList(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建UI事件时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 创建UI事件时服务抛出MetaDataBusinessException")
    void testCreateEventsThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<ActionContextExt> mockedActionContextExt = mockStatic(ActionContextExt.class);
             MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {

            // 准备测试数据
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(null);

            ActionContextExt mockActionContextExt = mock(ActionContextExt.class);
            mockedActionContextExt.when(() -> ActionContextExt.of(testUser)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(mockActionContext);

            MetadataServiceException serviceException = new MetadataServiceException("Service error");
            when(eventService.batchInsert(testUIEvents, mockActionContext)).thenThrow(serviceException);

            mockedI18N.when(() -> I18N.text(I18NKey.UI_EVENT_SAVE_FAIL))
                    .thenReturn("UI事件保存失败");

            // 执行并验证异常
            MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
                uiEventLogicService.createEvents(testUIEvents, testUser);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(eventService).batchInsert(testUIEvents, mockActionContext);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新UI事件的正常场景
     */
    @Test
    @DisplayName("正常场景 - 创建或更新UI事件成功")
    void testCreateOrUpdateEvents_Success() throws MetadataServiceException {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<ActionContextExt> mockedActionContextExt = mockStatic(ActionContextExt.class)) {

            // 准备测试数据
            RequestContext mockRequestContext = mock(RequestContext.class);
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(mockRequestContext);

            ActionContextExt mockActionContextExt = mock(ActionContextExt.class);
            mockedActionContextExt.when(() -> ActionContextExt.of(testUser, mockRequestContext)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(mockActionContext);

            when(eventService.batchUpsert(testUIEvents, mockActionContext)).thenReturn(testUIEvents);

            // 执行被测试方法
            List<IUIEvent> result = uiEventLogicService.createOrUpdateEvents(testUIEvents, testUser);

            // 验证结果
            assertNotNull(result);
            assertEquals(testUIEvents, result);
            verify(eventService).batchUpsert(testUIEvents, mockActionContext);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建或更新UI事件时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 创建或更新UI事件时服务抛出MetaDataBusinessException")
    void testCreateOrUpdateEventsThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<ActionContextExt> mockedActionContextExt = mockStatic(ActionContextExt.class)) {

            // 准备测试数据
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(null);

            ActionContextExt mockActionContextExt = mock(ActionContextExt.class);
            mockedActionContextExt.when(() -> ActionContextExt.of(testUser)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(mockActionContext);

            MetadataServiceException serviceException = new MetadataServiceException("Service error");
            when(eventService.batchUpsert(testUIEvents, mockActionContext)).thenThrow(serviceException);

            // 执行并验证异常
            MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
                uiEventLogicService.createOrUpdateEvents(testUIEvents, testUser);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(eventService).batchUpsert(testUIEvents, mockActionContext);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID列表查找UI事件的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据ID列表查找UI事件成功")
    void testFindEventListByIds_Success() {
        // 准备测试数据
        when(eventService.findUIEventsByIdList(testTenantId, testEventIds)).thenReturn(testUIEvents);

        // 执行被测试方法
        List<IUIEvent> result = uiEventLogicService.findEventListByIds(testEventIds, testTenantId);

        // 验证结果
        assertNotNull(result);
        assertEquals(testUIEvents, result);
        verify(eventService).findUIEventsByIdList(testTenantId, testEventIds);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID列表查找UI事件时，ID列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 根据ID列表查找UI事件时ID列表为空返回空列表")
    void testFindEventListByIds_EmptyIds() {
        // 准备测试数据
        List<String> emptyIds = Lists.newArrayList();

        // 执行被测试方法
        List<IUIEvent> result = uiEventLogicService.findEventListByIds(emptyIds, testTenantId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(eventService, never()).findUIEventsByIdList(anyString(), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID列表查找UI事件时，服务返回null的场景
     */
    @Test
    @DisplayName("正常场景 - 根据ID列表查找UI事件时服务返回null返回空列表")
    void testFindEventListByIds_ServiceReturnsNull() {
        // 准备测试数据
        when(eventService.findUIEventsByIdList(testTenantId, testEventIds)).thenReturn(null);

        // 执行被测试方法
        List<IUIEvent> result = uiEventLogicService.findEventListByIds(testEventIds, testTenantId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(eventService).findUIEventsByIdList(testTenantId, testEventIds);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量删除UI事件的正常场景
     */
    @Test
    @DisplayName("正常场景 - 批量删除UI事件成功")
    void testBatchDelete_Success() throws MetadataServiceException {
        // 执行被测试方法
        uiEventLogicService.batchDelete(testEventIds, testUser);

        // 验证结果
        verify(eventService).batchDelete(testTenantId, testEventIds);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量删除UI事件时，ID列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 批量删除UI事件时ID列表为空直接返回")
    void testBatchDelete_EmptyIds() throws MetadataServiceException {
        // 准备测试数据
        List<String> emptyIds = Lists.newArrayList();

        // 执行被测试方法
        uiEventLogicService.batchDelete(emptyIds, testUser);

        // 验证结果
        verify(eventService, never()).batchDelete(anyString(), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量删除UI事件时，包含空字符串ID的场景
     */
    @Test
    @DisplayName("正常场景 - 批量删除UI事件时过滤空字符串ID")
    void testBatchDelete_FilterEmptyStringIds() throws MetadataServiceException {
        // 准备测试数据
        List<String> idsWithEmpty = Lists.newArrayList("eventId1", "", "eventId2");
        List<String> expectedIds = Lists.newArrayList("eventId1", "eventId2");

        // 执行被测试方法
        uiEventLogicService.batchDelete(idsWithEmpty, testUser);

        // 验证结果
        verify(eventService).batchDelete(eq(testTenantId), eq(expectedIds));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量删除UI事件时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 批量删除UI事件时服务抛出MetaDataBusinessException")
    void testBatchDeleteThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // 准备测试数据
            MetadataServiceException serviceException = new MetadataServiceException("Service error");
            doThrow(serviceException).when(eventService).batchDelete(testTenantId, testEventIds);

            mockedI18N.when(() -> I18N.text(I18NKey.UI_EVENT_DELETE_FAIL))
                    .thenReturn("UI事件删除失败");

            // 执行并验证异常
            MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
                uiEventLogicService.batchDelete(testEventIds, testUser);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(eventService).batchDelete(testTenantId, testEventIds);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找UI事件的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据ID查找UI事件成功")
    void testFindEventById_Success() {
        // 准备测试数据
        String eventId = "eventId1";
        IUIEvent mockUIEvent = new UIEvent();
        when(eventService.findByTenantIdAndId(testTenantId, eventId)).thenReturn(mockUIEvent);

        // 执行被测试方法
        IUIEvent result = uiEventLogicService.findEventById(eventId, testTenantId);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockUIEvent, result);
        verify(eventService).findByTenantIdAndId(testTenantId, eventId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找UI事件时，ID为空的场景
     */
    @Test
    @DisplayName("正常场景 - 根据ID查找UI事件时ID为空返回null")
    void testFindEventById_EmptyId() {
        // 准备测试数据
        String emptyId = "";

        // 执行被测试方法
        IUIEvent result = uiEventLogicService.findEventById(emptyId, testTenantId);

        // 验证结果
        assertNull(result);
        verify(eventService, never()).findByTenantIdAndId(anyString(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量更新UI事件的正常场景
     */
    @Test
    @DisplayName("正常场景 - 批量更新UI事件成功")
    void testBatchUpdateUIEvent_Success() throws MetadataServiceException {
        try (MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<ActionContextExt> mockedActionContextExt = mockStatic(ActionContextExt.class)) {

            // 准备测试数据
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(null);

            ActionContextExt mockActionContextExt = mock(ActionContextExt.class);
            mockedActionContextExt.when(() -> ActionContextExt.of(testUser)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(mockActionContext);

            when(eventService.batchUpdate(testUIEvents, mockActionContext)).thenReturn(testUIEvents);

            // 执行被测试方法
            List<IUIEvent> result = uiEventLogicService.batchUpdateUIEvent(testUIEvents, testUser);

            // 验证结果
            assertNotNull(result);
            assertEquals(testUIEvents, result);
            verify(eventService).batchUpdate(testUIEvents, mockActionContext);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量更新UI事件时，事件列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 批量更新UI事件时事件列表为空返回空列表")
    void testBatchUpdateUIEvent_EmptyEvents() {
        // 准备测试数据
        List<IUIEvent> emptyEvents = Lists.newArrayList();

        // 执行被测试方法
        List<IUIEvent> result = uiEventLogicService.batchUpdateUIEvent(emptyEvents, testUser);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(eventService, never()).batchUpdate(anyList(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据对象查找UI事件的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据对象查找UI事件成功")
    void testFindEventByObject_Success() {
        // 准备测试数据
        when(eventService.findUIEventsByDescribeApiName(testTenantId, testDescribeApiName))
                .thenReturn(testUIEvents);

        // 执行被测试方法
        List<IUIEvent> result = uiEventLogicService.findEventByObject(testDescribeApiName, testTenantId);

        // 验证结果
        assertNotNull(result);
        assertEquals(testUIEvents, result);
        verify(eventService).findUIEventsByDescribeApiName(testTenantId, testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据对象查找UI事件时，对象API名称为空的场景
     */
    @Test
    @DisplayName("正常场景 - 根据对象查找UI事件时对象API名称为空返回null")
    void testFindEventByObject_EmptyDescribeApiName() {
        // 准备测试数据
        String emptyDescribeApiName = "";

        // 执行被测试方法
        List<IUIEvent> result = uiEventLogicService.findEventByObject(emptyDescribeApiName, testTenantId);

        // 验证结果
        assertNull(result);
        verify(eventService, never()).findUIEventsByDescribeApiName(anyString(), anyString());
    }
}
