package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.expression.RuleCalculateResult;
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.Rule;
import com.facishare.paas.metadata.service.impl.RuleServiceImpl;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ValidateRuleServiceImplTest {

    @Mock
    private RuleServiceImpl ruleService;

    @Mock
    private ExpressionCalculateLogicService expressionCalculateLogicService;

    @Mock
    private LicenseService licenseService;

    @Mock
    private SwitchCacheService switchCacheService;

    @InjectMocks
    private ValidateRuleServiceImpl validateRuleService;

    private User testUser;
    private String testTenantId;
    private IObjectDescribe mockObjectDescribe;
    private IObjectData testObjectData;
    private List<IRule> testRules;
    private IRule mockRule;

    @BeforeEach
    void setUp() {
        testUser = new User("74255", "testUserId");
        testTenantId = "74255";

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn("TestObj");
        when(mockObjectDescribe.getTenantId()).thenReturn(testTenantId);

        testObjectData = new ObjectData();
        testObjectData.setId("testDataId");
        testObjectData.put("name__c", "Test Name");

        mockRule = mock(IRule.class);
        when(mockRule.getId()).thenReturn("ruleId1");
        when(mockRule.getName()).thenReturn("Test Rule");
        when(mockRule.getExpression()).thenReturn("name__c != null");
        testRules = Lists.newArrayList(mockRule);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则的正常场景，验证数据通过验证
     */
    @Test
    @DisplayName("正常场景 - 验证规则成功（数据通过验证）")
    void testValidateRule_Success_DataPassesValidation() throws MetadataServiceException {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // 准备测试数据
            when(ruleService.findRulesByDescribe(mockObjectDescribe)).thenReturn(testRules);
            when(switchCacheService.isOpen(anyString(), anyString(), anyString())).thenReturn(true);

            RuleCalculateResult calculateResult = new RuleCalculateResult();
            calculateResult.setSuccess(true);
            when(expressionCalculateLogicService.calculateRule(eq(mockRule), eq(testObjectData), eq(mockObjectDescribe)))
                    .thenReturn(calculateResult);

            // 执行被测试方法
            RuleResult result = validateRuleService.validateRule(testUser, testObjectData, mockObjectDescribe, "CREATE");

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(ruleService).findRulesByDescribe(mockObjectDescribe);
            verify(expressionCalculateLogicService).calculateRule(eq(mockRule), eq(testObjectData), eq(mockObjectDescribe));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则时，数据未通过验证的场景
     */
    @Test
    @DisplayName("正常场景 - 验证规则失败（数据未通过验证）")
    void testValidateRule_Failure_DataFailsValidation() throws MetadataServiceException {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // 准备测试数据
            when(ruleService.findRulesByDescribe(mockObjectDescribe)).thenReturn(testRules);
            when(switchCacheService.isOpen(anyString(), anyString(), anyString())).thenReturn(true);

            RuleCalculateResult calculateResult = new RuleCalculateResult();
            calculateResult.setSuccess(false);
            calculateResult.setErrorMessage("验证失败");
            when(expressionCalculateLogicService.calculateRule(eq(mockRule), eq(testObjectData), eq(mockObjectDescribe)))
                    .thenReturn(calculateResult);

            mockedI18N.when(() -> I18N.text(I18NKey.VALIDATE_RULE_ERROR))
                    .thenReturn("验证规则错误");

            // 执行被测试方法
            RuleResult result = validateRuleService.validateRule(testUser, testObjectData, mockObjectDescribe, "CREATE");

            // 验证结果
            assertNotNull(result);
            assertFalse(result.isSuccess());
            assertNotNull(result.getErrorMessage());
            verify(ruleService).findRulesByDescribe(mockObjectDescribe);
            verify(expressionCalculateLogicService).calculateRule(eq(mockRule), eq(testObjectData), eq(mockObjectDescribe));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则时，没有找到规则的场景
     */
    @Test
    @DisplayName("正常场景 - 验证规则时没有找到规则直接通过")
    void testValidateRule_NoRulesFound_PassesValidation() throws MetadataServiceException {
        // 准备测试数据
        when(ruleService.findRulesByDescribe(mockObjectDescribe)).thenReturn(Lists.newArrayList());

        // 执行被测试方法
        RuleResult result = validateRuleService.validateRule(testUser, testObjectData, mockObjectDescribe, "CREATE");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(ruleService).findRulesByDescribe(mockObjectDescribe);
        verify(expressionCalculateLogicService, never()).calculateRule(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则时，开关缓存关闭的场景
     */
    @Test
    @DisplayName("正常场景 - 验证规则时开关缓存关闭直接通过")
    void testValidateRule_SwitchCacheDisabled_PassesValidation() throws MetadataServiceException {
        // 准备测试数据
        when(ruleService.findRulesByDescribe(mockObjectDescribe)).thenReturn(testRules);
        when(switchCacheService.isOpen(anyString(), anyString(), anyString())).thenReturn(false);

        // 执行被测试方法
        RuleResult result = validateRuleService.validateRule(testUser, testObjectData, mockObjectDescribe, "CREATE");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(ruleService).findRulesByDescribe(mockObjectDescribe);
        verify(expressionCalculateLogicService, never()).calculateRule(any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试验证规则时，表达式计算抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 验证规则时表达式计算抛出MetaDataBusinessException")
    void testValidateRuleThrowsMetaDataBusinessException_ExpressionCalculationFails() throws MetadataServiceException {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // 准备测试数据
            when(ruleService.findRulesByDescribe(mockObjectDescribe)).thenReturn(testRules);
            when(switchCacheService.isOpen(anyString(), anyString(), anyString())).thenReturn(true);

            when(expressionCalculateLogicService.calculateRule(eq(mockRule), eq(testObjectData), eq(mockObjectDescribe)))
                    .thenThrow(new RuntimeException("计算错误"));

            mockedI18N.when(() -> I18N.text(I18NKey.VALIDATE_RULE_ERROR))
                    .thenReturn("验证规则错误");

            // 执行并验证异常
            MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
                validateRuleService.validateRule(testUser, testObjectData, mockObjectDescribe, "CREATE");
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(ruleService).findRulesByDescribe(mockObjectDescribe);
            verify(expressionCalculateLogicService).calculateRule(eq(mockRule), eq(testObjectData), eq(mockObjectDescribe));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建验证规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 创建验证规则成功")
    void testCreateValidateRule_Success() throws MetadataServiceException {
        try (MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class)) {
            // 准备测试数据
            Rule newRule = new Rule();
            newRule.setName("New Rule");
            newRule.setExpression("name__c != null");

            when(licenseService.checkLicense(testUser, "VALIDATE_RULE")).thenReturn(true);

            Rule createdRule = new Rule();
            createdRule.setId("newRuleId");
            createdRule.setName("New Rule");
            when(ruleService.create(newRule)).thenReturn(createdRule);

            // 执行被测试方法
            IRule result = validateRuleService.createValidateRule(testUser, newRule);

            // 验证结果
            assertNotNull(result);
            assertEquals("newRuleId", result.getId());
            verify(licenseService).checkLicense(testUser, "VALIDATE_RULE");
            verify(ruleService).create(newRule);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建验证规则时，许可证检查失败的场景
     */
    @Test
    @DisplayName("异常场景 - 创建验证规则时许可证检查失败抛出ValidateException")
    void testCreateValidateRuleThrowsValidateException_LicenseCheckFails() {
        try (MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class)) {
            // 准备测试数据
            Rule newRule = new Rule();
            newRule.setName("New Rule");

            when(licenseService.checkLicense(testUser, "VALIDATE_RULE")).thenReturn(false);
            mockedI18NExt.when(() -> I18NExt.text(I18NKey.LICENSE_VALIDATE_RULE_LIMIT))
                    .thenReturn("验证规则许可证限制");

            // 执行并验证异常
            ValidateException exception = assertThrows(ValidateException.class, () -> {
                validateRuleService.createValidateRule(testUser, newRule);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(licenseService).checkLicense(testUser, "VALIDATE_RULE");
            verify(ruleService, never()).create(any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新验证规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 更新验证规则成功")
    void testUpdateValidateRule_Success() throws MetadataServiceException {
        // 准备测试数据
        Rule ruleToUpdate = new Rule();
        ruleToUpdate.setId("ruleToUpdate");
        ruleToUpdate.setName("Updated Rule");

        Rule updatedRule = new Rule();
        updatedRule.setId("ruleToUpdate");
        updatedRule.setName("Updated Rule");
        when(ruleService.update(ruleToUpdate)).thenReturn(updatedRule);

        // 执行被测试方法
        IRule result = validateRuleService.updateValidateRule(testUser, ruleToUpdate);

        // 验证结果
        assertNotNull(result);
        assertEquals("ruleToUpdate", result.getId());
        verify(ruleService).update(ruleToUpdate);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新验证规则时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 更新验证规则时服务抛出MetaDataBusinessException")
    void testUpdateValidateRuleThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        Rule ruleToUpdate = new Rule();
        ruleToUpdate.setId("ruleToUpdate");

        MetadataServiceException serviceException = new MetadataServiceException("更新失败");
        when(ruleService.update(ruleToUpdate)).thenThrow(serviceException);

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            validateRuleService.updateValidateRule(testUser, ruleToUpdate);
        });

        // 验证异常信息
        assertNotNull(exception);
        verify(ruleService).update(ruleToUpdate);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除验证规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除验证规则成功")
    void testDeleteValidateRule_Success() throws MetadataServiceException {
        // 准备测试数据
        String ruleId = "ruleToDelete";

        // 执行被测试方法
        validateRuleService.deleteValidateRule(testUser, ruleId);

        // 验证结果
        verify(ruleService).delete(ruleId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除验证规则时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 删除验证规则时服务抛出MetaDataBusinessException")
    void testDeleteValidateRuleThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        String ruleId = "ruleToDelete";
        MetadataServiceException serviceException = new MetadataServiceException("删除失败");
        doThrow(serviceException).when(ruleService).delete(ruleId);

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            validateRuleService.deleteValidateRule(testUser, ruleId);
        });

        // 验证异常信息
        assertNotNull(exception);
        verify(ruleService).delete(ruleId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找验证规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找验证规则成功")
    void testFindValidateRules_Success() throws MetadataServiceException {
        // 准备测试数据
        when(ruleService.findRulesByDescribe(mockObjectDescribe)).thenReturn(testRules);

        // 执行被测试方法
        List<IRule> result = validateRuleService.findValidateRules(testUser, mockObjectDescribe);

        // 验证结果
        assertNotNull(result);
        assertEquals(testRules, result);
        verify(ruleService).findRulesByDescribe(mockObjectDescribe);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找验证规则时，没有找到规则的场景
     */
    @Test
    @DisplayName("正常场景 - 查找验证规则时没有找到规则返回空列表")
    void testFindValidateRules_NoRulesFound() throws MetadataServiceException {
        // 准备测试数据
        when(ruleService.findRulesByDescribe(mockObjectDescribe)).thenReturn(Lists.newArrayList());

        // 执行被测试方法
        List<IRule> result = validateRuleService.findValidateRules(testUser, mockObjectDescribe);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(ruleService).findRulesByDescribe(mockObjectDescribe);
    }
}
