package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.Rule;
import com.facishare.paas.metadata.service.impl.RuleServiceImpl;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ValidateRuleServiceImplTest {

    @Mock
    private RuleServiceImpl ruleService;

    @Mock
    private MetaDataService metaDataService;

    @Mock
    private ExpressionCalculateLogicService expressionCalculateLogicService;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private SwitchCacheService switchCacheService;

    @Mock
    private I18nSettingService i18nSettingService;

    @Mock
    private LicenseService licenseService;

    @InjectMocks
    private ValidateRuleServiceImpl validateRuleService;

    private User testUser;
    private IRule testRule;
    private IObjectDescribe mockObjectDescribe;
    private String testTenantId;
    private String testDescribeApiName;
    private String testRuleApiName;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setTenantId("74255");
        testUser.setUserId("testUserId");

        testTenantId = "74255";
        testDescribeApiName = "TestObj";
        testRuleApiName = "testRule";

        testRule = new Rule();
        testRule.setTenantId(testTenantId);
        testRule.setDescribeApiName(testDescribeApiName);
        testRule.setApiName(testRuleApiName);
        testRule.setRuleName("测试验证规则");
        testRule.setMessage("验证失败消息");

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn(testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建验证规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 创建验证规则成功")
    void testCreate_Success() throws MetadataServiceException {
        // 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName))
                .thenReturn(mockObjectDescribe);
        when(ruleService.create(testRule)).thenReturn(testRule);

        // 执行被测试方法
        RuleResult result = validateRuleService.create(testRule);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(describeLogicService).findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName);
        verify(ruleService).create(testRule);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建验证规则时，对象描述不存在的场景
     */
    @Test
    @DisplayName("异常场景 - 创建验证规则时对象描述不存在抛出MetaDataBusinessException")
    void testCreateThrowsMetaDataBusinessException_ObjectDescribeNotFound() throws MetadataServiceException {
        try (MockedStatic<I18N> mockedI18N = mockStatic(I18N.class)) {
            // 准备测试数据
            when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName))
                    .thenReturn(null);
            mockedI18N.when(() -> I18N.text(I18NKey.OBJECT_DESC_UNEXIST))
                    .thenReturn("对象描述不存在");

            // 执行并验证异常
            MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
                validateRuleService.create(testRule);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(describeLogicService).findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName);
            verify(ruleService, never()).create(any(IRule.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建验证规则时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 创建验证规则时服务抛出MetadataServiceException")
    void testCreateThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName))
                .thenReturn(mockObjectDescribe);
        MetadataServiceException serviceException = new MetadataServiceException("Service error");
        when(ruleService.create(testRule)).thenThrow(serviceException);

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            validateRuleService.create(testRule);
        });

        // 验证异常信息
        assertNotNull(exception);
        assertEquals("Service error", exception.getMessage());
        verify(ruleService).create(testRule);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新验证规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 更新验证规则成功")
    void testUpdate_Success() throws MetadataServiceException {
        // 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName))
                .thenReturn(mockObjectDescribe);
        when(ruleService.update(testRule)).thenReturn(testRule);

        // 执行被测试方法
        RuleResult result = validateRuleService.update(testUser, testRule);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(testUser.getUserId(), testRule.getLastModifiedBy());
        assertEquals(testUser.getTenantId(), testRule.getTenantId());
        verify(ruleService).update(testRule);
        verify(i18nSettingService).syncTransValue(anyMap(), anyString(), eq(testUser.getTenantId()));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除验证规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除验证规则成功")
    void testDelete_Success() throws MetadataServiceException {
        // 准备测试数据
        when(ruleService.findByApiName(testTenantId, testDescribeApiName, testRuleApiName))
                .thenReturn(testRule);
        when(ruleService.delete(testRule)).thenReturn(testRule);

        // 执行被测试方法
        RuleResult result = validateRuleService.delete(testDescribeApiName, testTenantId, testRuleApiName);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(ruleService).findByApiName(testTenantId, testDescribeApiName, testRuleApiName);
        verify(ruleService).delete(testRule);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除验证规则时，规则不存在的场景
     */
    @Test
    @DisplayName("异常场景 - 删除验证规则时规则不存在抛出MetaDataBusinessException")
    void testDeleteThrowsMetaDataBusinessException_RuleNotFound() throws MetadataServiceException {
        try (MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class)) {
            // 准备测试数据
            when(ruleService.findByApiName(testTenantId, testDescribeApiName, testRuleApiName))
                    .thenReturn(null);
            mockedI18NExt.when(() -> I18NExt.text(I18NKey.VALIDATION_RULE_NOT_EXIST_OR_DELETED))
                    .thenReturn("验证规则不存在或已删除");

            // 执行并验证异常
            MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
                validateRuleService.delete(testDescribeApiName, testTenantId, testRuleApiName);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(ruleService).findByApiName(testTenantId, testDescribeApiName, testRuleApiName);
            verify(ruleService, never()).delete(any(IRule.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据对象描述API名称删除验证规则的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据对象描述API名称删除验证规则成功")
    void testDeleteByDescribeApiName_Success() throws MetadataServiceException {
        // 执行被测试方法
        validateRuleService.deleteByDescribeApiName(testTenantId, testDescribeApiName);

        // 验证结果
        verify(ruleService).deleteByDescribeApiName(testTenantId, testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据对象描述API名称删除验证规则时，服务抛出异常的场景
     */
    @Test
    @DisplayName("正常场景 - 根据对象描述API名称删除验证规则时服务异常不抛出")
    void testDeleteByDescribeApiName_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        MetadataServiceException serviceException = new MetadataServiceException("Service error");
        doThrow(serviceException).when(ruleService).deleteByDescribeApiName(testTenantId, testDescribeApiName);

        // 执行被测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> {
            validateRuleService.deleteByDescribeApiName(testTenantId, testDescribeApiName);
        });

        // 验证结果
        verify(ruleService).deleteByDescribeApiName(testTenantId, testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找验证规则列表的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找验证规则列表成功")
    void testFindRuleList_Success() throws MetadataServiceException {
        // 准备测试数据
        List<IRule> testRuleList = Lists.newArrayList(testRule);
        when(ruleService.findByDescribeApiName(testTenantId, testDescribeApiName))
                .thenReturn(testRuleList);

        // 执行被测试方法
        List<IRule> result = validateRuleService.findRuleList(testTenantId, testDescribeApiName);

        // 验证结果
        assertNotNull(result);
        assertEquals(testRuleList.size(), result.size());
        verify(ruleService).findByDescribeApiName(testTenantId, testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找验证规则列表时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 查找验证规则列表时服务抛出MetaDataBusinessException")
    void testFindRuleListThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        MetadataServiceException serviceException = new MetadataServiceException("Service error");
        when(ruleService.findByDescribeApiName(testTenantId, testDescribeApiName))
                .thenThrow(serviceException);

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            validateRuleService.findRuleList(testTenantId, testDescribeApiName);
        });

        // 验证异常信息
        assertNotNull(exception);
        assertEquals("Service error", exception.getMessage());
        verify(ruleService).findByDescribeApiName(testTenantId, testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带实时翻译参数的查找验证规则列表的正常场景
     */
    @Test
    @DisplayName("正常场景 - 带实时翻译参数查找验证规则列表成功")
    void testFindRuleListWithOnTime_Success() throws MetadataServiceException {
        // 准备测试数据
        List<IRule> testRuleList = Lists.newArrayList(testRule);
        when(ruleService.findByDescribeApiName(testTenantId, testDescribeApiName))
                .thenReturn(testRuleList);

        // 执行被测试方法
        List<IRule> result = validateRuleService.findRuleList(testTenantId, testDescribeApiName, true);

        // 验证结果
        assertNotNull(result);
        assertEquals(testRuleList.size(), result.size());
        verify(ruleService).findByDescribeApiName(testTenantId, testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据规则名称查找验证规则列表的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据规则名称查找验证规则列表成功")
    void testFindRuleListByRuleName_Success() throws MetadataServiceException {
        // 准备测试数据
        String ruleName = "测试";
        List<IRule> testRuleList = Lists.newArrayList(testRule);
        when(ruleService.findByDescribeApiName(testTenantId, testDescribeApiName))
                .thenReturn(testRuleList);

        // 执行被测试方法
        RuleResult result = validateRuleService.findRuleList(testDescribeApiName, testTenantId, ruleName);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getRuleList());
        verify(ruleService).findByDescribeApiName(testTenantId, testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据规则名称查找验证规则列表时，规则列表为空的场景
     */
    @Test
    @DisplayName("正常场景 - 根据规则名称查找验证规则列表为空时返回空结果")
    void testFindRuleListByRuleName_EmptyRuleList() throws MetadataServiceException {
        // 准备测试数据
        String ruleName = "测试";
        List<IRule> emptyRuleList = Lists.newArrayList();
        when(ruleService.findByDescribeApiName(testTenantId, testDescribeApiName))
                .thenReturn(emptyRuleList);

        // 执行被测试方法
        RuleResult result = validateRuleService.findRuleList(testDescribeApiName, testTenantId, ruleName);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNull(result.getRuleList());
        verify(ruleService).findByDescribeApiName(testTenantId, testDescribeApiName);
    }
}
