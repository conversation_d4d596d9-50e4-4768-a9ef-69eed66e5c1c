package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.config.ObjectControlLevelLogicService;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.dto.scene.SceneDTO;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.initscene.SceneInitManager;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.service.IFieldListConfigService;
import com.facishare.paas.metadata.api.service.ISearchTemplateService;
import com.facishare.paas.metadata.common.MetadataContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplate;
import com.facishare.paas.metadata.support.GrayHelper;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SceneLogicServiceImplTest {

    @Mock
    private ISearchTemplateService searchTemplateService;

    @Mock
    private SceneInitManager sceneInitManager;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private LayoutLogicService layoutLogicService;

    @Mock
    private LogService logService;

    @Mock
    private GrayHelper grayHelper;

    @Mock
    private OptionalFeaturesService optionalFeaturesService;

    @Mock
    private IFieldListConfigService fieldListConfigService;

    @Mock
    private AppDefaultRocketMQProducer wipeCustomListSettingsProducer;

    @Mock
    private ObjectControlLevelLogicService objectControlLevelLogicService;

    @InjectMocks
    private SceneLogicServiceImpl sceneLogicService;

    private User testUser;
    private String testDescribeApiName;
    private String testSceneApiName;
    private String testExtendAttribute;
    private IObjectDescribe mockObjectDescribe;
    private List<ISearchTemplate> testSearchTemplates;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setTenantId("74255");
        testUser.setUserId("testUserId");

        testDescribeApiName = "TestObj";
        testSceneApiName = "testScene";
        testExtendAttribute = "testAttribute";

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn(testDescribeApiName);

        ISearchTemplate mockSearchTemplate = mock(ISearchTemplate.class);
        when(mockSearchTemplate.getApiName()).thenReturn(testSceneApiName);
        when(mockSearchTemplate.getIsAvailable()).thenReturn(true);
        testSearchTemplates = Lists.newArrayList(mockSearchTemplate);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找场景的正常场景，验证场景查询功能
     */
    @Test
    @DisplayName("正常场景 - 查找场景成功")
    void testFindScenes_Success() throws MetadataServiceException {
        try (MockedStatic<SearchTemplateExt> mockedSearchTemplateExt = mockStatic(SearchTemplateExt.class)) {
            // 准备测试数据
            when(describeLogicService.findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName))
                    .thenReturn(mockObjectDescribe);

            when(searchTemplateService.findForTenantAndAttributeList(
                    eq(testUser.getTenantId()), eq(testDescribeApiName), isNull(), isNull(), 
                    anyList(), any(MetadataContext.class)))
                    .thenReturn(testSearchTemplates);

            mockedSearchTemplateExt.when(() -> SearchTemplateExt.searchTemplatesOrderByTypeAndCreateTime(testSearchTemplates))
                    .thenReturn(testSearchTemplates);

            // 执行被测试方法
            List<IScene> result = sceneLogicService.findScenes(testDescribeApiName, testUser, testExtendAttribute);

            // 验证结果
            assertNotNull(result);
            verify(describeLogicService).findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName);
            verify(searchTemplateService).findForTenantAndAttributeList(
                    eq(testUser.getTenantId()), eq(testDescribeApiName), isNull(), isNull(), 
                    anyList(), any(MetadataContext.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找场景时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 查找场景时服务抛出MetaDataBusinessException")
    void testFindScenesThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName))
                .thenReturn(mockObjectDescribe);

        MetadataServiceException serviceException = new MetadataServiceException("Service error");
        when(searchTemplateService.findForTenantAndAttributeList(
                eq(testUser.getTenantId()), eq(testDescribeApiName), isNull(), isNull(), 
                anyList(), any(MetadataContext.class)))
                .thenThrow(serviceException);

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            sceneLogicService.findScenes(testDescribeApiName, testUser, testExtendAttribute);
        });

        // 验证异常信息
        assertNotNull(exception);
        verify(searchTemplateService).findForTenantAndAttributeList(
                eq(testUser.getTenantId()), eq(testDescribeApiName), isNull(), isNull(), 
                anyList(), any(MetadataContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试带活跃状态参数的查找场景的正常场景
     */
    @Test
    @DisplayName("正常场景 - 带活跃状态参数查找场景成功")
    void testFindScenesWithIsActive_Success() throws MetadataServiceException {
        try (MockedStatic<SearchTemplateExt> mockedSearchTemplateExt = mockStatic(SearchTemplateExt.class)) {
            // 准备测试数据
            when(describeLogicService.findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName))
                    .thenReturn(mockObjectDescribe);

            when(searchTemplateService.findForTenantAndAttributeList(
                    eq(testUser.getTenantId()), eq(testDescribeApiName), isNull(), isNull(), 
                    anyList(), any(MetadataContext.class)))
                    .thenReturn(testSearchTemplates);

            mockedSearchTemplateExt.when(() -> SearchTemplateExt.searchTemplatesOrderByTypeAndCreateTime(testSearchTemplates))
                    .thenReturn(testSearchTemplates);

            // 执行被测试方法
            List<IScene> result = sceneLogicService.findScenes(testDescribeApiName, testUser, testExtendAttribute, true);

            // 验证结果
            assertNotNull(result);
            verify(describeLogicService).findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName);
            verify(searchTemplateService).findForTenantAndAttributeList(
                    eq(testUser.getTenantId()), eq(testDescribeApiName), isNull(), isNull(), 
                    anyList(), any(MetadataContext.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据API名称查找场景的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据API名称查找场景成功")
    void testFindSceneByApiName_Success() throws MetadataServiceException {
        try (MockedStatic<SceneDTO> mockedSceneDTO = mockStatic(SceneDTO.class)) {
            // 准备测试数据
            ISearchTemplate mockSearchTemplate = mock(ISearchTemplate.class);
            when(searchTemplateService.findForTenantByApiName(
                    testUser.getTenantId(), testDescribeApiName, testSceneApiName, 
                    testExtendAttribute, any(MetadataContext.class)))
                    .thenReturn(mockSearchTemplate);

            when(describeLogicService.findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName))
                    .thenReturn(mockObjectDescribe);

            SceneDTO mockSceneDTO = mock(SceneDTO.class);
            mockedSceneDTO.when(() -> SceneDTO.fromSearchTemplate(mockSearchTemplate, mockObjectDescribe))
                    .thenReturn(mockSceneDTO);

            // 执行被测试方法
            IScene result = sceneLogicService.findSceneByApiName(
                    testDescribeApiName, testSceneApiName, testUser.getTenantId(), testExtendAttribute);

            // 验证结果
            assertNotNull(result);
            assertEquals(mockSceneDTO, result);
            verify(searchTemplateService).findForTenantByApiName(
                    testUser.getTenantId(), testDescribeApiName, testSceneApiName, 
                    testExtendAttribute, any(MetadataContext.class));
            verify(describeLogicService).findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据API名称查找场景时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 根据API名称查找场景时服务抛出MetaDataBusinessException")
    void testFindSceneByApiNameThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        MetadataServiceException serviceException = new MetadataServiceException("Service error");
        when(searchTemplateService.findForTenantByApiName(
                testUser.getTenantId(), testDescribeApiName, testSceneApiName, 
                testExtendAttribute, any(MetadataContext.class)))
                .thenThrow(serviceException);

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            sceneLogicService.findSceneByApiName(
                    testDescribeApiName, testSceneApiName, testUser.getTenantId(), testExtendAttribute);
        });

        // 验证异常信息
        assertNotNull(exception);
        verify(searchTemplateService).findForTenantByApiName(
                testUser.getTenantId(), testDescribeApiName, testSceneApiName, 
                testExtendAttribute, any(MetadataContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据用户和API名称查找场景的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据用户和API名称查找场景成功")
    void testFindSceneByApiNameWithUser_Success() throws MetadataServiceException {
        try (MockedStatic<SearchTemplateExt> mockedSearchTemplateExt = mockStatic(SearchTemplateExt.class);
             MockedStatic<SceneDTO> mockedSceneDTO = mockStatic(SceneDTO.class)) {

            // 准备测试数据
            ISearchTemplate mockSearchTemplate = mock(ISearchTemplate.class);
            when(searchTemplateService.findForTenantByApiName(
                    testUser.getTenantId(), testDescribeApiName, testSceneApiName, 
                    testExtendAttribute, any(MetadataContext.class)))
                    .thenReturn(mockSearchTemplate);

            when(describeLogicService.findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName))
                    .thenReturn(mockObjectDescribe);

            SearchTemplateExt mockSearchTemplateExt = mock(SearchTemplateExt.class);
            mockedSearchTemplateExt.when(() -> SearchTemplateExt.of(mockSearchTemplate))
                    .thenReturn(mockSearchTemplateExt);
            when(mockSearchTemplateExt.isDefaultScene()).thenReturn(false);

            OptionalFeaturesSwitchDTO mockOptionalFeaturesSwitch = mock(OptionalFeaturesSwitchDTO.class);
            when(mockOptionalFeaturesSwitch.getIsRelatedTeamEnabled()).thenReturn(true);
            when(optionalFeaturesService.findOptionalFeaturesSwitch(testUser.getTenantId(), mockObjectDescribe))
                    .thenReturn(mockOptionalFeaturesSwitch);

            SceneDTO mockSceneDTO = mock(SceneDTO.class);
            mockedSceneDTO.when(() -> SceneDTO.fromSearchTemplate(eq(mockSearchTemplate), eq(mockObjectDescribe), anyString()))
                    .thenReturn(mockSceneDTO);

            // 执行被测试方法
            IScene result = sceneLogicService.findSceneByApiName(
                    testUser, testDescribeApiName, testSceneApiName, testExtendAttribute, null);

            // 验证结果
            assertNotNull(result);
            assertEquals(mockSceneDTO, result);
            verify(searchTemplateService).findForTenantByApiName(
                    testUser.getTenantId(), testDescribeApiName, testSceneApiName, 
                    testExtendAttribute, any(MetadataContext.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据用户和API名称查找场景时，搜索模板为null的场景
     */
    @Test
    @DisplayName("正常场景 - 根据用户和API名称查找场景时搜索模板为null返回null")
    void testFindSceneByApiNameWithUser_NullSearchTemplate() throws MetadataServiceException {
        // 准备测试数据
        when(searchTemplateService.findForTenantByApiName(
                testUser.getTenantId(), testDescribeApiName, testSceneApiName, 
                testExtendAttribute, any(MetadataContext.class)))
                .thenReturn(null);

        when(searchTemplateService.getSysSearchTemplateByApiName(
                testSceneApiName, any(MetadataContext.class), testDescribeApiName, testExtendAttribute))
                .thenReturn(null);

        // 执行被测试方法
        IScene result = sceneLogicService.findSceneByApiName(
                testUser, testDescribeApiName, testSceneApiName, testExtendAttribute, null);

        // 验证结果
        assertNull(result);
        verify(searchTemplateService).findForTenantByApiName(
                testUser.getTenantId(), testDescribeApiName, testSceneApiName, 
                testExtendAttribute, any(MetadataContext.class));
        verify(searchTemplateService).getSysSearchTemplateByApiName(
                testSceneApiName, any(MetadataContext.class), testDescribeApiName, testExtendAttribute);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据用户和API名称查找场景的重载方法
     */
    @Test
    @DisplayName("正常场景 - 根据用户和API名称查找场景重载方法成功")
    void testFindSceneByApiNameOverload_Success() throws MetadataServiceException {
        try (MockedStatic<SearchTemplateExt> mockedSearchTemplateExt = mockStatic(SearchTemplateExt.class);
             MockedStatic<SceneDTO> mockedSceneDTO = mockStatic(SceneDTO.class)) {

            // 准备测试数据
            ISearchTemplate mockSearchTemplate = mock(ISearchTemplate.class);
            when(searchTemplateService.findForTenantByApiName(
                    testUser.getTenantId(), testDescribeApiName, testSceneApiName, 
                    testExtendAttribute, any(MetadataContext.class)))
                    .thenReturn(mockSearchTemplate);

            when(describeLogicService.findObjectWithoutCopyIfGray(testUser.getTenantId(), testDescribeApiName))
                    .thenReturn(mockObjectDescribe);

            SearchTemplateExt mockSearchTemplateExt = mock(SearchTemplateExt.class);
            mockedSearchTemplateExt.when(() -> SearchTemplateExt.of(mockSearchTemplate))
                    .thenReturn(mockSearchTemplateExt);
            when(mockSearchTemplateExt.isDefaultScene()).thenReturn(false);

            OptionalFeaturesSwitchDTO mockOptionalFeaturesSwitch = mock(OptionalFeaturesSwitchDTO.class);
            when(mockOptionalFeaturesSwitch.getIsRelatedTeamEnabled()).thenReturn(true);
            when(optionalFeaturesService.findOptionalFeaturesSwitch(testUser.getTenantId(), mockObjectDescribe))
                    .thenReturn(mockOptionalFeaturesSwitch);

            SceneDTO mockSceneDTO = mock(SceneDTO.class);
            mockedSceneDTO.when(() -> SceneDTO.fromSearchTemplate(eq(mockSearchTemplate), eq(mockObjectDescribe), anyString()))
                    .thenReturn(mockSceneDTO);

            // 执行被测试方法
            IScene result = sceneLogicService.findSceneByApiName(testDescribeApiName, testSceneApiName, testUser, testExtendAttribute);

            // 验证结果
            assertNotNull(result);
            assertEquals(mockSceneDTO, result);
            verify(searchTemplateService).findForTenantByApiName(
                    testUser.getTenantId(), testDescribeApiName, testSceneApiName, 
                    testExtendAttribute, any(MetadataContext.class));
        }
    }
}
