package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.config.IUdefConfig;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.dto.scene.ISystemScene;
import com.facishare.paas.appframework.metadata.dto.scene.ITenantScene;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.service.ISearchTemplateService;
import com.facishare.paas.metadata.common.MetadataContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplate;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SceneLogicServiceImplTest {

    @Mock
    private ISearchTemplateService searchTemplateService;

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private LogService logService;

    @Mock
    private ObjectControlLevelLogicService objectControlLevelLogicService;

    @InjectMocks
    private SceneLogicServiceImpl sceneLogicService;

    private User testUser;
    private String testTenantId;
    private String testDescribeApiName;
    private String testSceneApiName;
    private String testExtendAttribute;
    private IObjectDescribe mockObjectDescribe;
    private ISearchTemplate mockSearchTemplate;
    private List<ISearchTemplate> testSearchTemplates;
    private ITenantScene mockTenantScene;
    private ISystemScene mockSystemScene;

    @BeforeEach
    void setUp() {
        testUser = new User("74255", "testUserId");
        testTenantId = "74255";
        testDescribeApiName = "TestObj";
        testSceneApiName = "testScene";
        testExtendAttribute = "list";

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn(testDescribeApiName);
        when(mockObjectDescribe.getTenantId()).thenReturn(testTenantId);

        mockSearchTemplate = mock(ISearchTemplate.class);
        when(mockSearchTemplate.getApiName()).thenReturn(testSceneApiName);
        when(mockSearchTemplate.getDescribeApiName()).thenReturn(testDescribeApiName);
        testSearchTemplates = Lists.newArrayList(mockSearchTemplate);

        mockTenantScene = mock(ITenantScene.class);
        when(mockTenantScene.getApiName()).thenReturn(testSceneApiName);
        when(mockTenantScene.toSearchTemplate()).thenReturn(mockSearchTemplate);

        mockSystemScene = mock(ISystemScene.class);
        when(mockSystemScene.getApiName()).thenReturn(testSceneApiName);
        when(mockSystemScene.getObjectDescribeApiName()).thenReturn(testDescribeApiName);
        when(mockSystemScene.toSearchTemplate()).thenReturn(mockSearchTemplate);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找场景的正常场景，验证基本的场景查询功能
     */
    @Test
    @DisplayName("正常场景 - 查找场景成功")
    void testFindScenes_Success() throws MetadataServiceException {
        // 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName))
                .thenReturn(mockObjectDescribe);
        when(searchTemplateService.findForTenant(eq(testTenantId), eq(testDescribeApiName), 
                eq(testExtendAttribute), any(MetadataContext.class)))
                .thenReturn(testSearchTemplates);

        // 执行被测试方法
        List<IScene> result = sceneLogicService.findScenes(testDescribeApiName, testUser, testExtendAttribute);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(describeLogicService).findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName);
        verify(searchTemplateService).findForTenant(eq(testTenantId), eq(testDescribeApiName), 
                eq(testExtendAttribute), any(MetadataContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找场景时，服务抛出异常的场景
     */
    @Test
    @DisplayName("异常场景 - 查找场景时服务抛出MetaDataBusinessException")
    void testFindScenesThrowsMetaDataBusinessException_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName))
                .thenReturn(mockObjectDescribe);
        when(searchTemplateService.findForTenant(eq(testTenantId), eq(testDescribeApiName), 
                eq(testExtendAttribute), any(MetadataContext.class)))
                .thenThrow(new MetadataServiceException("Service error"));

        // 执行并验证异常
        MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
            sceneLogicService.findScenes(testDescribeApiName, testUser, testExtendAttribute);
        });

        // 验证异常信息
        assertNotNull(exception);
        verify(describeLogicService).findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据API名称查找场景的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据API名称查找场景成功")
    void testFindSceneByApiName_Success() throws MetadataServiceException {
        // 准备测试数据
        when(searchTemplateService.findForTenantByApiName(eq(testTenantId), eq(testDescribeApiName), 
                eq(testSceneApiName), eq(testExtendAttribute), any(MetadataContext.class)))
                .thenReturn(mockSearchTemplate);

        // 执行被测试方法
        IScene result = sceneLogicService.findSceneByApiName(testDescribeApiName, testSceneApiName, 
                testTenantId, testExtendAttribute);

        // 验证结果
        assertNotNull(result);
        verify(searchTemplateService).findForTenantByApiName(eq(testTenantId), eq(testDescribeApiName), 
                eq(testSceneApiName), eq(testExtendAttribute), any(MetadataContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据API名称查找场景时，场景不存在的场景
     */
    @Test
    @DisplayName("正常场景 - 根据API名称查找场景时场景不存在返回null")
    void testFindSceneByApiName_SceneNotExists() throws MetadataServiceException {
        // 准备测试数据
        when(searchTemplateService.findForTenantByApiName(eq(testTenantId), eq(testDescribeApiName), 
                eq(testSceneApiName), eq(testExtendAttribute), any(MetadataContext.class)))
                .thenReturn(null);

        // 执行被测试方法
        IScene result = sceneLogicService.findSceneByApiName(testDescribeApiName, testSceneApiName, 
                testTenantId, testExtendAttribute);

        // 验证结果
        assertNull(result);
        verify(searchTemplateService).findForTenantByApiName(eq(testTenantId), eq(testDescribeApiName), 
                eq(testSceneApiName), eq(testExtendAttribute), any(MetadataContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建租户场景的正常场景
     */
    @Test
    @DisplayName("正常场景 - 创建租户场景成功")
    void testCreateTenantScene_Success() throws MetadataServiceException {
        // 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName))
                .thenReturn(mockObjectDescribe);
        when(mockTenantScene.toSearchTemplate(mockObjectDescribe)).thenReturn(mockSearchTemplate);
        when(searchTemplateService.createForTenant(eq(mockSearchTemplate), any(MetadataContext.class)))
                .thenReturn(mockSearchTemplate);

        // 执行被测试方法
        ITenantScene result = sceneLogicService.createTenantScene(mockTenantScene, testDescribeApiName, 
                testExtendAttribute, testUser);

        // 验证结果
        assertNotNull(result);
        verify(describeLogicService).findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName);
        verify(searchTemplateService).createForTenant(eq(mockSearchTemplate), any(MetadataContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新租户场景的正常场景
     */
    @Test
    @DisplayName("正常场景 - 更新租户场景成功")
    void testUpdateTenantScene_Success() throws MetadataServiceException {
        // 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName))
                .thenReturn(mockObjectDescribe);
        when(mockTenantScene.toSearchTemplate(mockObjectDescribe)).thenReturn(mockSearchTemplate);
        when(searchTemplateService.updateForTenant(eq(mockSearchTemplate), any(MetadataContext.class)))
                .thenReturn(mockSearchTemplate);

        // 执行被测试方法
        ITenantScene result = sceneLogicService.updateTenantScene(mockTenantScene, testDescribeApiName, testUser);

        // 验证结果
        assertNotNull(result);
        verify(describeLogicService).findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName);
        verify(searchTemplateService).updateForTenant(eq(mockSearchTemplate), any(MetadataContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新系统场景的正常场景
     */
    @Test
    @DisplayName("正常场景 - 更新系统场景成功")
    void testUpdateSystemScene_Success() throws MetadataServiceException {
        // 准备测试数据
        when(mockSystemScene.getExtendAttribute()).thenReturn(testExtendAttribute);
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName))
                .thenReturn(mockObjectDescribe);
        when(searchTemplateService.updateForSystem(eq(mockSearchTemplate), any(MetadataContext.class)))
                .thenReturn(mockSearchTemplate);

        // 执行被测试方法
        ISystemScene result = sceneLogicService.updateSystemScene(mockSystemScene, testUser);

        // 验证结果
        assertNotNull(result);
        verify(describeLogicService).findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName);
        verify(searchTemplateService).updateForSystem(eq(mockSearchTemplate), any(MetadataContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除租户场景的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除租户场景成功")
    void testDeleteTenantScene_Success() throws MetadataServiceException {
        // 准备测试数据
        when(searchTemplateService.findForTenantByApiName(eq(testTenantId), eq(testDescribeApiName), 
                eq(testSceneApiName), eq(testExtendAttribute), any(MetadataContext.class)))
                .thenReturn(mockSearchTemplate);
        when(mockSearchTemplate.getCreatedBy()).thenReturn(testUser.getUserId()); // 可删除的场景

        // 执行被测试方法
        sceneLogicService.deleteTenantScene(testDescribeApiName, testSceneApiName, testExtendAttribute, testUser);

        // 验证结果
        verify(searchTemplateService).findForTenantByApiName(eq(testTenantId), eq(testDescribeApiName), 
                eq(testSceneApiName), eq(testExtendAttribute), any(MetadataContext.class));
        verify(searchTemplateService).deleteForTenant(eq(mockSearchTemplate), any(MetadataContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除租户场景时，场景不可删除的场景
     */
    @Test
    @DisplayName("异常场景 - 删除租户场景时场景不可删除抛出MetaDataBusinessException")
    void testDeleteTenantSceneThrowsMetaDataBusinessException_SceneNotDeletable() throws MetadataServiceException {
        try (MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class)) {
            // 准备测试数据
            when(searchTemplateService.findForTenantByApiName(eq(testTenantId), eq(testDescribeApiName), 
                    eq(testSceneApiName), eq(testExtendAttribute), any(MetadataContext.class)))
                    .thenReturn(mockSearchTemplate);
            when(mockSearchTemplate.getCreatedBy()).thenReturn("otherUserId"); // 不可删除的场景

            mockedI18NExt.when(() -> I18NExt.text(I18NKey.CURRENT_SCENARIO_CANNOT_BE_DELETED))
                    .thenReturn("当前场景不能被删除");

            // 执行并验证异常
            MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () -> {
                sceneLogicService.deleteTenantScene(testDescribeApiName, testSceneApiName, testExtendAttribute, testUser);
            });

            // 验证异常信息
            assertNotNull(exception);
            verify(searchTemplateService).findForTenantByApiName(eq(testTenantId), eq(testDescribeApiName), 
                    eq(testSceneApiName), eq(testExtendAttribute), any(MetadataContext.class));
            verify(searchTemplateService, never()).deleteForTenant(any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找基础场景的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找基础场景成功")
    void testFindBaseScenes_Success() {
        // 准备测试数据
        when(describeLogicService.findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName))
                .thenReturn(mockObjectDescribe);

        // 执行被测试方法
        List<IScene> result = sceneLogicService.findBaseScenes(testDescribeApiName, testExtendAttribute, testUser);

        // 验证结果
        assertNotNull(result);
        verify(describeLogicService).findObjectWithoutCopyIfGray(testTenantId, testDescribeApiName);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找场景配置的正常场景
     */
    @Test
    @DisplayName("正常场景 - 查找场景配置成功")
    void testFindSceneConfig_Success() {
        // 执行被测试方法
        IUdefConfig result = sceneLogicService.findSceneConfig(testDescribeApiName, testSceneApiName, testUser);

        // 验证结果
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据对象描述删除场景的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据对象描述删除场景成功")
    void testDeleteSceneByDescribe_Success() throws MetadataServiceException {
        // 执行被测试方法
        sceneLogicService.deleteSceneByDescribe(testDescribeApiName, testUser);

        // 验证结果
        verify(searchTemplateService).deleteSceneByDescribe(eq(testDescribeApiName), any(MetadataContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据对象描述删除场景时，服务抛出异常的场景
     */
    @Test
    @DisplayName("正常场景 - 根据对象描述删除场景时服务抛出异常记录日志")
    void testDeleteSceneByDescribe_ServiceException() throws MetadataServiceException {
        // 准备测试数据
        doThrow(new MetadataServiceException("Delete error"))
                .when(searchTemplateService).deleteSceneByDescribe(eq(testDescribeApiName), any(MetadataContext.class));

        // 执行被测试方法（不应该抛出异常）
        assertDoesNotThrow(() -> {
            sceneLogicService.deleteSceneByDescribe(testDescribeApiName, testUser);
        });

        // 验证结果
        verify(searchTemplateService).deleteSceneByDescribe(eq(testDescribeApiName), any(MetadataContext.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除自定义场景的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除自定义场景成功")
    void testDeleteCustomForApiNameAndUserId_Success() throws MetadataServiceException {
        // 准备测试数据
        when(testUser.isOutUser()).thenReturn(false);

        // 执行被测试方法
        sceneLogicService.deleteCustomForApiNameAndUserId(testDescribeApiName, testSceneApiName, testUser);

        // 验证结果
        verify(searchTemplateService).deleteCustomForApiNameOrUserId(testTenantId, testDescribeApiName, 
                testSceneApiName, testUser.getUserId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除外部用户自定义场景的正常场景
     */
    @Test
    @DisplayName("正常场景 - 删除外部用户自定义场景成功")
    void testDeleteCustomForApiNameAndUserId_OutUser_Success() throws MetadataServiceException {
        // 准备测试数据
        when(testUser.isOutUser()).thenReturn(true);
        when(testUser.getOutTenantId()).thenReturn("outTenantId");
        when(testUser.getOutUserId()).thenReturn("outUserId");

        // 执行被测试方法
        sceneLogicService.deleteCustomForApiNameAndUserId(testDescribeApiName, testSceneApiName, testUser);

        // 验证结果
        verify(searchTemplateService).deleteCustomForApiNameAndOutTenantUser(testTenantId, testDescribeApiName, 
                testSceneApiName, "outTenantId", "outUserId");
    }
}
