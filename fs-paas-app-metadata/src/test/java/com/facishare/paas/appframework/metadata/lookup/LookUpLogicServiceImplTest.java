package com.facishare.paas.appframework.metadata.lookup;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.FieldMapping;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LookUpLogicServiceImpl的单元测试 测试Lookup逻辑服务的功能
 */
@ExtendWith(MockitoExtension.class)
class LookUpLogicServiceImplTest {

    @Mock
    private MetaDataFindService mockMetaDataFindService;

    @Mock
    private User mockUser;

    @Mock
    private IObjectDescribe mockObjectDescribe;

    @Mock
    private IFieldDescribe mockFieldDescribe;

    @Mock
    private MetaDataFindService.QueryContext mockQueryContext;

    @Mock
    private QueryResult<IObjectData> mockQueryResult;

    @InjectMocks
    private LookUpLogicServiceImpl lookUpLogicService;

    @BeforeEach
    void setUp() {
        // 配置基本的Mock行为
        when(mockUser.getTenantId()).thenReturn("testTenant");
        when(mockQueryContext.getUser()).thenReturn(mockUser);
        when(mockObjectDescribe.getApiName()).thenReturn("Account");
        when(mockObjectDescribe.getDisplayName()).thenReturn("客户");
        when(mockFieldDescribe.getApiName()).thenReturn("testField");
        when(mockFieldDescribe.getLabel()).thenReturn("测试字段");
        when(mockFieldDescribe.getDescribeApiName()).thenReturn("Account");
    }

    /**
     * GenerateByAI 测试内容描述：测试handleDataLookUpField的正常场景
     */
    @Test
    @DisplayName("正常场景 - handleDataLookUpField处理成功")
    void testHandleDataLookUpField_Success() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        FieldMapping fieldMapping = new FieldMapping();
        fieldMapping.setObjectApiName("Account");
        fieldMapping.setFieldApiName("testField");
        fieldMapping.setSpecifiedFieldApiName("name");
        fieldMappings.add(fieldMapping);

        List<IObjectData> dataList = new ArrayList<>();
        IObjectData objectData = new ObjectData();
        objectData.set("testField_mapping", "testValue");
        dataList.add(objectData);

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.core.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.core.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.core.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            when(mockObjectDescribe.containsField("testField")).thenReturn(true);

            // 执行测试
            assertDoesNotThrow(() -> {
                lookUpLogicService.handleDataLookUpField(mockUser, mockObjectDescribe, fieldMappings, dataList);
            });
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试handleDataLookUpField时灰度配置关闭的场景
     */
    @Test
    @DisplayName("边界场景 - handleDataLookUpField时灰度配置关闭")
    void testHandleDataLookUpField_GrayConfigDisabled() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        FieldMapping fieldMapping = new FieldMapping();
        fieldMappings.add(fieldMapping);

        List<IObjectData> dataList = new ArrayList<>();

        // Mock UdobjGrayConfig返回false
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(false);

            // 执行测试
            assertDoesNotThrow(() -> {
                lookUpLogicService.processLookUpField(mockQueryContext, mockObjectDescribe, fieldMappings, dataList);
            });

            // 验证没有调用MetaDataFindService
            verify(mockMetaDataFindService, never()).findBySearchQuery(any(), any(), any());
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试processLookUpField时字段映射为空的场景
     */
    @Test
    @DisplayName("边界场景 - processLookUpField时字段映射为空")
    void testProcessLookUpField_EmptyFieldMappings() {
        // 准备测试数据
        List<FieldMapping> emptyFieldMappings = Collections.emptyList();
        List<IObjectData> dataList = new ArrayList<>();

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            // 执行测试
            assertDoesNotThrow(() -> {
                lookUpLogicService.processLookUpField(mockQueryContext, mockObjectDescribe, emptyFieldMappings, dataList);
            });

            // 验证没有调用MetaDataFindService
            verify(mockMetaDataFindService, never()).findBySearchQuery(any(), any(), any());
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试processLookUpField时数据列表为空的场景
     */
    @Test
    @DisplayName("边界场景 - processLookUpField时数据列表为空")
    void testProcessLookUpField_EmptyDataList() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        FieldMapping fieldMapping = new FieldMapping();
        fieldMappings.add(fieldMapping);

        List<IObjectData> emptyDataList = Collections.emptyList();

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            // 执行测试
            assertDoesNotThrow(() -> {
                lookUpLogicService.processLookUpField(mockQueryContext, mockObjectDescribe, fieldMappings, emptyDataList);
            });

            // 验证没有调用MetaDataFindService
            verify(mockMetaDataFindService, never()).findBySearchQuery(any(), any(), any());
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试queryDataBySpecifiedField的正常场景
     */
    @Test
    @DisplayName("正常场景 - queryDataBySpecifiedField查询成功")
    void testQueryDataBySpecifiedField_Success() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        FieldMapping fieldMapping = new FieldMapping();
        fieldMapping.setObjectApiName("Account");
        fieldMapping.setFieldApiName("id");
        fieldMapping.setSpecifiedFieldApiName("name");
        fieldMappings.add(fieldMapping);

        List<IObjectData> dataList = new ArrayList<>();
        IObjectData objectData = new ObjectData();
        objectData.set("id_mapping", "testValue");
        dataList.add(objectData);

        List<IObjectData> queryResultData = new ArrayList<>();
        IObjectData resultData = new ObjectData();
        resultData.set("id", "123");
        resultData.set("name", "testValue");
        queryResultData.add(resultData);

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            when(mockQueryResult.getData()).thenReturn(queryResultData);
            when(mockMetaDataFindService.findBySearchQuery(any(), eq("Account"), any())).thenReturn(mockQueryResult);

            // 执行测试
            List<IObjectData> result = lookUpLogicService.queryDataBySpecifiedField(
                    mockQueryContext, mockObjectDescribe, fieldMappings, dataList);

            // 验证结果
            assertNotNull(result);
            // 注意：由于复杂的内部逻辑，这里主要验证方法不抛异常
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试queryDataBySpecifiedField时灰度配置关闭的场景
     */
    @Test
    @DisplayName("边界场景 - queryDataBySpecifiedField时灰度配置关闭")
    void testQueryDataBySpecifiedField_GrayConfigDisabled() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        List<IObjectData> dataList = new ArrayList<>();

        // Mock UdobjGrayConfig返回false
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(false);

            // 执行测试
            List<IObjectData> result = lookUpLogicService.queryDataBySpecifiedField(
                    mockQueryContext, mockObjectDescribe, fieldMappings, dataList);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试queryDataBySpecifiedField时字段映射为空的场景
     */
    @Test
    @DisplayName("边界场景 - queryDataBySpecifiedField时字段映射为空")
    void testQueryDataBySpecifiedField_EmptyFieldMappings() {
        // 准备测试数据
        List<FieldMapping> emptyFieldMappings = Collections.emptyList();
        List<IObjectData> dataList = new ArrayList<>();

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            // 执行测试
            List<IObjectData> result = lookUpLogicService.queryDataBySpecifiedField(
                    mockQueryContext, mockObjectDescribe, emptyFieldMappings, dataList);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试queryDataBySpecifiedField时数据列表为空的场景
     */
    @Test
    @DisplayName("边界场景 - queryDataBySpecifiedField时数据列表为空")
    void testQueryDataBySpecifiedField_EmptyDataList() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        FieldMapping fieldMapping = new FieldMapping();
        fieldMappings.add(fieldMapping);

        List<IObjectData> emptyDataList = Collections.emptyList();

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            // 执行测试
            List<IObjectData> result = lookUpLogicService.queryDataBySpecifiedField(
                    mockQueryContext, mockObjectDescribe, fieldMappings, emptyDataList);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试containIdFieldMapping的正常场景
     */
    @Test
    @DisplayName("正常场景 - containIdFieldMapping包含ID字段映射")
    void testContainIdFieldMapping_ContainsId() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        FieldMapping fieldMapping = new FieldMapping();
        fieldMapping.setObjectApiName("Account");
        fieldMapping.setFieldApiName("id");
        fieldMappings.add(fieldMapping);

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            // 执行测试
            boolean result = lookUpLogicService.containIdFieldMapping(mockUser, mockObjectDescribe, fieldMappings);

            // 验证结果
            assertTrue(result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试containIdFieldMapping时不包含ID字段映射的场景
     */
    @Test
    @DisplayName("边界场景 - containIdFieldMapping不包含ID字段映射")
    void testContainIdFieldMapping_NotContainsId() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        FieldMapping fieldMapping = new FieldMapping();
        fieldMapping.setObjectApiName("Account");
        fieldMapping.setFieldApiName("name"); // 不是id字段
        fieldMappings.add(fieldMapping);

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            // 执行测试
            boolean result = lookUpLogicService.containIdFieldMapping(mockUser, mockObjectDescribe, fieldMappings);

            // 验证结果
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试containIdFieldMapping时灰度配置关闭的场景
     */
    @Test
    @DisplayName("边界场景 - containIdFieldMapping时灰度配置关闭")
    void testContainIdFieldMapping_GrayConfigDisabled() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();

        // Mock UdobjGrayConfig返回false
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(false);

            // 执行测试
            boolean result = lookUpLogicService.containIdFieldMapping(mockUser, mockObjectDescribe, fieldMappings);

            // 验证结果
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试containIdFieldMapping时字段映射为空的场景
     */
    @Test
    @DisplayName("边界场景 - containIdFieldMapping时字段映射为空")
    void testContainIdFieldMapping_EmptyFieldMappings() {
        // 准备测试数据
        List<FieldMapping> emptyFieldMappings = Collections.emptyList();

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            // 执行测试
            boolean result = lookUpLogicService.containIdFieldMapping(mockUser, mockObjectDescribe, emptyFieldMappings);

            // 验证结果
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试containIdFieldMapping时字段映射为null的场景
     */
    @Test
    @DisplayName("边界场景 - containIdFieldMapping时字段映射为null")
    void testContainIdFieldMapping_NullFieldMappings() {
        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            // 执行测试
            boolean result = lookUpLogicService.containIdFieldMapping(mockUser, mockObjectDescribe, null);

            // 验证结果
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试containLookUpFieldMapping的正常场景
     */
    @Test
    @DisplayName("正常场景 - containLookUpFieldMapping包含Lookup字段映射")
    void testContainLookUpFieldMapping_ContainsLookup() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        FieldMapping fieldMapping = new FieldMapping();
        fieldMapping.setObjectApiName("Account");
        fieldMapping.setFieldApiName("name"); // 非id字段
        fieldMappings.add(fieldMapping);

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            when(mockObjectDescribe.containsField("name")).thenReturn(true);

            // 执行测试
            boolean result = lookUpLogicService.containLookUpFieldMapping(mockUser, mockObjectDescribe, fieldMappings);

            // 验证结果
            assertTrue(result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试containLookUpFieldMapping时不包含Lookup字段映射的场景
     */
    @Test
    @DisplayName("边界场景 - containLookUpFieldMapping不包含Lookup字段映射")
    void testContainLookUpFieldMapping_NotContainsLookup() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        FieldMapping fieldMapping = new FieldMapping();
        fieldMapping.setObjectApiName("Account");
        fieldMapping.setFieldApiName("id"); // id字段，不是lookup字段
        fieldMappings.add(fieldMapping);

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            // 执行测试
            boolean result = lookUpLogicService.containLookUpFieldMapping(mockUser, mockObjectDescribe, fieldMappings);

            // 验证结果
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试containLookUpFieldMapping时灰度配置关闭的场景
     */
    @Test
    @DisplayName("边界场景 - containLookUpFieldMapping时灰度配置关闭")
    void testContainLookUpFieldMapping_GrayConfigDisabled() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();

        // Mock UdobjGrayConfig返回false
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(false);

            // 执行测试
            boolean result = lookUpLogicService.containLookUpFieldMapping(mockUser, mockObjectDescribe, fieldMappings);

            // 验证结果
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试containLookUpFieldMapping时字段映射为空的场景
     */
    @Test
    @DisplayName("边界场景 - containLookUpFieldMapping时字段映射为空")
    void testContainLookUpFieldMapping_EmptyFieldMappings() {
        // 准备测试数据
        List<FieldMapping> emptyFieldMappings = Collections.emptyList();

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            // 执行测试
            boolean result = lookUpLogicService.containLookUpFieldMapping(mockUser, mockObjectDescribe, emptyFieldMappings);

            // 验证结果
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试containLookUpFieldMapping时字段映射为null的场景
     */
    @Test
    @DisplayName("边界场景 - containLookUpFieldMapping时字段映射为null")
    void testContainLookUpFieldMapping_NullFieldMappings() {
        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            // 执行测试
            boolean result = lookUpLogicService.containLookUpFieldMapping(mockUser, mockObjectDescribe, null);

            // 验证结果
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试containLookUpFieldMapping时对象不包含字段的场景
     */
    @Test
    @DisplayName("边界场景 - containLookUpFieldMapping时对象不包含字段")
    void testContainLookUpFieldMapping_ObjectNotContainsField() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        FieldMapping fieldMapping = new FieldMapping();
        fieldMapping.setObjectApiName("Account");
        fieldMapping.setFieldApiName("nonExistentField");
        fieldMappings.add(fieldMapping);

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            when(mockObjectDescribe.containsField("nonExistentField")).thenReturn(false);

            // 执行测试
            boolean result = lookUpLogicService.containLookUpFieldMapping(mockUser, mockObjectDescribe, fieldMappings);

            // 验证结果
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试复杂场景 - 多个字段映射的混合情况
     */
    @Test
    @DisplayName("复杂场景 - 多个字段映射的混合情况")
    void testComplexScenario_MultipleFieldMappings() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();

        // ID字段映射
        FieldMapping idFieldMapping = new FieldMapping();
        idFieldMapping.setObjectApiName("Account");
        idFieldMapping.setFieldApiName("id");
        fieldMappings.add(idFieldMapping);

        // 普通字段映射
        FieldMapping normalFieldMapping = new FieldMapping();
        normalFieldMapping.setObjectApiName("Account");
        normalFieldMapping.setFieldApiName("name");
        fieldMappings.add(normalFieldMapping);

        // 不匹配的对象字段映射
        FieldMapping otherObjectFieldMapping = new FieldMapping();
        otherObjectFieldMapping.setObjectApiName("Contact");
        otherObjectFieldMapping.setFieldApiName("email");
        fieldMappings.add(otherObjectFieldMapping);

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            when(mockObjectDescribe.containsField("name")).thenReturn(true);

            // 测试containIdFieldMapping
            boolean containsId = lookUpLogicService.containIdFieldMapping(mockUser, mockObjectDescribe, fieldMappings);
            assertTrue(containsId);

            // 测试containLookUpFieldMapping
            boolean containsLookup = lookUpLogicService.containLookUpFieldMapping(mockUser, mockObjectDescribe, fieldMappings);
            assertTrue(containsLookup);
        }
    }

    /**
     * GenerateByAI 测试内容描述：测试边界场景 - 用户为null的情况
     */
    @Test
    @DisplayName("异常场景 - 用户为null时的处理")
    void testNullUser_Handling() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        FieldMapping fieldMapping = new FieldMapping();
        fieldMappings.add(fieldMapping);

        // 执行测试并验证异常处理
        assertThrows(Exception.class, () -> {
            lookUpLogicService.containIdFieldMapping(null, mockObjectDescribe, fieldMappings);
        });

        assertThrows(Exception.class, () -> {
            lookUpLogicService.containLookUpFieldMapping(null, mockObjectDescribe, fieldMappings);
        });
    }

    /**
     * GenerateByAI 测试内容描述：测试边界场景 - 对象描述为null的情况
     */
    @Test
    @DisplayName("异常场景 - 对象描述为null时的处理")
    void testNullObjectDescribe_Handling() {
        // 准备测试数据
        List<FieldMapping> fieldMappings = new ArrayList<>();
        FieldMapping fieldMapping = new FieldMapping();
        fieldMapping.setObjectApiName("Account");
        fieldMapping.setFieldApiName("id");
        fieldMappings.add(fieldMapping);

        // Mock UdobjGrayConfig
        try (MockedStatic<com.facishare.paas.appframework.metadata.util.UdobjGrayConfig> mockedGrayConfig
                = mockStatic(com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.class)) {
            mockedGrayConfig.when(()
                    -> com.facishare.paas.appframework.metadata.util.UdobjGrayConfig.isAllow("grayLookUpFieldMapping", "testTenant"))
                    .thenReturn(true);

            // 执行测试并验证异常处理
            assertThrows(Exception.class, () -> {
                lookUpLogicService.containIdFieldMapping(mockUser, null, fieldMappings);
            });

            assertThrows(Exception.class, () -> {
                lookUpLogicService.containLookUpFieldMapping(mockUser, null, fieldMappings);
            });
        }
    }
}
