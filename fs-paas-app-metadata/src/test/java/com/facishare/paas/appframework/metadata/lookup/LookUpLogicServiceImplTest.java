package com.facishare.paas.appframework.metadata.lookup;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.FieldMapping;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LookUpLogicServiceImplTest {

    @Mock
    private DescribeLogicService describeLogicService;

    @Mock
    private MetaDataFindService metaDataFindService;

    @InjectMocks
    private LookUpLogicServiceImpl lookUpLogicService;

    private User testUser;
    private IObjectDescribe mockObjectDescribe;
    private IFieldDescribe mockFieldDescribe;
    private FieldMapping mockFieldMapping;
    private List<IObjectData> testDataList;
    private QueryResult<IObjectData> mockQueryResult;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setTenantId("74255");
        testUser.setUserId("testUserId");

        mockObjectDescribe = mock(IObjectDescribe.class);
        when(mockObjectDescribe.getApiName()).thenReturn("TestObj");
        when(mockObjectDescribe.getDisplayName()).thenReturn("测试对象");
        when(mockObjectDescribe.containsField(anyString())).thenReturn(true);

        mockFieldDescribe = mock(IFieldDescribe.class);
        when(mockFieldDescribe.getApiName()).thenReturn("testField");
        when(mockFieldDescribe.getLabel()).thenReturn("测试字段");
        when(mockFieldDescribe.getDescribeApiName()).thenReturn("TestObj");

        mockFieldMapping = new FieldMapping();
        mockFieldMapping.setObjectApiName("TestObj");
        mockFieldMapping.setFieldApiName("testField");
        mockFieldMapping.setSpecifiedFieldApiName("specifiedField");

        IObjectData testData = new ObjectData();
        testData.setId("testDataId");
        testData.set("testField_mapping", "testValue");
        testDataList = Lists.newArrayList(testData);

        mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(testDataList);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理数据查找字段的正常场景，当灰度配置关闭时应该直接返回
     */
    @Test
    @DisplayName("正常场景 - 灰度配置关闭时直接返回")
    void testHandleDataLookUpField_GrayConfigDisabled() {
        try (MockedStatic<UdobjGrayConfig> mockedStatic = mockStatic(UdobjGrayConfig.class)) {
            mockedStatic.when(() -> UdobjGrayConfig.isAllow("grayLookUpFieldMapping", testUser.getTenantId()))
                    .thenReturn(false);

            List<FieldMapping> fieldMappings = Lists.newArrayList(mockFieldMapping);

            // 执行被测试方法
            lookUpLogicService.handleDataLookUpField(testUser, mockObjectDescribe, fieldMappings, testDataList);

            // 验证结果 - 由于灰度配置关闭，不应该调用其他服务
            verify(describeLogicService, never()).findObjectsWithoutCopy(anyString(), anySet());
            verify(metaDataFindService, never()).findBySearchQuery(any(), anyString(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理数据查找字段时，当字段映射为空时应该直接返回
     */
    @Test
    @DisplayName("正常场景 - 字段映射为空时直接返回")
    void testHandleDataLookUpField_EmptyFieldMappings() {
        try (MockedStatic<UdobjGrayConfig> mockedStatic = mockStatic(UdobjGrayConfig.class)) {
            mockedStatic.when(() -> UdobjGrayConfig.isAllow("grayLookUpFieldMapping", testUser.getTenantId()))
                    .thenReturn(true);

            List<FieldMapping> emptyFieldMappings = Lists.newArrayList();

            // 执行被测试方法
            lookUpLogicService.handleDataLookUpField(testUser, mockObjectDescribe, emptyFieldMappings, testDataList);

            // 验证结果
            verify(describeLogicService, never()).findObjectsWithoutCopy(anyString(), anySet());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理数据查找字段时，当数据列表为空时应该直接返回
     */
    @Test
    @DisplayName("正常场景 - 数据列表为空时直接返回")
    void testHandleDataLookUpField_EmptyDataList() {
        try (MockedStatic<UdobjGrayConfig> mockedStatic = mockStatic(UdobjGrayConfig.class)) {
            mockedStatic.when(() -> UdobjGrayConfig.isAllow("grayLookUpFieldMapping", testUser.getTenantId()))
                    .thenReturn(true);

            List<FieldMapping> fieldMappings = Lists.newArrayList(mockFieldMapping);
            List<IObjectData> emptyDataList = Lists.newArrayList();

            // 执行被测试方法
            lookUpLogicService.handleDataLookUpField(testUser, mockObjectDescribe, fieldMappings, emptyDataList);

            // 验证结果
            verify(describeLogicService, never()).findObjectsWithoutCopy(anyString(), anySet());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据指定字段查询数据的正常场景
     */
    @Test
    @DisplayName("正常场景 - 根据指定字段查询数据成功")
    void testQueryDataBySpecifiedField_Success() {
        try (MockedStatic<UdobjGrayConfig> mockedStatic = mockStatic(UdobjGrayConfig.class)) {
            mockedStatic.when(() -> UdobjGrayConfig.isAllow("grayLookUpFieldMapping", testUser.getTenantId()))
                    .thenReturn(true);

            // 准备测试数据
            FieldMapping idFieldMapping = new FieldMapping();
            idFieldMapping.setObjectApiName("TestObj");
            idFieldMapping.setFieldApiName("_id");
            idFieldMapping.setSpecifiedFieldApiName("specifiedField");

            List<FieldMapping> fieldMappings = Lists.newArrayList(idFieldMapping);
            MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                    .user(testUser)
                    .build();

            when(metaDataFindService.findBySearchQuery(eq(queryContext), eq("TestObj"), any(SearchTemplateQuery.class)))
                    .thenReturn(mockQueryResult);

            // 执行被测试方法
            List<IObjectData> result = lookUpLogicService.queryDataBySpecifiedField(
                    queryContext, mockObjectDescribe, fieldMappings, testDataList);

            // 验证结果
            assertNotNull(result);
            verify(metaDataFindService).findBySearchQuery(eq(queryContext), eq("TestObj"), any(SearchTemplateQuery.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据指定字段查询数据时，灰度配置关闭应该返回空列表
     */
    @Test
    @DisplayName("正常场景 - 灰度配置关闭时返回空列表")
    void testQueryDataBySpecifiedField_GrayConfigDisabled() {
        try (MockedStatic<UdobjGrayConfig> mockedStatic = mockStatic(UdobjGrayConfig.class)) {
            mockedStatic.when(() -> UdobjGrayConfig.isAllow("grayLookUpFieldMapping", testUser.getTenantId()))
                    .thenReturn(false);

            List<FieldMapping> fieldMappings = Lists.newArrayList(mockFieldMapping);
            MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder()
                    .user(testUser)
                    .build();

            // 执行被测试方法
            List<IObjectData> result = lookUpLogicService.queryDataBySpecifiedField(
                    queryContext, mockObjectDescribe, fieldMappings, testDataList);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(metaDataFindService, never()).findBySearchQuery(any(), anyString(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否包含ID字段映射的正常场景
     */
    @Test
    @DisplayName("正常场景 - 包含ID字段映射时返回true")
    void testContainIdFieldMapping_ContainsIdField() {
        try (MockedStatic<UdobjGrayConfig> mockedStatic = mockStatic(UdobjGrayConfig.class)) {
            mockedStatic.when(() -> UdobjGrayConfig.isAllow("grayLookUpFieldMapping", testUser.getTenantId()))
                    .thenReturn(true);

            // 准备测试数据
            FieldMapping idFieldMapping = new FieldMapping();
            idFieldMapping.setObjectApiName("TestObj");
            idFieldMapping.setFieldApiName("_id");

            List<FieldMapping> fieldMappings = Lists.newArrayList(idFieldMapping);

            // 执行被测试方法
            boolean result = lookUpLogicService.containIdFieldMapping(testUser, mockObjectDescribe, fieldMappings);

            // 验证结果
            assertTrue(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否包含ID字段映射时，不包含ID字段应该返回false
     */
    @Test
    @DisplayName("正常场景 - 不包含ID字段映射时返回false")
    void testContainIdFieldMapping_NotContainsIdField() {
        try (MockedStatic<UdobjGrayConfig> mockedStatic = mockStatic(UdobjGrayConfig.class)) {
            mockedStatic.when(() -> UdobjGrayConfig.isAllow("grayLookUpFieldMapping", testUser.getTenantId()))
                    .thenReturn(true);

            List<FieldMapping> fieldMappings = Lists.newArrayList(mockFieldMapping);

            // 执行被测试方法
            boolean result = lookUpLogicService.containIdFieldMapping(testUser, mockObjectDescribe, fieldMappings);

            // 验证结果
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否包含查找字段映射的正常场景
     */
    @Test
    @DisplayName("正常场景 - 包含查找字段映射时返回true")
    void testContainLookUpFieldMapping_ContainsLookUpField() {
        try (MockedStatic<UdobjGrayConfig> mockedStatic = mockStatic(UdobjGrayConfig.class)) {
            mockedStatic.when(() -> UdobjGrayConfig.isAllow("grayLookUpFieldMapping", testUser.getTenantId()))
                    .thenReturn(true);

            List<FieldMapping> fieldMappings = Lists.newArrayList(mockFieldMapping);

            // 执行被测试方法
            boolean result = lookUpLogicService.containLookUpFieldMapping(testUser, mockObjectDescribe, fieldMappings);

            // 验证结果
            assertTrue(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否包含查找字段映射时，灰度配置关闭应该返回false
     */
    @Test
    @DisplayName("正常场景 - 灰度配置关闭时返回false")
    void testContainLookUpFieldMapping_GrayConfigDisabled() {
        try (MockedStatic<UdobjGrayConfig> mockedStatic = mockStatic(UdobjGrayConfig.class)) {
            mockedStatic.when(() -> UdobjGrayConfig.isAllow("grayLookUpFieldMapping", testUser.getTenantId()))
                    .thenReturn(false);

            List<FieldMapping> fieldMappings = Lists.newArrayList(mockFieldMapping);

            // 执行被测试方法
            boolean result = lookUpLogicService.containLookUpFieldMapping(testUser, mockObjectDescribe, fieldMappings);

            // 验证结果
            assertFalse(result);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否包含查找字段映射时，字段映射为空应该返回false
     */
    @Test
    @DisplayName("正常场景 - 字段映射为空时返回false")
    void testContainLookUpFieldMapping_EmptyFieldMappings() {
        try (MockedStatic<UdobjGrayConfig> mockedStatic = mockStatic(UdobjGrayConfig.class)) {
            mockedStatic.when(() -> UdobjGrayConfig.isAllow("grayLookUpFieldMapping", testUser.getTenantId()))
                    .thenReturn(true);

            List<FieldMapping> emptyFieldMappings = Lists.newArrayList();

            // 执行被测试方法
            boolean result = lookUpLogicService.containLookUpFieldMapping(testUser, mockObjectDescribe, emptyFieldMappings);

            // 验证结果
            assertFalse(result);
        }
    }
}
