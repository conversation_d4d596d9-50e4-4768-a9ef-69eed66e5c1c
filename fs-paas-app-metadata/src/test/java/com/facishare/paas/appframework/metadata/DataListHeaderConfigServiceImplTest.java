package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.facishare.paas.appframework.metadata.dto.scene.SceneExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.describe.IFieldListConfig;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IFieldListConfigService;
import com.facishare.paas.metadata.common.MetadataContext;
import com.facishare.paas.metadata.exception.ErrorCode;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.FieldListConfig;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.api.RLock;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DataListHeaderConfigServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DataListHeaderConfigServiceImplTest {

  @InjectMocks
  private DataListHeaderConfigServiceImpl dataListHeaderConfigService;

  @Mock
  private IFieldListConfigService fieldListConfigService;

  @Mock
  private DescribeLogicService describeLogicService;

  @Mock
  private RedissonService redissonService;

  @Mock
  private User user;

  @Mock
  private IFieldListConfig fieldListConfig;

  @Mock
  private IObjectDescribe objectDescribe;

  @Mock
  private ILayout layout;

  @Mock
  private RLock rLock;

  @Mock
  private MetadataContext metadataContext;

  @Mock
  private MetadataContextExt metadataContextExt;

  private static final String TENANT_ID = "test_tenant";
  private static final String USER_ID = "test_user";
  private static final String OBJECT_API_NAME = "test_object";
  private static final String EXTEND_ATTRIBUTE = "test_extend";

  @BeforeEach
  void setUp() {
    when(user.getTenantId()).thenReturn(TENANT_ID);
    when(user.getUserId()).thenReturn(USER_ID);
    when(user.isOutUser()).thenReturn(false);
    when(user.getUserIdOrOutUserIdIfOutUser()).thenReturn(USER_ID);
  }

  @Test
  @SneakyThrows
  @DisplayName("测试findFieldListConfig - 正常情况")
  void testFindFieldListConfig_Success() {
    // 准备测试数据
    List<Map<String, Object>> expectedFieldList = Lists.newArrayList();
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("fieldName", "test_field");
    fieldMap.put("width", 100);
    expectedFieldList.add(fieldMap);

    when(fieldListConfig.getFieldList()).thenReturn(expectedFieldList);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenReturn(fieldListConfig);

      // 执行测试
      List<Map<String, Object>> result = dataListHeaderConfigService.findFieldListConfig(
          user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果
      assertNotNull(result);
      assertEquals(1, result.size());
      assertEquals("test_field", result.get(0).get("fieldName"));
      assertEquals(100, result.get(0).get("width"));

      // 验证方法调用
      verify(fieldListConfigService).findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext);
    }
  }

  @Test
  @SneakyThrows
  @DisplayName("测试findFieldListConfig - fieldListConfig为null")
  void testFindFieldListConfig_NullConfig() {
    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenReturn(null);

      // 执行测试
      List<Map<String, Object>> result = dataListHeaderConfigService.findFieldListConfig(
          user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果
      assertNotNull(result);
      assertTrue(result.isEmpty());
    }
  }

  @Test
  @SneakyThrows
  @DisplayName("测试findFieldListConfig - fieldList为null")
  void testFindFieldListConfig_NullFieldList() {
    when(fieldListConfig.getFieldList()).thenReturn(null);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenReturn(fieldListConfig);

      // 执行测试
      List<Map<String, Object>> result = dataListHeaderConfigService.findFieldListConfig(
          user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果
      assertNotNull(result);
      assertTrue(result.isEmpty());
    }
  }

  @SneakyThrows
  @Test
  @DisplayName("测试findFieldListConfig - 抛出MetadataServiceException")
  void testFindFieldListConfig_MetadataServiceException() {
    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenThrow(new MetadataServiceException(ErrorCode.PARA_BLANK, "test error"));

      // 执行测试并验证异常
      MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () ->
          dataListHeaderConfigService.findFieldListConfig(user, OBJECT_API_NAME, EXTEND_ATTRIBUTE));

      assertEquals("test error", exception.getMessage());
    }
  }

  @SneakyThrows
  @Test
  @DisplayName("测试findFieldWidthConfig - 正常情况")
  void testFindFieldWidthConfig_Success() {
    // 准备测试数据
    List<Map<String, Object>> fieldWidth = Lists.newArrayList();
    Map<String, Object> widthMap = Maps.newHashMap();
    widthMap.put("field1", 150);
    fieldWidth.add(widthMap);

    when(fieldListConfig.getFieldWidth()).thenReturn(fieldWidth);
    when(fieldListConfig.getFieldList()).thenReturn(null);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenReturn(fieldListConfig);

      // 执行测试
      List<Tuple<String, Number>> result = dataListHeaderConfigService.findFieldWidthConfig(
          user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果
      assertNotNull(result);
      assertEquals(1, result.size());
      assertEquals("field1", result.get(0).getKey());
      assertEquals(150, result.get(0).getValue());
    }
  }

  @SneakyThrows
  @Test
  @DisplayName("测试findFieldWidthConfig - 使用fieldList的历史值")
  void testFindFieldWidthConfig_FromFieldList() {
    // 准备测试数据
    List<Map<String, Object>> fieldList = Lists.newArrayList();
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("field1", true);  // Boolean 类型的值用于创建 tuple
    fieldMap.put("width", 200);    // Number 类型的值用于宽度
    fieldList.add(fieldMap);

    when(fieldListConfig.getFieldWidth()).thenReturn(null);
    when(fieldListConfig.getFieldList()).thenReturn(fieldList);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenReturn(fieldListConfig);

      // 执行测试
      List<Tuple<String, Number>> result = dataListHeaderConfigService.findFieldWidthConfig(
          user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果
      assertNotNull(result);
      assertEquals(1, result.size());
      assertEquals("field1", result.get(0).getKey());
      assertEquals(200, result.get(0).getValue());
    }
  }

  @SneakyThrows
  @Test
  @DisplayName("测试findFieldWidthConfig - 配置为null")
  void testFindFieldWidthConfig_NullConfig() {
    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenReturn(null);

      // 执行测试
      List<Tuple<String, Number>> result = dataListHeaderConfigService.findFieldWidthConfig(
          user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果
      assertNotNull(result);
      assertTrue(result.isEmpty());
    }
  }

  @Test
  @SneakyThrows
  @DisplayName("测试findFieldWidthConfig - 抛出MetadataServiceException")
  void testFindFieldWidthConfig_MetadataServiceException() {
    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenThrow(new MetadataServiceException(ErrorCode.PARA_BLANK, "test error"));

      // 执行测试并验证异常
      MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () ->
          dataListHeaderConfigService.findFieldWidthConfig(user, OBJECT_API_NAME, EXTEND_ATTRIBUTE));

      assertEquals("test error", exception.getMessage());
    }
  }

  @Test
  @DisplayName("测试createFieldListConfig - 创建新配置")
  void testCreateFieldListConfig_CreateNew() throws Exception {
    // 准备测试数据
    List<Map<String, Object>> fieldList = Lists.newArrayList();
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("fieldName", "test_field");
    fieldList.add(fieldMap);

    String whatApiName = "what_object";
    CommonFilterField filterFields = new CommonFilterField();
    Boolean convertTopListFilter = true;

    IFieldListConfig createdConfig = new FieldListConfig();

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(redissonService.tryLock(any(User.class), anyString(), anyString())).thenReturn(rLock);
      when(fieldListConfigService.findByDescribeApiNameAndWhatDescribeApiName(
          anyString(), anyString(), anyString(), anyString(), any(MetadataContext.class)))
          .thenReturn(null);
      when(fieldListConfigService.create(any(IFieldListConfig.class), any(MetadataContext.class)))
          .thenReturn(createdConfig);

      // 执行测试
      IFieldListConfig result = dataListHeaderConfigService.createFieldListConfig(
          user, OBJECT_API_NAME, fieldList, whatApiName, EXTEND_ATTRIBUTE, filterFields, convertTopListFilter);

      // 验证结果
      assertNotNull(result);
      assertEquals(createdConfig, result);

      // 验证方法调用
      verify(redissonService).tryLock(any(User.class), anyString(), anyString());
      verify(fieldListConfigService).create(any(IFieldListConfig.class), any(MetadataContext.class));
      verify(redissonService).unlock(rLock);
    }
  }

  @Test
  @DisplayName("测试createFieldListConfig - 更新已存在配置")
  void testCreateFieldListConfig_UpdateExisting() throws Exception {
    // 准备测试数据
    List<Map<String, Object>> fieldList = Lists.newArrayList();
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("fieldName", "test_field");
    fieldList.add(fieldMap);

    String whatApiName = "what_object";
    CommonFilterField filterFields = new CommonFilterField();
    Boolean convertTopListFilter = true;

    IFieldListConfig existingConfig = mock(IFieldListConfig.class);
    IFieldListConfig updatedConfig = new FieldListConfig();

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(redissonService.tryLock(any(User.class), anyString(), anyString())).thenReturn(rLock);
      when(fieldListConfigService.findByDescribeApiNameAndWhatDescribeApiName(
          anyString(), anyString(), anyString(), anyString(), any(MetadataContext.class)))
          .thenReturn(existingConfig);
      when(fieldListConfigService.update(any(IFieldListConfig.class), any(MetadataContext.class)))
          .thenReturn(updatedConfig);

      // 执行测试
      IFieldListConfig result = dataListHeaderConfigService.createFieldListConfig(
          user, OBJECT_API_NAME, fieldList, whatApiName, EXTEND_ATTRIBUTE, filterFields, convertTopListFilter);

      // 验证结果
      assertNotNull(result);
      assertEquals(updatedConfig, result);

      // 验证方法调用
      verify(existingConfig).setFieldList(fieldList);
      verify(existingConfig).setWhatDescribeApiName(whatApiName);
      verify(existingConfig).setConvertTopListFilter(convertTopListFilter);
      verify(fieldListConfigService).update(existingConfig, metadataContext);
      verify(redissonService).unlock(rLock);
    }
  }

  @Test
  @SneakyThrows
  @DisplayName("测试createFieldListConfig - 抛出MetadataServiceException")
  void testCreateFieldListConfig_MetadataServiceException() {
    // 准备测试数据
    List<Map<String, Object>> fieldList = Lists.newArrayList();

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(redissonService.tryLock(any(User.class), anyString(), anyString())).thenReturn(rLock);
      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          anyString(), anyString(), anyString(), anyString(), any(MetadataContext.class)))
          .thenThrow(new MetadataServiceException(ErrorCode.PARA_BLANK, "test error"));

      // 执行测试并验证异常
      MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () ->
          dataListHeaderConfigService.createFieldListConfig(
              user, OBJECT_API_NAME, fieldList, null, EXTEND_ATTRIBUTE, null, null));

      assertEquals("test error", exception.getMessage());
      verify(redissonService).unlock(rLock);
    }
  }

  @Test
  @DisplayName("测试createOrUpdateFieldWidthConfig - 创建新配置")
  void testCreateOrUpdateFieldWidthConfig_CreateNew() throws Exception {
    // 准备测试数据
    List<Map<String, Object>> fieldWidth = Lists.newArrayList();
    Map<String, Object> widthMap = Maps.newHashMap();
    widthMap.put("field1", 150);
    fieldWidth.add(widthMap);

    CommonFilterField filterFields = new CommonFilterField();
    IFieldListConfig createdConfig = new FieldListConfig();

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(redissonService.tryLock(any(User.class), anyString(), anyString())).thenReturn(rLock);
      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          anyString(), anyString(), anyString(), anyString(), any(MetadataContext.class)))
          .thenReturn(null);
      when(fieldListConfigService.create(any(IFieldListConfig.class), any(MetadataContext.class)))
          .thenReturn(createdConfig);

      // 执行测试
      IFieldListConfig result = dataListHeaderConfigService.createOrUpdateFieldWidthConfig(
          user, OBJECT_API_NAME, fieldWidth, EXTEND_ATTRIBUTE, filterFields);

      // 验证结果
      assertNotNull(result);
      assertEquals(createdConfig, result);

      // 验证方法调用
      verify(fieldListConfigService).create(any(IFieldListConfig.class), eq(metadataContext));
      verify(redissonService).unlock(rLock);
    }
  }

  @Test
  @DisplayName("测试createOrUpdateFieldWidthConfig - 更新已存在配置")
  void testCreateOrUpdateFieldWidthConfig_UpdateExisting() throws Exception {
    // 准备测试数据
    List<Map<String, Object>> fieldWidth = Lists.newArrayList();
    Map<String, Object> widthMap = Maps.newHashMap();
    widthMap.put("field1", 150);
    fieldWidth.add(widthMap);

    CommonFilterField filterFields = new CommonFilterField();
    IFieldListConfig existingConfig = mock(IFieldListConfig.class);
    IFieldListConfig updatedConfig = new FieldListConfig();

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(redissonService.tryLock(any(User.class), anyString(), anyString())).thenReturn(rLock);
      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          anyString(), anyString(), anyString(), anyString(), any(MetadataContext.class)))
          .thenReturn(existingConfig);
      when(fieldListConfigService.partialUpdate(any(IFieldListConfig.class), any(Set.class), any(MetadataContext.class)))
          .thenReturn(updatedConfig);

      // 执行测试
      IFieldListConfig result = dataListHeaderConfigService.createOrUpdateFieldWidthConfig(
          user, OBJECT_API_NAME, fieldWidth, EXTEND_ATTRIBUTE, filterFields);

      // 验证结果
      assertNotNull(result);
      assertEquals(updatedConfig, result);

      // 验证方法调用
      verify(existingConfig).setFieldWidth(fieldWidth);
      verify(fieldListConfigService).partialUpdate(eq(existingConfig), any(Set.class), eq(metadataContext));
      verify(redissonService).unlock(rLock);
    }
  }

  @SneakyThrows
  @Test
  @DisplayName("测试findFieldListConfigWithWhatField - 正常情况")
  void testFindFieldListConfigWithWhatField_Success() {
    // 准备测试数据
    String whatApiName = "what_object";
    List<Map<String, Object>> expectedFieldList = Lists.newArrayList();
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("fieldName", "test_field");
    expectedFieldList.add(fieldMap);

    when(fieldListConfig.getFieldList()).thenReturn(expectedFieldList);
    when(fieldListConfigService.findByDescribeApiNameAndWhatDescribeApiName(
        TENANT_ID, USER_ID, OBJECT_API_NAME, whatApiName))
        .thenReturn(fieldListConfig);

    // 执行测试
    List<Map<String, Object>> result = dataListHeaderConfigService.findFieldListConfigWithWhatField(
        user, OBJECT_API_NAME, whatApiName);

    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("test_field", result.get(0).get("fieldName"));

    // 验证方法调用
    verify(fieldListConfigService).findByDescribeApiNameAndWhatDescribeApiName(
        TENANT_ID, USER_ID, OBJECT_API_NAME, whatApiName);
  }

  @Test
  @SneakyThrows
  @DisplayName("测试findFieldListConfigWithWhatField - 配置为null")
  void testFindFieldListConfigWithWhatField_NullConfig() {
    String whatApiName = "what_object";

    when(fieldListConfigService.findByDescribeApiNameAndWhatDescribeApiName(
        TENANT_ID, USER_ID, OBJECT_API_NAME, whatApiName))
        .thenReturn(null);

    // 执行测试
    List<Map<String, Object>> result = dataListHeaderConfigService.findFieldListConfigWithWhatField(
        user, OBJECT_API_NAME, whatApiName);

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  @SneakyThrows
  @DisplayName("测试findFieldListConfigWithWhatField - 抛出MetadataServiceException")
  void testFindFieldListConfigWithWhatField_MetadataServiceException() {
    String whatApiName = "what_object";

    when(fieldListConfigService.findByDescribeApiNameAndWhatDescribeApiName(
        TENANT_ID, USER_ID, OBJECT_API_NAME, whatApiName))
        .thenThrow(new MetadataServiceException(ErrorCode.PARA_BLANK, "test error"));

    // 执行测试并验证异常
    MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () ->
        dataListHeaderConfigService.findFieldListConfigWithWhatField(user, OBJECT_API_NAME, whatApiName));

    assertEquals("test error", exception.getMessage());
  }

  @Test
  @SneakyThrows
  @DisplayName("测试findFieldListConfigWithWhatField带sessionKey - 正常情况")
  void testFindFieldListConfigWithWhatFieldAndSessionKey_Success() {
    // 准备测试数据
    String whatApiName = "what_object";
    String sessionKey = "session123";
    List<Map<String, Object>> expectedFieldList = Lists.newArrayList();
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("fieldName", "test_field");
    expectedFieldList.add(fieldMap);

    when(fieldListConfig.getFieldList()).thenReturn(expectedFieldList);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, whatApiName + "_" + sessionKey, metadataContext))
          .thenReturn(fieldListConfig);

      // 执行测试
      List<Map<String, Object>> result = dataListHeaderConfigService.findFieldListConfigWithWhatField(
          user, OBJECT_API_NAME, whatApiName, sessionKey);

      // 验证结果
      assertNotNull(result);
      assertEquals(1, result.size());
      assertEquals("test_field", result.get(0).get("fieldName"));
    }
  }

  @Test
  @SneakyThrows
  @DisplayName("测试findFieldListConfigWithWhatField带sessionKey - sessionKey为空")
  void testFindFieldListConfigWithWhatFieldAndSessionKey_EmptySessionKey() {
    // 准备测试数据
    String whatApiName = "what_object";
    String sessionKey = "";
    List<Map<String, Object>> expectedFieldList = Lists.newArrayList();
    Map<String, Object> fieldMap = Maps.newHashMap();
    fieldMap.put("fieldName", "test_field");
    expectedFieldList.add(fieldMap);

    when(fieldListConfig.getFieldList()).thenReturn(expectedFieldList);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndWhatDescribeApiName(
          TENANT_ID, USER_ID, OBJECT_API_NAME, whatApiName, metadataContext))
          .thenReturn(fieldListConfig);

      // 执行测试
      List<Map<String, Object>> result = dataListHeaderConfigService.findFieldListConfigWithWhatField(
          user, OBJECT_API_NAME, whatApiName, sessionKey);

      // 验证结果
      assertNotNull(result);
      assertEquals(1, result.size());
      assertEquals("test_field", result.get(0).get("fieldName"));
    }
  }

  @Test
  @SneakyThrows
  @DisplayName("测试convertTopListFilter - 正常情况")
  void testConvertTopListFilter_Success() {
    Boolean expectedValue = true;
    when(fieldListConfig.getConvertTopListFilter()).thenReturn(expectedValue);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenReturn(fieldListConfig);

      // 执行测试
      Boolean result = dataListHeaderConfigService.convertTopListFilter(user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果
      assertEquals(expectedValue, result);
    }
  }

  @Test
  @SneakyThrows
  @DisplayName("测试convertTopListFilter - 配置为null")
  void testConvertTopListFilter_NullConfig() {
    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenReturn(null);

      // 执行测试
      Boolean result = dataListHeaderConfigService.convertTopListFilter(user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果
      assertNull(result);
    }
  }

  @Test
  @SneakyThrows
  @DisplayName("测试convertTopListFilter - 抛出MetadataServiceException")
  void testConvertTopListFilter_MetadataServiceException() {
    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenThrow(new MetadataServiceException(ErrorCode.PARA_BLANK, "test error"));

      // 执行测试并验证异常
      MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () ->
          dataListHeaderConfigService.convertTopListFilter(user, OBJECT_API_NAME, EXTEND_ATTRIBUTE));

      assertEquals("test error", exception.getMessage());
    }
  }

  @SneakyThrows
  @Test
  @DisplayName("测试findFilterFields废弃版本 - 正常情况使用fieldListNew")
  void testFindFilterFieldsDeprecated_Success_WithFieldListNew() {
    // 准备测试数据
    Map<String, Object> filterFieldsMap = Maps.newHashMap();

    Map<String, Object> field1 = Maps.newHashMap();
    field1.put("field_name", "field1");
    field1.put("is_show", true);
    field1.put("enable_quick_filter", false);

    Map<String, Object> field2 = Maps.newHashMap();
    field2.put("field_name", "field2");
    field2.put("is_show", false);
    field2.put("enable_quick_filter", true);

    Map<String, Object> field3 = Maps.newHashMap();
    field3.put("field_name", "field3");
    field3.put("is_show", true);
    field3.put("enable_quick_filter", true);

    filterFieldsMap.put("field_list_new", Lists.newArrayList(field1, field2, field3));

    when(fieldListConfig.getFilterFields()).thenReturn(filterFieldsMap);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          anyString(), anyString(), anyString(), anyString(), any(MetadataContext.class)))
          .thenReturn(fieldListConfig);

      // 执行测试
      List<String> result = dataListHeaderConfigService.findFilterFields(user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果 - 只返回isShow为true的字段
      assertNotNull(result);
      assertEquals(2, result.size());
      assertTrue(result.contains("field1"));
      assertTrue(result.contains("field3"));
      assertFalse(result.contains("field2")); // isShow为false，不应该包含
    }
  }

  @SneakyThrows
  @Test
  @DisplayName("测试findFilterFields废弃版本 - 正常情况使用fieldList")
  void testFindFilterFieldsDeprecated_Success_WithFieldList() {
    // 准备测试数据
    Map<String, Object> filterFieldsMap = Maps.newHashMap();
    filterFieldsMap.put("field_list", Lists.newArrayList("field1", "field2", "field3"));

    when(fieldListConfig.getFilterFields()).thenReturn(filterFieldsMap);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          anyString(), anyString(), anyString(), anyString(), any(MetadataContext.class)))
          .thenReturn(fieldListConfig);

      // 执行测试
      List<String> result = dataListHeaderConfigService.findFilterFields(user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果
      assertNotNull(result);
      assertEquals(3, result.size());
      assertTrue(result.contains("field1"));
      assertTrue(result.contains("field2"));
      assertTrue(result.contains("field3"));
    }
  }

  @SneakyThrows
  @Test
  @DisplayName("测试findFilterFields废弃版本 - fieldListConfig为null")
  void testFindFilterFieldsDeprecated_NullConfig() {
    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          anyString(), anyString(), anyString(), anyString(), any(MetadataContext.class)))
          .thenReturn(null);

      // 执行测试
      List<String> result = dataListHeaderConfigService.findFilterFields(user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果
      assertNotNull(result);
      assertTrue(result.isEmpty());
    }
  }

  @SneakyThrows
  @Test
  @DisplayName("测试findFilterFields废弃版本 - filterFields为空")
  void testFindFilterFieldsDeprecated_EmptyFilterFields() {
    when(fieldListConfig.getFilterFields()).thenReturn(Maps.newHashMap());

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          anyString(), anyString(), anyString(), anyString(), any(MetadataContext.class)))
          .thenReturn(fieldListConfig);

      // 执行测试
      List<String> result = dataListHeaderConfigService.findFilterFields(user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果
      assertNotNull(result);
      assertTrue(result.isEmpty());
    }
  }

  @SneakyThrows
  @Test
  @DisplayName("测试findFilterFields废弃版本 - filterFields为null")
  void testFindFilterFieldsDeprecated_NullFilterFields() {
    when(fieldListConfig.getFilterFields()).thenReturn(null);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          anyString(), anyString(), anyString(), anyString(), any(MetadataContext.class)))
          .thenReturn(fieldListConfig);

      // 执行测试
      List<String> result = dataListHeaderConfigService.findFilterFields(user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证结果
      assertNotNull(result);
      assertTrue(result.isEmpty());
    }
  }

  @SneakyThrows
  @Test
  @DisplayName("测试findFilterFields废弃版本 - extendAttribute为空字符串")
  void testFindFilterFieldsDeprecated_EmptyExtendAttribute() {
    // 准备测试数据
    Map<String, Object> filterFieldsMap = Maps.newHashMap();
    filterFieldsMap.put("field_list", Lists.newArrayList("field1"));

    when(fieldListConfig.getFilterFields()).thenReturn(filterFieldsMap);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          anyString(), anyString(), anyString(), isNull(), any(MetadataContext.class)))
          .thenReturn(fieldListConfig);

      // 执行测试 - 传入空字符串
      List<String> result = dataListHeaderConfigService.findFilterFields(user, OBJECT_API_NAME, "");

      // 验证结果
      assertNotNull(result);
      assertEquals(1, result.size());
      assertEquals("field1", result.get(0));

      // 验证调用时extendAttribute被转换为null
      verify(fieldListConfigService).findByDescribeApiNameAndExtendAttribute(
          eq(TENANT_ID), eq(USER_ID), eq(OBJECT_API_NAME), isNull(), eq(metadataContext));
    }
  }

  @SneakyThrows
  @Test
  @DisplayName("测试findFilterFields废弃版本 - 抛出MetadataServiceException")
  void testFindFilterFieldsDeprecated_MetadataServiceException() {
    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          anyString(), anyString(), anyString(), anyString(), any(MetadataContext.class)))
          .thenThrow(new MetadataServiceException(ErrorCode.PARA_BLANK, "test error"));

      // 执行测试并验证异常
      MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () ->
          dataListHeaderConfigService.findFilterFields(user, OBJECT_API_NAME, EXTEND_ATTRIBUTE));

      assertEquals("test error", exception.getMessage());
    }
  }

  @Test
  @DisplayName("测试findFilterFields新版本 - describe为null")
  void testFindFilterFields_NullDescribe() {
    // 执行测试
    List<CommonFilterField.FilterField> result = dataListHeaderConfigService.findFilterFields(
        user, null, EXTEND_ATTRIBUTE, "what_object", layout);

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  @SneakyThrows
  @DisplayName("测试findFieldWidthConfigs - 正常情况")
  void testFindFieldWidthConfigs_Success() {
    // 准备测试数据
    List<String> objectApiNames = Lists.newArrayList("object1", "object2");
    Map<String, IFieldListConfig> configMap = Maps.newHashMap();

    IFieldListConfig config1 = mock(IFieldListConfig.class);
    List<Map<String, Object>> fieldWidth1 = Lists.newArrayList();
    Map<String, Object> widthMap1 = Maps.newHashMap();
    widthMap1.put("field1", 100);
    fieldWidth1.add(widthMap1);
    when(config1.getFieldWidth()).thenReturn(fieldWidth1);
    when(config1.getFieldList()).thenReturn(null);

    configMap.put("object1", config1);

    when(fieldListConfigService.findByDescribeApiNames(TENANT_ID, USER_ID, objectApiNames))
        .thenReturn(configMap);

    // 执行测试
    Map<String, List<Map>> result = dataListHeaderConfigService.findFieldWidthConfigs(user, objectApiNames);

    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertTrue(result.containsKey("object1"));
    assertEquals(1, result.get("object1").size());
  }

  @Test
  @DisplayName("测试findFieldWidthConfigs - 空列表")
  void testFindFieldWidthConfigs_EmptyList() {
    // 执行测试
    Map<String, List<Map>> result = dataListHeaderConfigService.findFieldWidthConfigs(user, Lists.newArrayList());

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  @SneakyThrows
  @DisplayName("测试findFieldWidthConfigs - 抛出MetadataServiceException")
  void testFindFieldWidthConfigs_MetadataServiceException() {
    List<String> objectApiNames = Lists.newArrayList("object1");

    when(fieldListConfigService.findByDescribeApiNames(TENANT_ID, USER_ID, objectApiNames))
        .thenThrow(new MetadataServiceException(ErrorCode.PARA_BLANK, "test error"));

    // 执行测试并验证异常
    MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () ->
        dataListHeaderConfigService.findFieldWidthConfigs(user, objectApiNames));

    assertEquals("test error", exception.getMessage());
  }

  @Test
  @DisplayName("测试deleteByUserObjApiName - 普通用户")
  void testDeleteByUserObjApiName_NormalUser() throws Exception {
    when(user.isOutUser()).thenReturn(false);

    // 执行测试
    dataListHeaderConfigService.deleteByUserObjApiName(user, OBJECT_API_NAME);

    // 验证方法调用
    verify(fieldListConfigService).deleteByTenantIdAndApiName(TENANT_ID, USER_ID, OBJECT_API_NAME);
  }

  @Test
  @DisplayName("测试deleteByUserObjApiName - 外部用户")
  void testDeleteByUserObjApiName_OutUser() throws Exception {
    String outTenantId = "out_tenant";
    String outUserId = "out_user";

    when(user.isOutUser()).thenReturn(true);
    when(user.getOutTenantId()).thenReturn(outTenantId);
    when(user.getOutUserId()).thenReturn(outUserId);

    // 执行测试
    dataListHeaderConfigService.deleteByUserObjApiName(user, OBJECT_API_NAME);

    // 验证方法调用
    verify(fieldListConfigService).deleteByTenantIdAndApiName40ut(TENANT_ID, outTenantId, outUserId, OBJECT_API_NAME);
  }

  @Test
  @DisplayName("测试deleteByUserObjApiName - 抛出MetadataServiceException")
  void testDeleteByUserObjApiName_MetadataServiceException() throws Exception {
    when(user.isOutUser()).thenReturn(false);
    doThrow(new MetadataServiceException(ErrorCode.PARA_BLANK, "test error"))
        .when(fieldListConfigService).deleteByTenantIdAndApiName(TENANT_ID, USER_ID, OBJECT_API_NAME);

    // 执行测试并验证异常
    MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () ->
        dataListHeaderConfigService.deleteByUserObjApiName(user, OBJECT_API_NAME));

    assertEquals("test error", exception.getMessage());
  }

  @Test
  @DisplayName("测试wipeSettingsByObjApiNameAndUser - 正常情况")
  void testWipeSettingsByObjApiNameAndUser_Success() throws Exception {
    // 准备测试数据
    Set<String> settingNames = Sets.newHashSet("setting1", "setting2");
    IFieldListConfig existingConfig = mock(IFieldListConfig.class);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenReturn(existingConfig);

      // 执行测试
      dataListHeaderConfigService.wipeSettingsByObjApiNameAndUser(
          settingNames, user, OBJECT_API_NAME, EXTEND_ATTRIBUTE);

      // 验证方法调用
      verify(existingConfig, times(2)).set(anyString(), isNull());
      verify(fieldListConfigService).partialUpdate(existingConfig, settingNames, metadataContext);
    }
  }

  @Test
  @SneakyThrows
  @DisplayName("测试wipeSettingsByObjApiNameAndUser - 配置为null")
  void testWipeSettingsByObjApiNameAndUser_NullConfig() {
    Set<String> settingNames = Sets.newHashSet("setting1");

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenReturn(null);

      // 执行测试 - 不应该抛出异常
      assertDoesNotThrow(() -> dataListHeaderConfigService.wipeSettingsByObjApiNameAndUser(
          settingNames, user, OBJECT_API_NAME, EXTEND_ATTRIBUTE));

      // 验证不会调用partialUpdate
      verify(fieldListConfigService, never()).partialUpdate(any(), any(), any());
    }
  }

  @Test
  @SneakyThrows
  @DisplayName("测试wipeSettingsByObjApiNameAndUser - 抛出MetadataServiceException")
  void testWipeSettingsByObjApiNameAndUser_MetadataServiceException() {
    Set<String> settingNames = Sets.newHashSet("setting1");

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndExtendAttribute(
          TENANT_ID, USER_ID, OBJECT_API_NAME, EXTEND_ATTRIBUTE, metadataContext))
          .thenThrow(new MetadataServiceException(ErrorCode.PARA_BLANK, "test error"));

      // 执行测试并验证异常
      MetaDataBusinessException exception = assertThrows(MetaDataBusinessException.class, () ->
          dataListHeaderConfigService.wipeSettingsByObjApiNameAndUser(
              settingNames, user, OBJECT_API_NAME, EXTEND_ATTRIBUTE));

      assertEquals("test error", exception.getMessage());
    }
  }

  @Test
  @DisplayName("测试wipePersonalSettings - 正常情况")
  void testWipePersonalSettings_Success() throws Exception {
    String whatApiName = "what_object";
    IFieldListConfig existingConfig = mock(IFieldListConfig.class);

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndWhatDescribeApiName(
          TENANT_ID, USER_ID, OBJECT_API_NAME, whatApiName, metadataContext))
          .thenReturn(existingConfig);

      // 执行测试
      dataListHeaderConfigService.wipePersonalSettings(user, OBJECT_API_NAME, whatApiName, EXTEND_ATTRIBUTE);

      // 验证方法调用
      verify(existingConfig).setFieldList(null);
      verify(fieldListConfigService).partialUpdate(eq(existingConfig), any(List.class), eq(metadataContext));
    }
  }

  @Test
  @SneakyThrows
  @DisplayName("测试wipePersonalSettings - 配置为null")
  void testWipePersonalSettings_NullConfig() {
    String whatApiName = "what_object";

    try (MockedStatic<SceneExt> sceneExtMock = mockStatic(SceneExt.class)) {
      sceneExtMock.when(() -> SceneExt.getOutTenantInfo(user)).thenReturn(metadataContextExt);
      when(metadataContextExt.getMetadataContext()).thenReturn(metadataContext);

      when(fieldListConfigService.findByDescribeApiNameAndWhatDescribeApiName(
          TENANT_ID, USER_ID, OBJECT_API_NAME, whatApiName, metadataContext))
          .thenReturn(null);

      // 执行测试 - 不应该抛出异常
      assertDoesNotThrow(() -> dataListHeaderConfigService.wipePersonalSettings(
          user, OBJECT_API_NAME, whatApiName, EXTEND_ATTRIBUTE));

      // 验证不会调用partialUpdate
      verify(fieldListConfigService, never()).partialUpdate(any(), any(), any());
    }
  }

  @Test
  @DisplayName("测试getLock方法")
  void testGetLock() {
    when(redissonService.tryLock(eq(user), eq(OBJECT_API_NAME), anyString())).thenReturn(rLock);

    // 执行测试
    RLock result = dataListHeaderConfigService.getLock(user, OBJECT_API_NAME);

    // 验证结果
    assertEquals(rLock, result);
    verify(redissonService).tryLock(eq(user), eq(OBJECT_API_NAME), anyString());
  }

  @Test
  @DisplayName("测试unLock方法")
  void testUnLock() {
    // 执行测试
    dataListHeaderConfigService.unLock(rLock);

    // 验证方法调用
    verify(redissonService).unlock(rLock);
  }
}
